(()=>{var e={};e.id=733,e.ids=[391,733],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},6953:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(60687),s=r(43210),i=r(85814),o=r.n(i),n=r(87979),l=r(91391),d=r(3582),c=r(83475),u=r(77567);function x(){let{user:e,loading:t,isAdmin:r}=(0,n.wC)(),[i,x]=(0,s.useState)([]),[p,m]=(0,s.useState)(!0),[h,g]=(0,s.useState)(""),[f,y]=(0,s.useState)(!1),[b,v]=(0,s.useState)(null),[w,j]=(0,s.useState)(!1),[N,D]=(0,s.useState)({name:"",email:"",mobile:"",referralCode:"",referredBy:"",plan:"",activeDays:0,totalVideos:0,todayVideos:0,wallet:0,status:"active",videoDuration:300}),[C,k]=(0,s.useState)(!1),[S,A]=(0,s.useState)(1),[P,E]=(0,s.useState)(!0),[q,B]=(0,s.useState)(null),[U,M]=(0,s.useState)(!1),[_,T]=(0,s.useState)(7),V=async(e=!0)=>{try{m(!0);let t=await (0,l.lo)(50,e?null:q);e?(x(t.users),A(1)):x(e=>[...e,...t.users]),B(t.lastDoc),E(t.hasMore)}catch(e){console.error("Error loading users:",e),u.A.fire({icon:"error",title:"Error",text:"Failed to load users. Please try again."})}finally{m(!1)}},I=async()=>{if(!h.trim())return void V();try{y(!0);let e=await (0,l.Ki)(h.trim());x(e),E(!1)}catch(e){console.error("Error searching users:",e),u.A.fire({icon:"error",title:"Search Failed",text:"Failed to search users. Please try again."})}finally{y(!1)}},$=e=>{v(e),D({name:e.name,email:e.email,mobile:e.mobile,referralCode:e.referralCode,referredBy:e.referredBy,plan:e.plan,activeDays:e.activeDays,totalVideos:e.totalVideos,todayVideos:e.todayVideos,wallet:e.wallet||0,status:e.status,videoDuration:e.videoDuration||300}),j(!0)},R=async()=>{if(b)try{k(!0);let e=b.plan,t=N.plan,r=e!==t,a={name:N.name,email:N.email,mobile:N.mobile,referralCode:N.referralCode,referredBy:N.referredBy,plan:N.plan,activeDays:N.activeDays,totalVideos:N.totalVideos,todayVideos:N.todayVideos,wallet:N.wallet,status:N.status};if(await (0,l.TK)(b.id,a),N.videoDuration!==(b.videoDuration||300)&&await (0,d.Gl)(b.id,N.videoDuration),r)try{await (0,d.II)(b.id,t),console.log(`Updated plan expiry for user ${b.id}: ${e} -> ${t}`)}catch(e){console.error("Error updating plan expiry:",e)}if(r&&"Trial"===e&&"Trial"!==t)try{console.log(`Processing referral bonus for user ${b.id}: ${e} -> ${t}`),await (0,d.IK)(b.id,e,t),u.A.fire({icon:"success",title:"User Updated & Referral Bonus Processed",html:`
              <div class="text-left">
                <p><strong>User plan updated:</strong> ${e} → ${t}</p>
                <p><strong>Referral bonus:</strong> Processed for referrer (if applicable)</p>
              </div>
            `,timer:4e3,showConfirmButton:!1})}catch(r){console.error("Error processing referral bonus:",r),u.A.fire({icon:"warning",title:"User Updated (Referral Bonus Issue)",html:`
              <div class="text-left">
                <p><strong>User plan updated successfully:</strong> ${e} → ${t}</p>
                <p><strong>Referral bonus:</strong> Could not be processed automatically</p>
                <p class="text-sm text-gray-600 mt-2">Please check referral bonus manually if needed.</p>
              </div>
            `,timer:5e3,showConfirmButton:!1})}else u.A.fire({icon:"success",title:"User Updated",text:"User information has been updated successfully",timer:2e3,showConfirmButton:!1});x(e=>e.map(e=>e.id===b.id?{...e,...a,videoDuration:N.videoDuration}:e)),j(!1),v(null)}catch(e){console.error("Error updating user:",e),u.A.fire({icon:"error",title:"Update Failed",text:"Failed to update user. Please try again."})}finally{k(!1)}},G=async e=>{if((await u.A.fire({icon:"warning",title:"Delete User",text:`Are you sure you want to delete ${e.name}? This action cannot be undone.`,showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{await (0,l.hG)(e.id),x(t=>t.filter(t=>t.id!==e.id)),u.A.fire({icon:"success",title:"User Deleted",text:"User has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting user:",e),u.A.fire({icon:"error",title:"Delete Failed",text:"Failed to delete user. Please try again."})}},Y=async e=>{let{value:t}=await u.A.fire({title:"Grant Quick Video Advantage",html:`
        <p>Grant quick video advantage to <strong>${e.name}</strong></p>
        <p class="text-sm text-gray-600 mb-4">User will get 30-second video duration for the selected period</p>
        <label class="block text-left mb-2">Number of days:</label>
      `,input:"number",inputValue:7,inputAttributes:{min:"1",max:"365",step:"1"},showCancelButton:!0,confirmButtonText:"Grant Advantage",cancelButtonText:"Cancel",inputValidator:e=>{let t=parseInt(e);if(!e||t<1||t>365)return"Please enter a number between 1 and 365"}});if(t)try{await (0,d.w1)(e.id,parseInt(t),e.email||"admin"),await V(),u.A.fire("Success!",`Quick video advantage granted for ${t} days`,"success")}catch(e){console.error("Error granting quick advantage:",e),u.A.fire("Error",e.message||"Failed to grant quick advantage","error")}},O=async e=>{if((await u.A.fire({title:"Remove Quick Video Advantage",text:`Remove quick video advantage from ${e.name}?`,icon:"warning",showCancelButton:!0,confirmButtonColor:"#dc2626",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, remove",cancelButtonText:"Cancel"})).isConfirmed)try{await (0,d.wT)(e.id,e.email||"admin"),await V(),u.A.fire("Removed!","Quick video advantage has been removed.","success")}catch(e){console.error("Error removing quick advantage:",e),u.A.fire("Error",e.message||"Failed to remove quick advantage","error")}},J=e=>null==e||isNaN(e)?"₹0.00":`₹${e.toFixed(2)}`,F=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},L=e=>{switch(e){case"active":return"bg-green-500";case"inactive":return"bg-red-500";case"suspended":return"bg-yellow-500";default:return"bg-gray-500"}};return t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,a.jsxs)(o(),{href:"/admin",className:"text-gray-600 hover:text-gray-800",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(o(),{href:"/admin/upload-users",className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]}),(0,a.jsxs)("button",{onClick:()=>{if(0===i.length)return void u.A.fire({icon:"warning",title:"No Data",text:"No users to export."});let e=(0,c.Fz)(i);(0,c.Bf)(e,"users"),u.A.fire({icon:"success",title:"Export Complete",text:`Exported ${i.length} users to CSV file.`,timer:2e3,showConfirmButton:!1})},className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,a.jsxs)("button",{onClick:()=>V(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,a.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("input",{type:"text",value:h,onChange:e=>g(e.target.value),placeholder:"Search by mobile number...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onKeyPress:e=>"Enter"===e.key&&I()}),(0,a.jsx)("button",{onClick:I,disabled:f,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Searching..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-search mr-2"}),"Search"]})}),h&&(0,a.jsx)("button",{onClick:()=>{g(""),V()},className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:(0,a.jsx)("i",{className:"fas fa-times"})})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Videos"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duration"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quick Advantage"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p&&0===i.length?(0,a.jsx)("tr",{children:(0,a.jsxs)("td",{colSpan:9,className:"px-6 py-4 text-center",children:[(0,a.jsx)("div",{className:"spinner mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading users..."})]})}):0===i.length?(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:9,className:"px-6 py-4 text-center text-gray-500",children:"No users found"})}):i.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Joined: ",e.joinedDate.toLocaleDateString()]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.mobile}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Code: ",e.referralCode]}),e.referredBy&&(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Ref: ",e.referredBy]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-semibold rounded-full text-white ${F(e.plan)}`,children:e.plan}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["Days: ",e.activeDays]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:["Total: ",e.totalVideos]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Today: ",e.todayVideos]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:60>(e.videoDuration||300)?`${e.videoDuration||300}s`:`${Math.round((e.videoDuration||300)/60)}m`}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:60>(e.videoDuration||300)?`${e.videoDuration||300} second${(e.videoDuration||300)>1?"s":""}`:`${Math.round((e.videoDuration||300)/60)} minute${Math.round((e.videoDuration||300)/60)>1?"s":""}`})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.quickVideoAdvantage&&e.quickVideoAdvantageExpiry&&new Date<e.quickVideoAdvantageExpiry?(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-green-500",children:"Active"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Until: ",e.quickVideoAdvantageExpiry.toLocaleDateString()]})]}):(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-gray-500",children:"None"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,a.jsx)("i",{className:"fas fa-wallet mr-1 text-green-500"}),J(e.wallet||0)]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Total Balance"})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-semibold rounded-full text-white ${L(e.status)}`,children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>$(e),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:(0,a.jsx)("i",{className:"fas fa-edit"})}),e.quickVideoAdvantage&&e.quickVideoAdvantageExpiry&&new Date<e.quickVideoAdvantageExpiry?(0,a.jsx)("button",{onClick:()=>O(e),className:"text-orange-600 hover:text-orange-900",title:"Remove Quick Advantage",children:(0,a.jsx)("i",{className:"fas fa-clock"})}):(0,a.jsx)("button",{onClick:()=>Y(e),className:"text-green-600 hover:text-green-900",title:"Grant Quick Advantage",children:(0,a.jsx)("i",{className:"fas fa-bolt"})}),(0,a.jsx)("button",{onClick:()=>G(e),className:"text-red-600 hover:text-red-900",title:"Delete User",children:(0,a.jsx)("i",{className:"fas fa-trash"})})]})})]},e.id))})]})}),P&&!p&&i.length>0&&(0,a.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 text-center",children:(0,a.jsxs)("button",{onClick:()=>{P&&!p&&V(!1)},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:[(0,a.jsx)("i",{className:"fas fa-chevron-down mr-2"}),"Load More Users"]})})]})}),w&&b&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 overflow-y-auto modal-scrollable",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-md my-8 max-h-[calc(100vh-2rem)] flex flex-col shadow-xl",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200 flex-shrink-0",children:(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"Edit User"})}),(0,a.jsxs)("div",{className:"p-6 space-y-4 flex-1 overflow-y-auto modal-scrollable",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,a.jsx)("input",{type:"text",value:N.name,onChange:e=>D(t=>({...t,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,a.jsx)("input",{type:"email",value:N.email,onChange:e=>D(t=>({...t,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mobile"}),(0,a.jsx)("input",{type:"text",value:N.mobile,onChange:e=>D(t=>({...t,mobile:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referral Code"}),(0,a.jsx)("input",{type:"text",value:N.referralCode,onChange:e=>D(t=>({...t,referralCode:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referred By"}),(0,a.jsx)("input",{type:"text",value:N.referredBy,onChange:e=>D(t=>({...t,referredBy:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan"}),(0,a.jsxs)("select",{value:N.plan,onChange:e=>D(t=>({...t,plan:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"Trial",children:"Trial"}),(0,a.jsx)("option",{value:"Starter",children:"Starter"}),(0,a.jsx)("option",{value:"Basic",children:"Basic"}),(0,a.jsx)("option",{value:"Premium",children:"Premium"}),(0,a.jsx)("option",{value:"Gold",children:"Gold"}),(0,a.jsx)("option",{value:"Platinum",children:"Platinum"}),(0,a.jsx)("option",{value:"Diamond",children:"Diamond"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Active Days"}),(0,a.jsx)("input",{type:"number",value:N.activeDays,onChange:e=>D(t=>({...t,activeDays:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Videos"}),(0,a.jsx)("input",{type:"number",value:N.totalVideos,onChange:e=>D(t=>({...t,totalVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Today Videos"}),(0,a.jsx)("input",{type:"number",value:N.todayVideos,onChange:e=>D(t=>({...t,todayVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Wallet Balance (₹)"}),(0,a.jsx)("input",{type:"number",step:"0.01",value:N.wallet,onChange:e=>D(t=>({...t,wallet:parseFloat(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Video Duration"}),(0,a.jsxs)("select",{value:N.videoDuration,onChange:e=>D(t=>({...t,videoDuration:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,a.jsxs)("optgroup",{label:"\uD83D\uDE80 Quick Duration",children:[(0,a.jsx)("option",{value:1,children:"1 second"}),(0,a.jsx)("option",{value:10,children:"10 seconds"}),(0,a.jsx)("option",{value:30,children:"30 seconds"})]}),(0,a.jsxs)("optgroup",{label:"⏱️ Standard Duration",children:[(0,a.jsx)("option",{value:60,children:"1 minute"}),(0,a.jsx)("option",{value:120,children:"2 minutes"}),(0,a.jsx)("option",{value:180,children:"3 minutes"}),(0,a.jsx)("option",{value:240,children:"4 minutes"}),(0,a.jsx)("option",{value:300,children:"5 minutes"}),(0,a.jsx)("option",{value:360,children:"6 minutes"}),(0,a.jsx)("option",{value:420,children:"7 minutes"})]})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:N.videoDuration<60?`${N.videoDuration} second${N.videoDuration>1?"s":""}`:`${Math.round(N.videoDuration/60)} minute${Math.round(N.videoDuration/60)>1?"s":""}`})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:N.status,onChange:e=>D(t=>({...t,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"}),(0,a.jsx)("option",{value:"suspended",children:"Suspended"})]})]})]}),(0,a.jsx)("div",{className:"p-6 border-t border-gray-200 flex-shrink-0",children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("button",{onClick:R,disabled:C,className:"flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:C?"Saving...":"Save Changes"}),(0,a.jsx)("button",{onClick:()=>j(!1),className:"flex-1 bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700",children:"Cancel"})]})})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},25292:(e,t,r)=>{Promise.resolve().then(r.bind(r,6953))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>d,j2:()=>l});var a=r(67989),s=r(63385),i=r(75535),o=r(70146);let n=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,s.xI)(n),d=(0,i.aU)(n);(0,o.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,t,r)=>{"use strict";r.d(t,{M4:()=>n,_f:()=>o});var a=r(33784),s=r(77567);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function o(e,t="/login"){try{if((await s.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await a.j2.signOut(),s.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),s.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function n(e,t="/login"){try{e&&i(e),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},52031:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\users\\page.tsx","default")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60179:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=r(65239),s=r(48088),i=r(88170),o=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,52031)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\users\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\users\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/users/page",pathname:"/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78092:(e,t,r)=>{Promise.resolve().then(r.bind(r,52031))},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,r)=>{"use strict";function a(e,t,r){if(!e||0===e.length)return void alert("No data to export");let a=r||Object.keys(e[0]),s=new Blob([[a.join(","),...e.map(e=>a.map(t=>{let r=e[t];if(null==r)return"";if("string"==typeof r){let e=r.replace(/"/g,'""');return e.includes(",")?`"${e}"`:e}return r instanceof Date?r.toLocaleDateString():String(r)}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(s);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function s(e){return e.map(e=>({Name:e.name,Email:e.email,Mobile:e.mobile,"Referral Code":e.referralCode,"Referred By":e.referredBy||"Direct",Plan:e.plan,"Active Days":e.activeDays,"Total Videos":e.totalVideos,"Today Videos":e.todayVideos,"Video Duration (seconds)":e.videoDuration||300,"Wallet Balance":e.wallet||0,Status:e.status,"Joined Date":e.joinedDate?.toLocaleDateString()||""}))}function i(e){return e.map(e=>({"User ID":e.userId,"User Name":e.userName||"","User Email":e.userEmail||"",Type:e.type,Amount:e.amount,Description:e.description,Status:e.status,Date:e.date?.toLocaleDateString()||""}))}function o(e){return e.map(e=>({"User Name":e.userName,"User Email":e.userEmail,"Mobile Number":e.userMobile||"","User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount,"Account Holder":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":e.bankDetails?.accountNumber||"","IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status,"Request Date":e.requestDate?.toLocaleDateString()||"","Admin Notes":e.adminNotes||""}))}function n(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt?.toLocaleDateString()||"","Sent Date":e.sentAt?.toLocaleDateString()||""}))}r.d(t,{Bf:()=>a,Fz:()=>s,Pe:()=>n,dB:()=>o,sL:()=>i})},87979:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>o,hD:()=>i,wC:()=>n});var a=r(43210);r(63385),r(33784);var s=r(51278);function i(){let[e,t]=(0,a.useState)(null),[r,i]=(0,a.useState)(!0),o=async()=>{try{await (0,s.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:o}}function o(){let{user:e,loading:t}=i();return{user:e,loading:t}}function n(){let{user:e,loading:t}=i(),[r,s]=(0,a.useState)(!1),[o,n]=(0,a.useState)(!0);return{user:e,loading:t||o,isAdmin:r}}},91391:(e,t,r)=>{"use strict";r.d(t,{Ki:()=>d,Pn:()=>n,TK:()=>u,getWithdrawals:()=>c,hG:()=>x,lo:()=>l,updateWithdrawalStatus:()=>p});var a=r(75535),s=r(33784),i=r(3582);let o=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=o.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let r=a.Dc.fromDate(t),n=await (0,a.GG)((0,a.rJ)(s.db,i.I.users)),l=n.size,d=(0,a.P)((0,a.rJ)(s.db,i.I.users),(0,a._M)(i.Yr.joinedDate,">=",r)),c=(await (0,a.GG)(d)).size,u=0,x=0,p=0,m=0;n.forEach(e=>{let r=e.data();u+=r[i.Yr.totalVideos]||0,x+=r[i.Yr.wallet]||0;let a=r[i.Yr.lastVideoDate]?.toDate();a&&a.toDateString()===t.toDateString()&&(p+=r[i.Yr.todayVideos]||0)});try{let e=(0,a.P)((0,a.rJ)(s.db,i.I.transactions),(0,a._M)(i.Yr.type,"==","video_earning"),(0,a.AB)(1e3));(await (0,a.GG)(e)).forEach(e=>{let r=e.data(),a=r[i.Yr.date]?.toDate();a&&a>=t&&(m+=r[i.Yr.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let h=(0,a.P)((0,a.rJ)(s.db,i.I.withdrawals),(0,a._M)("status","==","pending")),g=(await (0,a.GG)(h)).size,f=(0,a.P)((0,a.rJ)(s.db,i.I.withdrawals),(0,a._M)("date",">=",r)),y=(await (0,a.GG)(f)).size,b={totalUsers:l,totalVideos:u,totalEarnings:x,pendingWithdrawals:g,todayUsers:c,todayVideos:p,todayEarnings:m,todayWithdrawals:y};return o.set(e,{data:b,timestamp:Date.now()}),b}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(e=50,t=null){try{let r=(0,a.P)((0,a.rJ)(s.db,i.I.users),(0,a.My)(i.Yr.joinedDate,"desc"),(0,a.AB)(e));t&&(r=(0,a.P)((0,a.rJ)(s.db,i.I.users),(0,a.My)(i.Yr.joinedDate,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let o=await (0,a.GG)(r);return{users:o.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.Yr.joinedDate]?.toDate(),planExpiry:e.data()[i.Yr.planExpiry]?.toDate()})),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{let t=(0,a.P)((0,a.rJ)(s.db,i.I.users),(0,a._M)(i.Yr.mobile,"==",e));return(await (0,a.GG)(t)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.Yr.joinedDate]?.toDate(),planExpiry:e.data()[i.Yr.planExpiry]?.toDate()}))}catch(e){throw console.error("Error searching users by mobile:",e),e}}async function c(e=50,t=null){try{let r=(0,a.P)((0,a.rJ)(s.db,i.I.withdrawals),(0,a.My)("date","desc"),(0,a.AB)(e));t&&(r=(0,a.P)((0,a.rJ)(s.db,i.I.withdrawals),(0,a.My)("date","desc"),(0,a.HM)(t),(0,a.AB)(e)));let o=await (0,a.GG)(r);return{withdrawals:o.docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()})),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function u(e,t){try{await (0,a.mZ)((0,a.H9)(s.db,i.I.users,e),t),o.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function x(e){try{await (0,a.kd)((0,a.H9)(s.db,i.I.users,e)),o.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function p(e,t,n){try{let l=await (0,a.x7)((0,a.H9)(s.db,i.I.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=l.data(),x={status:t,updatedAt:a.Dc.now()};if(n&&(x.adminNotes=n),await (0,a.mZ)((0,a.H9)(s.db,i.I.withdrawals,e),x),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(r.bind(r,3582));await e(d,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(r.bind(r,3582));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}o.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[204,756,567,441,582],()=>r(60179));module.exports=a})();