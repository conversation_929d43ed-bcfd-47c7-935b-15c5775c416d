// CSV Export Utility Functions

export function downloadCSV(data: any[], filename: string, headers?: string[]) {
  if (!data || data.length === 0) {
    alert('No data to export')
    return
  }

  // Generate headers from first object if not provided
  const csvHeaders = headers || Object.keys(data[0])
  
  // Create CSV content
  const csvContent = [
    // Headers
    csvHeaders.join(','),
    // Data rows
    ...data.map(row => 
      csvHeaders.map(header => {
        const value = row[header]
        // Handle different data types
        if (value === null || value === undefined) return ''
        if (typeof value === 'string') {
          // Escape quotes and wrap in quotes if contains comma
          const escaped = value.replace(/"/g, '""')
          return escaped.includes(',') ? `"${escaped}"` : escaped
        }
        if (value instanceof Date) {
          return value.toLocaleDateString()
        }
        return String(value)
      }).join(',')
    )
  ].join('\n')

  // Create and download file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// Format user data for export
export function formatUsersForExport(users: any[]) {
  return users.map(user => ({
    'Name': user.name,
    'Email': user.email,
    'Mobile': user.mobile,
    'Referral Code': user.referralCode,
    'Referred By': user.referredBy || 'Direct',
    'Plan': user.plan,
    'Active Days': user.activeDays,
    'Total Videos': user.totalVideos,
    'Today Videos': user.todayVideos,
    'Video Duration (seconds)': user.videoDuration || 300,
    'Wallet Balance': user.wallet || 0,
    'Status': user.status,
    'Joined Date': user.joinedDate?.toLocaleDateString() || ''
  }))
}

// Format transactions for export
export function formatTransactionsForExport(transactions: any[]) {
  return transactions.map(transaction => ({
    'User ID': transaction.userId,
    'User Name': transaction.userName || '',
    'User Email': transaction.userEmail || '',
    'Mobile Number': transaction.userMobile || '',
    'User Number': transaction.userNumber || '',
    'Type': transaction.type,
    'Amount': transaction.amount,
    'Description': transaction.description,
    'Status': transaction.status,
    'Date': transaction.date?.toLocaleDateString() || ''
  }))
}

// Format withdrawals for export
export function formatWithdrawalsForExport(withdrawals: any[]) {
  return withdrawals.map(withdrawal => ({
    'User Name': withdrawal.userName,
    'User Email': withdrawal.userEmail,
    'Mobile Number': withdrawal.userMobile || '',
    'User Plan': withdrawal.userPlan || '',
    'Active Days': withdrawal.userActiveDays || 0,
    'Wallet Balance': withdrawal.walletBalance || 0,
    'Withdrawal Amount': withdrawal.amount,
    'Account Holder': withdrawal.bankDetails?.accountHolderName || '',
    'Bank Name': withdrawal.bankDetails?.bankName || '',
    'Account Number': withdrawal.bankDetails?.accountNumber || '',
    'IFSC Code': withdrawal.bankDetails?.ifscCode || '',
    'Status': withdrawal.status,
    'Request Date': withdrawal.requestDate?.toLocaleDateString() || '',
    'Admin Notes': withdrawal.adminNotes || ''
  }))
}

// Format notifications for export
export function formatNotificationsForExport(notifications: any[]) {
  return notifications.map(notification => ({
    'Title': notification.title,
    'Message': notification.message,
    'Type': notification.type,
    'Target': notification.target,
    'Status': notification.status,
    'Created Date': notification.createdAt?.toLocaleDateString() || '',
    'Sent Date': notification.sentAt?.toLocaleDateString() || ''
  }))
}
