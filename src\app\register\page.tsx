'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { useAuthState } from '@/hooks/useAuth'
import { FIELD_NAMES, COLLECTIONS } from '@/lib/dataService'
import Swal from 'sweetalert2'

export default function RegisterPage() {
  const { user, loading } = useAuthState()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    mobile: '',
    password: '',
    confirmPassword: '',
    referralCode: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // Remove automatic redirect - user should only redirect after successful registration
  // useEffect(() => {
  //   if (user && !loading) {
  //     window.location.href = '/dashboard'
  //   }
  // }, [user, loading])

  useEffect(() => {
    // Get referral code from URL if present
    const urlParams = new URLSearchParams(window.location.search)
    const refCode = urlParams.get('ref')
    if (refCode) {
      setFormData(prev => ({ ...prev, referralCode: refCode }))
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Referral code generation is handled by generateUniqueReferralCode function

  const validateForm = () => {
    const { name, email, mobile, password, confirmPassword } = formData

    if (!name || !email || !mobile || !password || !confirmPassword) {
      throw new Error('Please fill in all required fields')
    }

    if (name.length < 2) {
      throw new Error('Name must be at least 2 characters long')
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      throw new Error('Please enter a valid email address')
    }

    if (!/^[6-9]\d{9}$/.test(mobile)) {
      throw new Error('Please enter a valid 10-digit mobile number')
    }

    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters long')
    }

    if (password !== confirmPassword) {
      throw new Error('Passwords do not match')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      validateForm()
      setIsLoading(true)

      // Create user account first - Firebase Auth will handle email uniqueness
      // Mobile number uniqueness will be checked after user creation
      console.log('Creating user with email and password...')
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        formData.email,
        formData.password
      )

      const user = userCredential.user
      console.log('Firebase Auth user created successfully:', user.uid)

      console.log('Generating referral code...')
      // Use a simple, reliable referral code generation for registration
      // Complex sequential generation can be done later by admin if needed
      const timestamp = Date.now().toString().slice(-4)
      const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase()
      const userReferralCode = `MY${timestamp}${randomPart}`
      console.log('Generated referral code:', userReferralCode)

      // Note: Mobile number uniqueness is handled by the admin panel
      // Firebase Auth already ensures email uniqueness

      // Create user document in Firestore with all required fields
      const userData = {
        [FIELD_NAMES.name]: formData.name.trim(),
        [FIELD_NAMES.email]: formData.email.toLowerCase(),
        [FIELD_NAMES.mobile]: formData.mobile,
        [FIELD_NAMES.referralCode]: userReferralCode,
        [FIELD_NAMES.referredBy]: formData.referralCode || '',
        [FIELD_NAMES.referralBonusCredited]: false, // Bonus not credited yet
        [FIELD_NAMES.plan]: 'Trial',
        [FIELD_NAMES.planExpiry]: null,
        [FIELD_NAMES.activeDays]: 0, // Will be calculated from plan activation date
        [FIELD_NAMES.joinedDate]: Timestamp.now(),
        [FIELD_NAMES.wallet]: 0, // Single wallet
        [FIELD_NAMES.totalVideos]: 0,
        [FIELD_NAMES.todayVideos]: 0,
        [FIELD_NAMES.lastVideoDate]: null,
        [FIELD_NAMES.videoDuration]: 30, // Default 30 seconds for trial
        status: 'active'
      }

      console.log('Creating user document with data:', userData)
      console.log('User UID:', user.uid)
      console.log('Collection:', COLLECTIONS.users)
      console.log('Document path:', `${COLLECTIONS.users}/${user.uid}`)

      // Create user document in Firestore
      console.log('Creating user document in Firestore...')
      const userDocRef = doc(db, COLLECTIONS.users, user.uid)
      console.log('Document reference created:', userDocRef.path)
      console.log('About to create document with data:', JSON.stringify(userData, null, 2))

      try {
        console.log('Attempting to create document...')
        console.log('User UID:', user.uid)
        console.log('Document path:', userDocRef.path)
        console.log('Auth user email:', user.email)
        console.log('Auth user verified:', user.emailVerified)

        await setDoc(userDocRef, userData)
        console.log('✅ User document created successfully')

        // Verify the document was created
        const verifyDoc = await getDoc(userDocRef)
        if (verifyDoc.exists()) {
          console.log('✅ Document verification successful:', verifyDoc.data())
          console.log('✅ Registration completed successfully - both Auth and Firestore created')
        } else {
          console.error('❌ Document was not created properly')
          throw new Error('User document was not created properly')
        }
      } catch (firestoreError: any) {
        console.error('❌ Firestore setDoc failed:', firestoreError)
        console.error('❌ Firestore error code:', firestoreError.code)
        console.error('❌ Firestore error message:', firestoreError.message)
        console.error('❌ Full error object:', JSON.stringify(firestoreError, null, 2))

        // Firebase Auth succeeded but Firestore failed - user account exists but no profile
        console.error('❌ CRITICAL: Firebase Auth succeeded but Firestore document creation failed')
        console.error('❌ User account exists but profile is incomplete')

        throw new Error(`Failed to create user profile: ${firestoreError.message}. Your account was created but profile setup failed. Please contact support.`)
      }



      // Note: Referral bonus will be credited when admin upgrades user from Trial to paid plan
      console.log('User registered successfully. Referral bonus will be processed when upgraded to paid plan.')

      // Show success message and redirect only after complete registration
      Swal.fire({
        icon: 'success',
        title: 'Registration Successful!',
        text: 'Your account and profile have been created successfully. Welcome to MyTube!',
        timer: 2000,
        showConfirmButton: false
      }).then(() => {
        // Redirect only after both Firebase Auth and Firestore document are created
        console.log('✅ Complete registration successful - redirecting to dashboard...')
        window.location.href = '/dashboard'
      })
    } catch (error: any) {
      console.error('Registration error:', error)
      console.error('Error code:', error.code)
      console.error('Error message:', error.message)
      console.error('Full error object:', JSON.stringify(error, null, 2))

      let message = 'An error occurred during registration'
      
      if (error.message.includes('fill in all')) {
        message = error.message
      } else if (error.message.includes('Name must be')) {
        message = error.message
      } else if (error.message.includes('valid email')) {
        message = error.message
      } else if (error.message.includes('valid 10-digit')) {
        message = error.message
      } else if (error.message.includes('Password must be')) {
        message = error.message
      } else if (error.message.includes('Passwords do not match')) {
        message = error.message
      } else if (error.message.includes('email address is already registered')) {
        message = error.message
      } else if (error.message.includes('mobile number is already registered')) {
        message = error.message
      } else {
        switch (error.code) {
          case 'auth/email-already-in-use':
            message = 'An account with this email already exists'
            break
          case 'auth/invalid-email':
            message = 'Invalid email address'
            break
          case 'auth/weak-password':
            message = 'Password is too weak'
            break
          default:
            message = error.message || 'Registration failed'
        }
      }

      Swal.fire({
        icon: 'error',
        title: 'Registration Failed',
        text: message,
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8">
      <div className="glass-card w-full max-w-md p-8">
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Image
              src="/img/mytube-logo.svg"
              alt="MyTube Logo"
              width={50}
              height={50}
              className="mr-3"
            />
            <span className="text-2xl font-bold text-white">MyTube</span>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">Create Account</h1>
          <p className="text-white/80">Join MyTube and start earning today</p>
        </div>

        {/* Registration Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-white font-medium mb-2">
              Full Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter your full name"
              required
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-white font-medium mb-2">
              Email Address *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter your email"
              required
            />
          </div>

          <div>
            <label htmlFor="mobile" className="block text-white font-medium mb-2">
              Mobile Number *
            </label>
            <input
              type="tel"
              id="mobile"
              name="mobile"
              value={formData.mobile}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter 10-digit mobile number"
              maxLength={10}
              required
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-white font-medium mb-2">
              Password *
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className="form-input pr-12"
                placeholder="Enter password (min 6 characters)"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="password-toggle-btn"
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
              </button>
            </div>
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-white font-medium mb-2">
              Confirm Password *
            </label>
            <div className="relative">
              <input
                type={showConfirmPassword ? "text" : "password"}
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                className="form-input pr-12"
                placeholder="Confirm your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="password-toggle-btn"
                aria-label={showConfirmPassword ? "Hide confirm password" : "Show confirm password"}
              >
                <i className={`fas ${showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
              </button>
            </div>
          </div>

          <div>
            <label htmlFor="referralCode" className="block text-white font-medium mb-2">
              Referral Code (Optional)
            </label>
            <input
              type="text"
              id="referralCode"
              name="referralCode"
              value={formData.referralCode}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter referral code if you have one"
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full btn-primary flex items-center justify-center mt-6"
          >
            {isLoading ? (
              <>
                <div className="spinner mr-2 w-5 h-5"></div>
                Creating Account...
              </>
            ) : (
              <>
                <i className="fas fa-user-plus mr-2"></i>
                Create Account
              </>
            )}
          </button>
        </form>

        {/* Links */}
        <div className="mt-6 text-center">
          <div className="text-white/60">
            Already have an account?{' '}
            <Link
              href="/login"
              className="text-white font-semibold hover:underline"
            >
              Sign in here
            </Link>
          </div>
        </div>

        {/* Back to Home */}
        <div className="mt-8 text-center">
          <Link
            href="/"
            className="text-white/80 hover:text-white transition-colors inline-flex items-center"
          >
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  )
}
