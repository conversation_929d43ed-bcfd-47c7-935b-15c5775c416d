/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/debug-registration-simple/page";
exports.ids = ["app/debug-registration-simple/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdebug-registration-simple%2Fpage&page=%2Fdebug-registration-simple%2Fpage&appPaths=%2Fdebug-registration-simple%2Fpage&pagePath=private-next-app-dir%2Fdebug-registration-simple%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdebug-registration-simple%2Fpage&page=%2Fdebug-registration-simple%2Fpage&appPaths=%2Fdebug-registration-simple%2Fpage&pagePath=private-next-app-dir%2Fdebug-registration-simple%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/debug-registration-simple/page.tsx */ \"(rsc)/./src/app/debug-registration-simple/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'debug-registration-simple',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\"],\n'error': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/debug-registration-simple/page\",\n        pathname: \"/debug-registration-simple\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdebug-registration-simple%2Fpage&page=%2Fdebug-registration-simple%2Fpage&appPaths=%2Fdebug-registration-simple%2Fpage&pagePath=private-next-app-dir%2Fdebug-registration-simple%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(rsc)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(rsc)/./src/components/PWAInstaller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cdebug-registration-simple%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cdebug-registration-simple%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/debug-registration-simple/page.tsx */ \"(rsc)/./src/app/debug-registration-simple/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2RlYnVnLXJlZ2lzdHJhdGlvbi1zaW1wbGUlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQWdKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxkZWJ1Zy1yZWdpc3RyYXRpb24tc2ltcGxlXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cdebug-registration-simple%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/debug-registration-simple/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/debug-registration-simple/page.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-registration-simple\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fe166ce4cd0c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFTVVNcXE9uZURyaXZlXFxEZXNrdG9wXFxNWSBQUk9KRUNUU1xcTm9kZSBNeXR1YmVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZlMTY2Y2U0Y2QwY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PWAInstaller */ \"(rsc)/./src/components/PWAInstaller.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'MyTube - Watch Videos & Earn',\n    description: 'Watch videos and earn money. Complete daily video watching tasks to earn rewards.',\n    keywords: 'video watching, earn money, online earning, video tasks, rewards',\n    authors: [\n        {\n            name: 'MyTube Team'\n        }\n    ],\n    manifest: '/manifest.json',\n    icons: {\n        icon: '/img/mytube-favicon.svg',\n        apple: '/img/mytube-favicon.svg'\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1.0,\n    maximumScale: 1.0,\n    userScalable: false,\n    themeColor: '#FF0000'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"https://cdn.jsdelivr.net/npm/sweetalert2@11\",\n                        async: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animated-bg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-white/80\",\n                    children: \"Loading MyTube...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQVNVU1xcT25lRHJpdmVcXERlc2t0b3BcXE1ZIFBST0pFQ1RTXFxOb2RlIE15dHViZVxcc3JjXFxhcHBcXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGlubmVyIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MFwiPkxvYWRpbmcgTXlUdWJlLi4uPC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/img/mytube-logo.svg\",\n                            alt: \"MyTube Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-white mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-white mb-2\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"The page you're looking for doesn't exist or has been moved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need help finding what you're looking for?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-home mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/work\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-play-circle mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Watch Videos\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\PWAInstaller.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(ssr)/./src/components/PWAInstaller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cdebug-registration-simple%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cdebug-registration-simple%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/debug-registration-simple/page.tsx */ \"(ssr)/./src/app/debug-registration-simple/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2RlYnVnLXJlZ2lzdHJhdGlvbi1zaW1wbGUlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQWdKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxkZWJ1Zy1yZWdpc3RyYXRpb24tc2ltcGxlXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cdebug-registration-simple%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/debug-registration-simple/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/debug-registration-simple/page.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugRegistrationSimple)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/dataService */ \"(ssr)/./src/lib/dataService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DebugRegistrationSimple() {\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: 'Test User',\n        email: '',\n        mobile: '9876543210',\n        password: 'test123456',\n        confirmPassword: 'test123456',\n        referralCode: ''\n    });\n    const addToResult = (text)=>{\n        setResult((prev)=>prev + text + '\\n');\n        console.log(text);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const testRegistration = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        try {\n            const testEmail = `test${Date.now()}@example.com`;\n            const testPassword = 'test123456';\n            const testName = 'Test Registration User';\n            const testMobile = '9876543210';\n            addToResult('🚀 Starting registration test...');\n            addToResult(`📧 Email: ${testEmail}`);\n            addToResult(`👤 Name: ${testName}`);\n            addToResult(`📱 Mobile: ${testMobile}`);\n            addToResult(`🔧 Firebase Project: ${\"mytube-india\"}`);\n            // Step 1: Create Firebase Auth user\n            addToResult('\\n=== STEP 1: Creating Firebase Auth User ===');\n            try {\n                const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, testEmail, testPassword);\n                const user = userCredential.user;\n                addToResult(`✅ Auth user created successfully!`);\n                addToResult(`🆔 UID: ${user.uid}`);\n                addToResult(`📧 Email: ${user.email}`);\n                addToResult(`✅ Email Verified: ${user.emailVerified}`);\n            } catch (authError) {\n                addToResult(`❌ Auth creation failed: ${authError.message}`);\n                addToResult(`❌ Auth error code: ${authError.code}`);\n                throw authError;\n            }\n            // Step 2: Wait for auth state\n            addToResult('\\n=== STEP 2: Waiting for Auth State ===');\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            addToResult(`✅ Auth state propagated`);\n            addToResult(`Current auth user: ${_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth.currentUser?.uid}`);\n            addToResult(`Auth state matches: ${_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth.currentUser?.uid === _lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth.currentUser?.uid}`);\n            // Step 3: Generate referral code\n            addToResult('\\n=== STEP 3: Generating Referral Code ===');\n            try {\n                const userReferralCode1 = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.generateUniqueReferralCode)();\n                addToResult(`✅ Generated referral code: ${userReferralCode1}`);\n            } catch (refError) {\n                addToResult(`❌ Referral code generation failed: ${refError.message}`);\n                throw refError;\n            }\n            // Step 4: Prepare user data\n            addToResult('\\n=== STEP 4: Preparing User Data ===');\n            const userData = {\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.name]: testName,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.email]: testEmail.toLowerCase(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.mobile]: testMobile,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.referralCode]: userReferralCode,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.referredBy]: '',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.referralBonusCredited]: false,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.plan]: 'Trial',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.planExpiry]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.activeDays]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.joinedDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.Timestamp.now(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.wallet]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.totalVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.todayVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.lastVideoDate]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.videoDuration]: 30,\n                status: 'active'\n            };\n            addToResult(`✅ User data prepared`);\n            addToResult(`📊 Data keys: ${Object.keys(userData).join(', ')}`);\n            // Step 5: Create Firestore document\n            addToResult('\\n=== STEP 5: Creating Firestore Document ===');\n            const user = _lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth.currentUser;\n            if (!user) {\n                addToResult(`❌ No current user found`);\n                throw new Error('No current user found');\n            }\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, _lib_dataService__WEBPACK_IMPORTED_MODULE_5__.COLLECTIONS.users, user.uid);\n            addToResult(`📍 Document path: ${userDocRef.path}`);\n            addToResult(`🔐 Current user UID: ${user.uid}`);\n            addToResult(`📧 Current user email: ${user.email}`);\n            try {\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)(userDocRef, userData);\n                addToResult(`✅ Firestore document created successfully!`);\n            } catch (firestoreError) {\n                addToResult(`❌ Firestore creation failed: ${firestoreError.message}`);\n                addToResult(`❌ Firestore error code: ${firestoreError.code}`);\n                addToResult(`❌ Full error: ${JSON.stringify(firestoreError, null, 2)}`);\n                throw firestoreError;\n            }\n            // Step 6: Verify document\n            addToResult('\\n=== STEP 6: Verifying Document ===');\n            try {\n                const verifyDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(userDocRef);\n                if (verifyDoc.exists()) {\n                    const data = verifyDoc.data();\n                    addToResult(`✅ Document verification successful!`);\n                    addToResult(`📊 Document data keys: ${Object.keys(data).join(', ')}`);\n                    addToResult(`👤 Name: ${data[_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.name]}`);\n                    addToResult(`📧 Email: ${data[_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.email]}`);\n                    addToResult(`🎯 Referral Code: ${data[_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.referralCode]}`);\n                } else {\n                    addToResult(`❌ Document was not created properly`);\n                    throw new Error('Document verification failed');\n                }\n            } catch (verifyError) {\n                addToResult(`❌ Document verification failed: ${verifyError.message}`);\n                throw verifyError;\n            }\n            addToResult('\\n🎉 Registration test completed successfully!');\n        } catch (error) {\n            addToResult(`\\n❌ Registration test failed!`);\n            addToResult(`Error: ${error.message}`);\n            addToResult(`Code: ${error.code}`);\n            addToResult(`Stack: ${error.stack}`);\n            console.error('Registration test error:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const testFormRegistration = async (e)=>{\n        e.preventDefault();\n        setResult('');\n        setIsLoading(true);\n        try {\n            const testEmail = formData.email || `test${Date.now()}@example.com`;\n            addToResult('🚀 Starting FORM registration test...');\n            addToResult(`📧 Email: ${testEmail}`);\n            addToResult(`👤 Name: ${formData.name}`);\n            addToResult(`📱 Mobile: ${formData.mobile}`);\n            // Validation\n            if (!formData.name || !testEmail || !formData.mobile || !formData.password) {\n                throw new Error('Please fill in all required fields');\n            }\n            if (formData.password !== formData.confirmPassword) {\n                throw new Error('Passwords do not match');\n            }\n            // Create user\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, testEmail, formData.password);\n            const user = userCredential.user;\n            addToResult(`✅ Auth user created: ${user.uid}`);\n            // Generate referral code\n            const userReferralCode1 = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.generateUniqueReferralCode)();\n            addToResult(`✅ Referral code: ${userReferralCode1}`);\n            // Create user data\n            const userData = {\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.name]: formData.name.trim(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.email]: testEmail.toLowerCase(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.mobile]: formData.mobile,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.referralCode]: userReferralCode1,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.referredBy]: formData.referralCode || '',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.referralBonusCredited]: false,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.plan]: 'Trial',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.planExpiry]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.activeDays]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.joinedDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.Timestamp.now(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.wallet]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.totalVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.todayVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.lastVideoDate]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_5__.FIELD_NAMES.videoDuration]: 30,\n                status: 'active'\n            };\n            // Create document\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, _lib_dataService__WEBPACK_IMPORTED_MODULE_5__.COLLECTIONS.users, user.uid);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)(userDocRef, userData);\n            addToResult(`✅ Document created successfully!`);\n            addToResult('\\n🎉 FORM registration test completed successfully!');\n        } catch (error) {\n            addToResult(`\\n❌ FORM registration test failed!`);\n            addToResult(`Error: ${error.message}`);\n            addToResult(`Code: ${error.code}`);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-card p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-white mb-6\",\n                                children: \"Automated Test\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: testRegistration,\n                                disabled: isLoading,\n                                className: \"btn-primary mb-6 w-full\",\n                                children: isLoading ? 'Testing Registration...' : 'Test Registration Process'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto\",\n                                children: result || 'Click the button to test registration process...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-card p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-white mb-6\",\n                                children: \"Form Test\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: testFormRegistration,\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        name: \"name\",\n                                        value: formData.name,\n                                        onChange: handleInputChange,\n                                        placeholder: \"Full Name\",\n                                        className: \"form-input\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        name: \"email\",\n                                        value: formData.email,\n                                        onChange: handleInputChange,\n                                        placeholder: \"Email (leave empty for auto-generated)\",\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"tel\",\n                                        name: \"mobile\",\n                                        value: formData.mobile,\n                                        onChange: handleInputChange,\n                                        placeholder: \"Mobile Number\",\n                                        className: \"form-input\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        name: \"password\",\n                                        value: formData.password,\n                                        onChange: handleInputChange,\n                                        placeholder: \"Password\",\n                                        className: \"form-input\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        name: \"confirmPassword\",\n                                        value: formData.confirmPassword,\n                                        onChange: handleInputChange,\n                                        placeholder: \"Confirm Password\",\n                                        className: \"form-input\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        name: \"referralCode\",\n                                        value: formData.referralCode,\n                                        onChange: handleInputChange,\n                                        placeholder: \"Referral Code (Optional)\",\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"btn-primary w-full\",\n                                        children: isLoading ? 'Testing...' : 'Test Form Registration'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto\",\n                                children: result || 'Fill the form and submit to test...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2RlYnVnLXJlZ2lzdHJhdGlvbi1zaW1wbGUvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUVnQztBQUM4QjtBQUNLO0FBQzFCO0FBQytDO0FBRXpFLFNBQVNXO0lBQ3RCLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHYiwrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUNjLFdBQVdDLGFBQWEsR0FBR2YsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDZ0IsVUFBVUMsWUFBWSxHQUFHakIsK0NBQVFBLENBQUM7UUFDdkNrQixNQUFNO1FBQ05DLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLGlCQUFpQjtRQUNqQkMsY0FBYztJQUNoQjtJQUVBLE1BQU1DLGNBQWMsQ0FBQ0M7UUFDbkJaLFVBQVVhLENBQUFBLE9BQVFBLE9BQU9ELE9BQU87UUFDaENFLFFBQVFDLEdBQUcsQ0FBQ0g7SUFDZDtJQUVBLE1BQU1JLG9CQUFvQixDQUFDQztRQUN6QixNQUFNLEVBQUVaLElBQUksRUFBRWEsS0FBSyxFQUFFLEdBQUdELEVBQUVFLE1BQU07UUFDaENmLFlBQVlTLENBQUFBLE9BQVM7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRSxDQUFDUixLQUFLLEVBQUVhO1lBQU07SUFDaEQ7SUFFQSxNQUFNRSxtQkFBbUI7UUFDdkJwQixVQUFVO1FBQ1ZFLGFBQWE7UUFFYixJQUFJO1lBQ0YsTUFBTW1CLFlBQVksQ0FBQyxJQUFJLEVBQUVDLEtBQUtDLEdBQUcsR0FBRyxZQUFZLENBQUM7WUFDakQsTUFBTUMsZUFBZTtZQUNyQixNQUFNQyxXQUFXO1lBQ2pCLE1BQU1DLGFBQWE7WUFFbkJmLFlBQVk7WUFDWkEsWUFBWSxDQUFDLFVBQVUsRUFBRVUsV0FBVztZQUNwQ1YsWUFBWSxDQUFDLFNBQVMsRUFBRWMsVUFBVTtZQUNsQ2QsWUFBWSxDQUFDLFdBQVcsRUFBRWUsWUFBWTtZQUN0Q2YsWUFBWSxDQUFDLHFCQUFxQixFQUFFZ0IsY0FBMkMsRUFBRTtZQUVqRixvQ0FBb0M7WUFDcENoQixZQUFZO1lBQ1osSUFBSTtnQkFDRixNQUFNbUIsaUJBQWlCLE1BQU0xQyw2RUFBOEJBLENBQUNLLCtDQUFJQSxFQUFFNEIsV0FBV0c7Z0JBQzdFLE1BQU1PLE9BQU9ELGVBQWVDLElBQUk7Z0JBQ2hDcEIsWUFBWSxDQUFDLGlDQUFpQyxDQUFDO2dCQUMvQ0EsWUFBWSxDQUFDLFFBQVEsRUFBRW9CLEtBQUtDLEdBQUcsRUFBRTtnQkFDakNyQixZQUFZLENBQUMsVUFBVSxFQUFFb0IsS0FBS3pCLEtBQUssRUFBRTtnQkFDckNLLFlBQVksQ0FBQyxrQkFBa0IsRUFBRW9CLEtBQUtFLGFBQWEsRUFBRTtZQUN2RCxFQUFFLE9BQU9DLFdBQWdCO2dCQUN2QnZCLFlBQVksQ0FBQyx3QkFBd0IsRUFBRXVCLFVBQVVDLE9BQU8sRUFBRTtnQkFDMUR4QixZQUFZLENBQUMsbUJBQW1CLEVBQUV1QixVQUFVRSxJQUFJLEVBQUU7Z0JBQ2xELE1BQU1GO1lBQ1I7WUFFQSw4QkFBOEI7WUFDOUJ2QixZQUFZO1lBQ1osTUFBTSxJQUFJMEIsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztZQUNqRDNCLFlBQVksQ0FBQyx1QkFBdUIsQ0FBQztZQUNyQ0EsWUFBWSxDQUFDLG1CQUFtQixFQUFFbEIsK0NBQUlBLENBQUMrQyxXQUFXLEVBQUVSLEtBQUs7WUFDekRyQixZQUFZLENBQUMsb0JBQW9CLEVBQUVsQiwrQ0FBSUEsQ0FBQytDLFdBQVcsRUFBRVIsUUFBUXZDLCtDQUFJQSxDQUFDK0MsV0FBVyxFQUFFUixLQUFLO1lBRXBGLGlDQUFpQztZQUNqQ3JCLFlBQVk7WUFDWixJQUFJO2dCQUNGLE1BQU04QixvQkFBbUIsTUFBTTVDLDRFQUEwQkE7Z0JBQ3pEYyxZQUFZLENBQUMsMkJBQTJCLEVBQUU4QixtQkFBa0I7WUFDOUQsRUFBRSxPQUFPQyxVQUFlO2dCQUN0Qi9CLFlBQVksQ0FBQyxtQ0FBbUMsRUFBRStCLFNBQVNQLE9BQU8sRUFBRTtnQkFDcEUsTUFBTU87WUFDUjtZQUVBLDRCQUE0QjtZQUM1Qi9CLFlBQVk7WUFDWixNQUFNZ0MsV0FBVztnQkFDZixDQUFDaEQseURBQVdBLENBQUNVLElBQUksQ0FBQyxFQUFFb0I7Z0JBQ3BCLENBQUM5Qix5REFBV0EsQ0FBQ1csS0FBSyxDQUFDLEVBQUVlLFVBQVV1QixXQUFXO2dCQUMxQyxDQUFDakQseURBQVdBLENBQUNZLE1BQU0sQ0FBQyxFQUFFbUI7Z0JBQ3RCLENBQUMvQix5REFBV0EsQ0FBQ2UsWUFBWSxDQUFDLEVBQUUrQjtnQkFDNUIsQ0FBQzlDLHlEQUFXQSxDQUFDa0QsVUFBVSxDQUFDLEVBQUU7Z0JBQzFCLENBQUNsRCx5REFBV0EsQ0FBQ21ELHFCQUFxQixDQUFDLEVBQUU7Z0JBQ3JDLENBQUNuRCx5REFBV0EsQ0FBQ29ELElBQUksQ0FBQyxFQUFFO2dCQUNwQixDQUFDcEQseURBQVdBLENBQUNxRCxVQUFVLENBQUMsRUFBRTtnQkFDMUIsQ0FBQ3JELHlEQUFXQSxDQUFDc0QsVUFBVSxDQUFDLEVBQUU7Z0JBQzFCLENBQUN0RCx5REFBV0EsQ0FBQ3VELFVBQVUsQ0FBQyxFQUFFMUQseURBQVNBLENBQUMrQixHQUFHO2dCQUN2QyxDQUFDNUIseURBQVdBLENBQUN3RCxNQUFNLENBQUMsRUFBRTtnQkFDdEIsQ0FBQ3hELHlEQUFXQSxDQUFDeUQsV0FBVyxDQUFDLEVBQUU7Z0JBQzNCLENBQUN6RCx5REFBV0EsQ0FBQzBELFdBQVcsQ0FBQyxFQUFFO2dCQUMzQixDQUFDMUQseURBQVdBLENBQUMyRCxhQUFhLENBQUMsRUFBRTtnQkFDN0IsQ0FBQzNELHlEQUFXQSxDQUFDNEQsYUFBYSxDQUFDLEVBQUU7Z0JBQzdCQyxRQUFRO1lBQ1Y7WUFFQTdDLFlBQVksQ0FBQyxvQkFBb0IsQ0FBQztZQUNsQ0EsWUFBWSxDQUFDLGNBQWMsRUFBRThDLE9BQU9DLElBQUksQ0FBQ2YsVUFBVWdCLElBQUksQ0FBQyxPQUFPO1lBRS9ELG9DQUFvQztZQUNwQ2hELFlBQVk7WUFDWixNQUFNb0IsT0FBT3RDLCtDQUFJQSxDQUFDK0MsV0FBVztZQUM3QixJQUFJLENBQUNULE1BQU07Z0JBQ1RwQixZQUFZLENBQUMsdUJBQXVCLENBQUM7Z0JBQ3JDLE1BQU0sSUFBSWlELE1BQU07WUFDbEI7WUFFQSxNQUFNQyxhQUFheEUsdURBQUdBLENBQUNLLDZDQUFFQSxFQUFFRSx5REFBV0EsQ0FBQ2tFLEtBQUssRUFBRS9CLEtBQUtDLEdBQUc7WUFDdERyQixZQUFZLENBQUMsa0JBQWtCLEVBQUVrRCxXQUFXRSxJQUFJLEVBQUU7WUFDbERwRCxZQUFZLENBQUMscUJBQXFCLEVBQUVvQixLQUFLQyxHQUFHLEVBQUU7WUFDOUNyQixZQUFZLENBQUMsdUJBQXVCLEVBQUVvQixLQUFLekIsS0FBSyxFQUFFO1lBRWxELElBQUk7Z0JBQ0YsTUFBTWhCLDBEQUFNQSxDQUFDdUUsWUFBWWxCO2dCQUN6QmhDLFlBQVksQ0FBQywwQ0FBMEMsQ0FBQztZQUMxRCxFQUFFLE9BQU9xRCxnQkFBcUI7Z0JBQzVCckQsWUFBWSxDQUFDLDZCQUE2QixFQUFFcUQsZUFBZTdCLE9BQU8sRUFBRTtnQkFDcEV4QixZQUFZLENBQUMsd0JBQXdCLEVBQUVxRCxlQUFlNUIsSUFBSSxFQUFFO2dCQUM1RHpCLFlBQVksQ0FBQyxjQUFjLEVBQUVzRCxLQUFLQyxTQUFTLENBQUNGLGdCQUFnQixNQUFNLElBQUk7Z0JBQ3RFLE1BQU1BO1lBQ1I7WUFFQSwwQkFBMEI7WUFDMUJyRCxZQUFZO1lBQ1osSUFBSTtnQkFDRixNQUFNd0QsWUFBWSxNQUFNNUUsMERBQU1BLENBQUNzRTtnQkFDL0IsSUFBSU0sVUFBVUMsTUFBTSxJQUFJO29CQUN0QixNQUFNQyxPQUFPRixVQUFVRSxJQUFJO29CQUMzQjFELFlBQVksQ0FBQyxtQ0FBbUMsQ0FBQztvQkFDakRBLFlBQVksQ0FBQyx1QkFBdUIsRUFBRThDLE9BQU9DLElBQUksQ0FBQ1csTUFBTVYsSUFBSSxDQUFDLE9BQU87b0JBQ3BFaEQsWUFBWSxDQUFDLFNBQVMsRUFBRTBELElBQUksQ0FBQzFFLHlEQUFXQSxDQUFDVSxJQUFJLENBQUMsRUFBRTtvQkFDaERNLFlBQVksQ0FBQyxVQUFVLEVBQUUwRCxJQUFJLENBQUMxRSx5REFBV0EsQ0FBQ1csS0FBSyxDQUFDLEVBQUU7b0JBQ2xESyxZQUFZLENBQUMsa0JBQWtCLEVBQUUwRCxJQUFJLENBQUMxRSx5REFBV0EsQ0FBQ2UsWUFBWSxDQUFDLEVBQUU7Z0JBQ25FLE9BQU87b0JBQ0xDLFlBQVksQ0FBQyxtQ0FBbUMsQ0FBQztvQkFDakQsTUFBTSxJQUFJaUQsTUFBTTtnQkFDbEI7WUFDRixFQUFFLE9BQU9VLGFBQWtCO2dCQUN6QjNELFlBQVksQ0FBQyxnQ0FBZ0MsRUFBRTJELFlBQVluQyxPQUFPLEVBQUU7Z0JBQ3BFLE1BQU1tQztZQUNSO1lBRUEzRCxZQUFZO1FBRWQsRUFBRSxPQUFPNEQsT0FBWTtZQUNuQjVELFlBQVksQ0FBQyw2QkFBNkIsQ0FBQztZQUMzQ0EsWUFBWSxDQUFDLE9BQU8sRUFBRTRELE1BQU1wQyxPQUFPLEVBQUU7WUFDckN4QixZQUFZLENBQUMsTUFBTSxFQUFFNEQsTUFBTW5DLElBQUksRUFBRTtZQUNqQ3pCLFlBQVksQ0FBQyxPQUFPLEVBQUU0RCxNQUFNQyxLQUFLLEVBQUU7WUFDbkMxRCxRQUFReUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUMsU0FBVTtZQUNSckUsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNdUUsdUJBQXVCLE9BQU94RDtRQUNsQ0EsRUFBRXlELGNBQWM7UUFDaEIxRSxVQUFVO1FBQ1ZFLGFBQWE7UUFFYixJQUFJO1lBQ0YsTUFBTW1CLFlBQVlsQixTQUFTRyxLQUFLLElBQUksQ0FBQyxJQUFJLEVBQUVnQixLQUFLQyxHQUFHLEdBQUcsWUFBWSxDQUFDO1lBRW5FWixZQUFZO1lBQ1pBLFlBQVksQ0FBQyxVQUFVLEVBQUVVLFdBQVc7WUFDcENWLFlBQVksQ0FBQyxTQUFTLEVBQUVSLFNBQVNFLElBQUksRUFBRTtZQUN2Q00sWUFBWSxDQUFDLFdBQVcsRUFBRVIsU0FBU0ksTUFBTSxFQUFFO1lBRTNDLGFBQWE7WUFDYixJQUFJLENBQUNKLFNBQVNFLElBQUksSUFBSSxDQUFDZ0IsYUFBYSxDQUFDbEIsU0FBU0ksTUFBTSxJQUFJLENBQUNKLFNBQVNLLFFBQVEsRUFBRTtnQkFDMUUsTUFBTSxJQUFJb0QsTUFBTTtZQUNsQjtZQUVBLElBQUl6RCxTQUFTSyxRQUFRLEtBQUtMLFNBQVNNLGVBQWUsRUFBRTtnQkFDbEQsTUFBTSxJQUFJbUQsTUFBTTtZQUNsQjtZQUVBLGNBQWM7WUFDZCxNQUFNOUIsaUJBQWlCLE1BQU0xQyw2RUFBOEJBLENBQUNLLCtDQUFJQSxFQUFFNEIsV0FBV2xCLFNBQVNLLFFBQVE7WUFDOUYsTUFBTXVCLE9BQU9ELGVBQWVDLElBQUk7WUFDaENwQixZQUFZLENBQUMscUJBQXFCLEVBQUVvQixLQUFLQyxHQUFHLEVBQUU7WUFFOUMseUJBQXlCO1lBQ3pCLE1BQU1TLG9CQUFtQixNQUFNNUMsNEVBQTBCQTtZQUN6RGMsWUFBWSxDQUFDLGlCQUFpQixFQUFFOEIsbUJBQWtCO1lBRWxELG1CQUFtQjtZQUNuQixNQUFNRSxXQUFXO2dCQUNmLENBQUNoRCx5REFBV0EsQ0FBQ1UsSUFBSSxDQUFDLEVBQUVGLFNBQVNFLElBQUksQ0FBQ3NFLElBQUk7Z0JBQ3RDLENBQUNoRix5REFBV0EsQ0FBQ1csS0FBSyxDQUFDLEVBQUVlLFVBQVV1QixXQUFXO2dCQUMxQyxDQUFDakQseURBQVdBLENBQUNZLE1BQU0sQ0FBQyxFQUFFSixTQUFTSSxNQUFNO2dCQUNyQyxDQUFDWix5REFBV0EsQ0FBQ2UsWUFBWSxDQUFDLEVBQUUrQjtnQkFDNUIsQ0FBQzlDLHlEQUFXQSxDQUFDa0QsVUFBVSxDQUFDLEVBQUUxQyxTQUFTTyxZQUFZLElBQUk7Z0JBQ25ELENBQUNmLHlEQUFXQSxDQUFDbUQscUJBQXFCLENBQUMsRUFBRTtnQkFDckMsQ0FBQ25ELHlEQUFXQSxDQUFDb0QsSUFBSSxDQUFDLEVBQUU7Z0JBQ3BCLENBQUNwRCx5REFBV0EsQ0FBQ3FELFVBQVUsQ0FBQyxFQUFFO2dCQUMxQixDQUFDckQseURBQVdBLENBQUNzRCxVQUFVLENBQUMsRUFBRTtnQkFDMUIsQ0FBQ3RELHlEQUFXQSxDQUFDdUQsVUFBVSxDQUFDLEVBQUUxRCx5REFBU0EsQ0FBQytCLEdBQUc7Z0JBQ3ZDLENBQUM1Qix5REFBV0EsQ0FBQ3dELE1BQU0sQ0FBQyxFQUFFO2dCQUN0QixDQUFDeEQseURBQVdBLENBQUN5RCxXQUFXLENBQUMsRUFBRTtnQkFDM0IsQ0FBQ3pELHlEQUFXQSxDQUFDMEQsV0FBVyxDQUFDLEVBQUU7Z0JBQzNCLENBQUMxRCx5REFBV0EsQ0FBQzJELGFBQWEsQ0FBQyxFQUFFO2dCQUM3QixDQUFDM0QseURBQVdBLENBQUM0RCxhQUFhLENBQUMsRUFBRTtnQkFDN0JDLFFBQVE7WUFDVjtZQUVBLGtCQUFrQjtZQUNsQixNQUFNSyxhQUFheEUsdURBQUdBLENBQUNLLDZDQUFFQSxFQUFFRSx5REFBV0EsQ0FBQ2tFLEtBQUssRUFBRS9CLEtBQUtDLEdBQUc7WUFDdEQsTUFBTTFDLDBEQUFNQSxDQUFDdUUsWUFBWWxCO1lBQ3pCaEMsWUFBWSxDQUFDLGdDQUFnQyxDQUFDO1lBRTlDQSxZQUFZO1FBRWQsRUFBRSxPQUFPNEQsT0FBWTtZQUNuQjVELFlBQVksQ0FBQyxrQ0FBa0MsQ0FBQztZQUNoREEsWUFBWSxDQUFDLE9BQU8sRUFBRTRELE1BQU1wQyxPQUFPLEVBQUU7WUFDckN4QixZQUFZLENBQUMsTUFBTSxFQUFFNEQsTUFBTW5DLElBQUksRUFBRTtRQUNuQyxTQUFVO1lBQ1JsQyxhQUFhO1FBQ2Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDMEU7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFHRCxXQUFVOzBDQUFxQzs7Ozs7OzBDQUVuRCw4REFBQ0U7Z0NBQ0NDLFNBQVM1RDtnQ0FDVDZELFVBQVVoRjtnQ0FDVjRFLFdBQVU7MENBRVQ1RSxZQUFZLDRCQUE0Qjs7Ozs7OzBDQUczQyw4REFBQzJFO2dDQUFJQyxXQUFVOzBDQUNaOUUsVUFBVTs7Ozs7Ozs7Ozs7O2tDQUtmLDhEQUFDNkU7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBR0QsV0FBVTswQ0FBcUM7Ozs7OzswQ0FFbkQsOERBQUNLO2dDQUFLQyxVQUFVVjtnQ0FBc0JJLFdBQVU7O2tEQUM5Qyw4REFBQ087d0NBQ0NDLE1BQUs7d0NBQ0xoRixNQUFLO3dDQUNMYSxPQUFPZixTQUFTRSxJQUFJO3dDQUNwQmlGLFVBQVV0RTt3Q0FDVnVFLGFBQVk7d0NBQ1pWLFdBQVU7d0NBQ1ZXLFFBQVE7Ozs7OztrREFFViw4REFBQ0o7d0NBQ0NDLE1BQUs7d0NBQ0xoRixNQUFLO3dDQUNMYSxPQUFPZixTQUFTRyxLQUFLO3dDQUNyQmdGLFVBQVV0RTt3Q0FDVnVFLGFBQVk7d0NBQ1pWLFdBQVU7Ozs7OztrREFFWiw4REFBQ087d0NBQ0NDLE1BQUs7d0NBQ0xoRixNQUFLO3dDQUNMYSxPQUFPZixTQUFTSSxNQUFNO3dDQUN0QitFLFVBQVV0RTt3Q0FDVnVFLGFBQVk7d0NBQ1pWLFdBQVU7d0NBQ1ZXLFFBQVE7Ozs7OztrREFFViw4REFBQ0o7d0NBQ0NDLE1BQUs7d0NBQ0xoRixNQUFLO3dDQUNMYSxPQUFPZixTQUFTSyxRQUFRO3dDQUN4QjhFLFVBQVV0RTt3Q0FDVnVFLGFBQVk7d0NBQ1pWLFdBQVU7d0NBQ1ZXLFFBQVE7Ozs7OztrREFFViw4REFBQ0o7d0NBQ0NDLE1BQUs7d0NBQ0xoRixNQUFLO3dDQUNMYSxPQUFPZixTQUFTTSxlQUFlO3dDQUMvQjZFLFVBQVV0RTt3Q0FDVnVFLGFBQVk7d0NBQ1pWLFdBQVU7d0NBQ1ZXLFFBQVE7Ozs7OztrREFFViw4REFBQ0o7d0NBQ0NDLE1BQUs7d0NBQ0xoRixNQUFLO3dDQUNMYSxPQUFPZixTQUFTTyxZQUFZO3dDQUM1QjRFLFVBQVV0RTt3Q0FDVnVFLGFBQVk7d0NBQ1pWLFdBQVU7Ozs7OztrREFFWiw4REFBQ0U7d0NBQ0NNLE1BQUs7d0NBQ0xKLFVBQVVoRjt3Q0FDVjRFLFdBQVU7a0RBRVQ1RSxZQUFZLGVBQWU7Ozs7Ozs7Ozs7OzswQ0FJaEMsOERBQUMyRTtnQ0FBSUMsV0FBVTswQ0FDWjlFLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPekIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQVNVU1xcT25lRHJpdmVcXERlc2t0b3BcXE1ZIFBST0pFQ1RTXFxOb2RlIE15dHViZVxcc3JjXFxhcHBcXGRlYnVnLXJlZ2lzdHJhdGlvbi1zaW1wbGVcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgY3JlYXRlVXNlcldpdGhFbWFpbEFuZFBhc3N3b3JkIH0gZnJvbSAnZmlyZWJhc2UvYXV0aCdcbmltcG9ydCB7IGRvYywgc2V0RG9jLCBnZXREb2MsIFRpbWVzdGFtcCB9IGZyb20gJ2ZpcmViYXNlL2ZpcmVzdG9yZSdcbmltcG9ydCB7IGF1dGgsIGRiIH0gZnJvbSAnQC9saWIvZmlyZWJhc2UnXG5pbXBvcnQgeyBGSUVMRF9OQU1FUywgQ09MTEVDVElPTlMsIGdlbmVyYXRlVW5pcXVlUmVmZXJyYWxDb2RlIH0gZnJvbSAnQC9saWIvZGF0YVNlcnZpY2UnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERlYnVnUmVnaXN0cmF0aW9uU2ltcGxlKCkge1xuICBjb25zdCBbcmVzdWx0LCBzZXRSZXN1bHRdID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgbmFtZTogJ1Rlc3QgVXNlcicsXG4gICAgZW1haWw6ICcnLFxuICAgIG1vYmlsZTogJzk4NzY1NDMyMTAnLFxuICAgIHBhc3N3b3JkOiAndGVzdDEyMzQ1NicsXG4gICAgY29uZmlybVBhc3N3b3JkOiAndGVzdDEyMzQ1NicsXG4gICAgcmVmZXJyYWxDb2RlOiAnJ1xuICB9KVxuXG4gIGNvbnN0IGFkZFRvUmVzdWx0ID0gKHRleHQ6IHN0cmluZykgPT4ge1xuICAgIHNldFJlc3VsdChwcmV2ID0+IHByZXYgKyB0ZXh0ICsgJ1xcbicpXG4gICAgY29uc29sZS5sb2codGV4dClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgeyBuYW1lLCB2YWx1ZSB9ID0gZS50YXJnZXRcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIFtuYW1lXTogdmFsdWUgfSkpXG4gIH1cblxuICBjb25zdCB0ZXN0UmVnaXN0cmF0aW9uID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldFJlc3VsdCgnJylcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSlcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB0ZXN0RW1haWwgPSBgdGVzdCR7RGF0ZS5ub3coKX1AZXhhbXBsZS5jb21gXG4gICAgICBjb25zdCB0ZXN0UGFzc3dvcmQgPSAndGVzdDEyMzQ1NidcbiAgICAgIGNvbnN0IHRlc3ROYW1lID0gJ1Rlc3QgUmVnaXN0cmF0aW9uIFVzZXInXG4gICAgICBjb25zdCB0ZXN0TW9iaWxlID0gJzk4NzY1NDMyMTAnXG5cbiAgICAgIGFkZFRvUmVzdWx0KCfwn5qAIFN0YXJ0aW5nIHJlZ2lzdHJhdGlvbiB0ZXN0Li4uJylcbiAgICAgIGFkZFRvUmVzdWx0KGDwn5OnIEVtYWlsOiAke3Rlc3RFbWFpbH1gKVxuICAgICAgYWRkVG9SZXN1bHQoYPCfkaQgTmFtZTogJHt0ZXN0TmFtZX1gKVxuICAgICAgYWRkVG9SZXN1bHQoYPCfk7EgTW9iaWxlOiAke3Rlc3RNb2JpbGV9YClcbiAgICAgIGFkZFRvUmVzdWx0KGDwn5SnIEZpcmViYXNlIFByb2plY3Q6ICR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfUFJPSkVDVF9JRH1gKVxuXG4gICAgICAvLyBTdGVwIDE6IENyZWF0ZSBGaXJlYmFzZSBBdXRoIHVzZXJcbiAgICAgIGFkZFRvUmVzdWx0KCdcXG49PT0gU1RFUCAxOiBDcmVhdGluZyBGaXJlYmFzZSBBdXRoIFVzZXIgPT09JylcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHVzZXJDcmVkZW50aWFsID0gYXdhaXQgY3JlYXRlVXNlcldpdGhFbWFpbEFuZFBhc3N3b3JkKGF1dGgsIHRlc3RFbWFpbCwgdGVzdFBhc3N3b3JkKVxuICAgICAgICBjb25zdCB1c2VyID0gdXNlckNyZWRlbnRpYWwudXNlclxuICAgICAgICBhZGRUb1Jlc3VsdChg4pyFIEF1dGggdXNlciBjcmVhdGVkIHN1Y2Nlc3NmdWxseSFgKVxuICAgICAgICBhZGRUb1Jlc3VsdChg8J+GlCBVSUQ6ICR7dXNlci51aWR9YClcbiAgICAgICAgYWRkVG9SZXN1bHQoYPCfk6cgRW1haWw6ICR7dXNlci5lbWFpbH1gKVxuICAgICAgICBhZGRUb1Jlc3VsdChg4pyFIEVtYWlsIFZlcmlmaWVkOiAke3VzZXIuZW1haWxWZXJpZmllZH1gKVxuICAgICAgfSBjYXRjaCAoYXV0aEVycm9yOiBhbnkpIHtcbiAgICAgICAgYWRkVG9SZXN1bHQoYOKdjCBBdXRoIGNyZWF0aW9uIGZhaWxlZDogJHthdXRoRXJyb3IubWVzc2FnZX1gKVxuICAgICAgICBhZGRUb1Jlc3VsdChg4p2MIEF1dGggZXJyb3IgY29kZTogJHthdXRoRXJyb3IuY29kZX1gKVxuICAgICAgICB0aHJvdyBhdXRoRXJyb3JcbiAgICAgIH1cblxuICAgICAgLy8gU3RlcCAyOiBXYWl0IGZvciBhdXRoIHN0YXRlXG4gICAgICBhZGRUb1Jlc3VsdCgnXFxuPT09IFNURVAgMjogV2FpdGluZyBmb3IgQXV0aCBTdGF0ZSA9PT0nKVxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDIwMDApKVxuICAgICAgYWRkVG9SZXN1bHQoYOKchSBBdXRoIHN0YXRlIHByb3BhZ2F0ZWRgKVxuICAgICAgYWRkVG9SZXN1bHQoYEN1cnJlbnQgYXV0aCB1c2VyOiAke2F1dGguY3VycmVudFVzZXI/LnVpZH1gKVxuICAgICAgYWRkVG9SZXN1bHQoYEF1dGggc3RhdGUgbWF0Y2hlczogJHthdXRoLmN1cnJlbnRVc2VyPy51aWQgPT09IGF1dGguY3VycmVudFVzZXI/LnVpZH1gKVxuXG4gICAgICAvLyBTdGVwIDM6IEdlbmVyYXRlIHJlZmVycmFsIGNvZGVcbiAgICAgIGFkZFRvUmVzdWx0KCdcXG49PT0gU1RFUCAzOiBHZW5lcmF0aW5nIFJlZmVycmFsIENvZGUgPT09JylcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHVzZXJSZWZlcnJhbENvZGUgPSBhd2FpdCBnZW5lcmF0ZVVuaXF1ZVJlZmVycmFsQ29kZSgpXG4gICAgICAgIGFkZFRvUmVzdWx0KGDinIUgR2VuZXJhdGVkIHJlZmVycmFsIGNvZGU6ICR7dXNlclJlZmVycmFsQ29kZX1gKVxuICAgICAgfSBjYXRjaCAocmVmRXJyb3I6IGFueSkge1xuICAgICAgICBhZGRUb1Jlc3VsdChg4p2MIFJlZmVycmFsIGNvZGUgZ2VuZXJhdGlvbiBmYWlsZWQ6ICR7cmVmRXJyb3IubWVzc2FnZX1gKVxuICAgICAgICB0aHJvdyByZWZFcnJvclxuICAgICAgfVxuXG4gICAgICAvLyBTdGVwIDQ6IFByZXBhcmUgdXNlciBkYXRhXG4gICAgICBhZGRUb1Jlc3VsdCgnXFxuPT09IFNURVAgNDogUHJlcGFyaW5nIFVzZXIgRGF0YSA9PT0nKVxuICAgICAgY29uc3QgdXNlckRhdGEgPSB7XG4gICAgICAgIFtGSUVMRF9OQU1FUy5uYW1lXTogdGVzdE5hbWUsXG4gICAgICAgIFtGSUVMRF9OQU1FUy5lbWFpbF06IHRlc3RFbWFpbC50b0xvd2VyQ2FzZSgpLFxuICAgICAgICBbRklFTERfTkFNRVMubW9iaWxlXTogdGVzdE1vYmlsZSxcbiAgICAgICAgW0ZJRUxEX05BTUVTLnJlZmVycmFsQ29kZV06IHVzZXJSZWZlcnJhbENvZGUsXG4gICAgICAgIFtGSUVMRF9OQU1FUy5yZWZlcnJlZEJ5XTogJycsXG4gICAgICAgIFtGSUVMRF9OQU1FUy5yZWZlcnJhbEJvbnVzQ3JlZGl0ZWRdOiBmYWxzZSxcbiAgICAgICAgW0ZJRUxEX05BTUVTLnBsYW5dOiAnVHJpYWwnLFxuICAgICAgICBbRklFTERfTkFNRVMucGxhbkV4cGlyeV06IG51bGwsXG4gICAgICAgIFtGSUVMRF9OQU1FUy5hY3RpdmVEYXlzXTogMCxcbiAgICAgICAgW0ZJRUxEX05BTUVTLmpvaW5lZERhdGVdOiBUaW1lc3RhbXAubm93KCksXG4gICAgICAgIFtGSUVMRF9OQU1FUy53YWxsZXRdOiAwLFxuICAgICAgICBbRklFTERfTkFNRVMudG90YWxWaWRlb3NdOiAwLFxuICAgICAgICBbRklFTERfTkFNRVMudG9kYXlWaWRlb3NdOiAwLFxuICAgICAgICBbRklFTERfTkFNRVMubGFzdFZpZGVvRGF0ZV06IG51bGwsXG4gICAgICAgIFtGSUVMRF9OQU1FUy52aWRlb0R1cmF0aW9uXTogMzAsXG4gICAgICAgIHN0YXR1czogJ2FjdGl2ZSdcbiAgICAgIH1cblxuICAgICAgYWRkVG9SZXN1bHQoYOKchSBVc2VyIGRhdGEgcHJlcGFyZWRgKVxuICAgICAgYWRkVG9SZXN1bHQoYPCfk4ogRGF0YSBrZXlzOiAke09iamVjdC5rZXlzKHVzZXJEYXRhKS5qb2luKCcsICcpfWApXG5cbiAgICAgIC8vIFN0ZXAgNTogQ3JlYXRlIEZpcmVzdG9yZSBkb2N1bWVudFxuICAgICAgYWRkVG9SZXN1bHQoJ1xcbj09PSBTVEVQIDU6IENyZWF0aW5nIEZpcmVzdG9yZSBEb2N1bWVudCA9PT0nKVxuICAgICAgY29uc3QgdXNlciA9IGF1dGguY3VycmVudFVzZXJcbiAgICAgIGlmICghdXNlcikge1xuICAgICAgICBhZGRUb1Jlc3VsdChg4p2MIE5vIGN1cnJlbnQgdXNlciBmb3VuZGApXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignTm8gY3VycmVudCB1c2VyIGZvdW5kJylcbiAgICAgIH1cblxuICAgICAgY29uc3QgdXNlckRvY1JlZiA9IGRvYyhkYiwgQ09MTEVDVElPTlMudXNlcnMsIHVzZXIudWlkKVxuICAgICAgYWRkVG9SZXN1bHQoYPCfk40gRG9jdW1lbnQgcGF0aDogJHt1c2VyRG9jUmVmLnBhdGh9YClcbiAgICAgIGFkZFRvUmVzdWx0KGDwn5SQIEN1cnJlbnQgdXNlciBVSUQ6ICR7dXNlci51aWR9YClcbiAgICAgIGFkZFRvUmVzdWx0KGDwn5OnIEN1cnJlbnQgdXNlciBlbWFpbDogJHt1c2VyLmVtYWlsfWApXG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHNldERvYyh1c2VyRG9jUmVmLCB1c2VyRGF0YSlcbiAgICAgICAgYWRkVG9SZXN1bHQoYOKchSBGaXJlc3RvcmUgZG9jdW1lbnQgY3JlYXRlZCBzdWNjZXNzZnVsbHkhYClcbiAgICAgIH0gY2F0Y2ggKGZpcmVzdG9yZUVycm9yOiBhbnkpIHtcbiAgICAgICAgYWRkVG9SZXN1bHQoYOKdjCBGaXJlc3RvcmUgY3JlYXRpb24gZmFpbGVkOiAke2ZpcmVzdG9yZUVycm9yLm1lc3NhZ2V9YClcbiAgICAgICAgYWRkVG9SZXN1bHQoYOKdjCBGaXJlc3RvcmUgZXJyb3IgY29kZTogJHtmaXJlc3RvcmVFcnJvci5jb2RlfWApXG4gICAgICAgIGFkZFRvUmVzdWx0KGDinYwgRnVsbCBlcnJvcjogJHtKU09OLnN0cmluZ2lmeShmaXJlc3RvcmVFcnJvciwgbnVsbCwgMil9YClcbiAgICAgICAgdGhyb3cgZmlyZXN0b3JlRXJyb3JcbiAgICAgIH1cblxuICAgICAgLy8gU3RlcCA2OiBWZXJpZnkgZG9jdW1lbnRcbiAgICAgIGFkZFRvUmVzdWx0KCdcXG49PT0gU1RFUCA2OiBWZXJpZnlpbmcgRG9jdW1lbnQgPT09JylcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHZlcmlmeURvYyA9IGF3YWl0IGdldERvYyh1c2VyRG9jUmVmKVxuICAgICAgICBpZiAodmVyaWZ5RG9jLmV4aXN0cygpKSB7XG4gICAgICAgICAgY29uc3QgZGF0YSA9IHZlcmlmeURvYy5kYXRhKClcbiAgICAgICAgICBhZGRUb1Jlc3VsdChg4pyFIERvY3VtZW50IHZlcmlmaWNhdGlvbiBzdWNjZXNzZnVsIWApXG4gICAgICAgICAgYWRkVG9SZXN1bHQoYPCfk4ogRG9jdW1lbnQgZGF0YSBrZXlzOiAke09iamVjdC5rZXlzKGRhdGEpLmpvaW4oJywgJyl9YClcbiAgICAgICAgICBhZGRUb1Jlc3VsdChg8J+RpCBOYW1lOiAke2RhdGFbRklFTERfTkFNRVMubmFtZV19YClcbiAgICAgICAgICBhZGRUb1Jlc3VsdChg8J+TpyBFbWFpbDogJHtkYXRhW0ZJRUxEX05BTUVTLmVtYWlsXX1gKVxuICAgICAgICAgIGFkZFRvUmVzdWx0KGDwn46vIFJlZmVycmFsIENvZGU6ICR7ZGF0YVtGSUVMRF9OQU1FUy5yZWZlcnJhbENvZGVdfWApXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgYWRkVG9SZXN1bHQoYOKdjCBEb2N1bWVudCB3YXMgbm90IGNyZWF0ZWQgcHJvcGVybHlgKVxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRG9jdW1lbnQgdmVyaWZpY2F0aW9uIGZhaWxlZCcpXG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKHZlcmlmeUVycm9yOiBhbnkpIHtcbiAgICAgICAgYWRkVG9SZXN1bHQoYOKdjCBEb2N1bWVudCB2ZXJpZmljYXRpb24gZmFpbGVkOiAke3ZlcmlmeUVycm9yLm1lc3NhZ2V9YClcbiAgICAgICAgdGhyb3cgdmVyaWZ5RXJyb3JcbiAgICAgIH1cblxuICAgICAgYWRkVG9SZXN1bHQoJ1xcbvCfjokgUmVnaXN0cmF0aW9uIHRlc3QgY29tcGxldGVkIHN1Y2Nlc3NmdWxseSEnKVxuXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgYWRkVG9SZXN1bHQoYFxcbuKdjCBSZWdpc3RyYXRpb24gdGVzdCBmYWlsZWQhYClcbiAgICAgIGFkZFRvUmVzdWx0KGBFcnJvcjogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICBhZGRUb1Jlc3VsdChgQ29kZTogJHtlcnJvci5jb2RlfWApXG4gICAgICBhZGRUb1Jlc3VsdChgU3RhY2s6ICR7ZXJyb3Iuc3RhY2t9YClcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1JlZ2lzdHJhdGlvbiB0ZXN0IGVycm9yOicsIGVycm9yKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgdGVzdEZvcm1SZWdpc3RyYXRpb24gPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgc2V0UmVzdWx0KCcnKVxuICAgIHNldElzTG9hZGluZyh0cnVlKVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRlc3RFbWFpbCA9IGZvcm1EYXRhLmVtYWlsIHx8IGB0ZXN0JHtEYXRlLm5vdygpfUBleGFtcGxlLmNvbWBcblxuICAgICAgYWRkVG9SZXN1bHQoJ/CfmoAgU3RhcnRpbmcgRk9STSByZWdpc3RyYXRpb24gdGVzdC4uLicpXG4gICAgICBhZGRUb1Jlc3VsdChg8J+TpyBFbWFpbDogJHt0ZXN0RW1haWx9YClcbiAgICAgIGFkZFRvUmVzdWx0KGDwn5GkIE5hbWU6ICR7Zm9ybURhdGEubmFtZX1gKVxuICAgICAgYWRkVG9SZXN1bHQoYPCfk7EgTW9iaWxlOiAke2Zvcm1EYXRhLm1vYmlsZX1gKVxuXG4gICAgICAvLyBWYWxpZGF0aW9uXG4gICAgICBpZiAoIWZvcm1EYXRhLm5hbWUgfHwgIXRlc3RFbWFpbCB8fCAhZm9ybURhdGEubW9iaWxlIHx8ICFmb3JtRGF0YS5wYXNzd29yZCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1BsZWFzZSBmaWxsIGluIGFsbCByZXF1aXJlZCBmaWVsZHMnKVxuICAgICAgfVxuXG4gICAgICBpZiAoZm9ybURhdGEucGFzc3dvcmQgIT09IGZvcm1EYXRhLmNvbmZpcm1QYXNzd29yZCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1Bhc3N3b3JkcyBkbyBub3QgbWF0Y2gnKVxuICAgICAgfVxuXG4gICAgICAvLyBDcmVhdGUgdXNlclxuICAgICAgY29uc3QgdXNlckNyZWRlbnRpYWwgPSBhd2FpdCBjcmVhdGVVc2VyV2l0aEVtYWlsQW5kUGFzc3dvcmQoYXV0aCwgdGVzdEVtYWlsLCBmb3JtRGF0YS5wYXNzd29yZClcbiAgICAgIGNvbnN0IHVzZXIgPSB1c2VyQ3JlZGVudGlhbC51c2VyXG4gICAgICBhZGRUb1Jlc3VsdChg4pyFIEF1dGggdXNlciBjcmVhdGVkOiAke3VzZXIudWlkfWApXG5cbiAgICAgIC8vIEdlbmVyYXRlIHJlZmVycmFsIGNvZGVcbiAgICAgIGNvbnN0IHVzZXJSZWZlcnJhbENvZGUgPSBhd2FpdCBnZW5lcmF0ZVVuaXF1ZVJlZmVycmFsQ29kZSgpXG4gICAgICBhZGRUb1Jlc3VsdChg4pyFIFJlZmVycmFsIGNvZGU6ICR7dXNlclJlZmVycmFsQ29kZX1gKVxuXG4gICAgICAvLyBDcmVhdGUgdXNlciBkYXRhXG4gICAgICBjb25zdCB1c2VyRGF0YSA9IHtcbiAgICAgICAgW0ZJRUxEX05BTUVTLm5hbWVdOiBmb3JtRGF0YS5uYW1lLnRyaW0oKSxcbiAgICAgICAgW0ZJRUxEX05BTUVTLmVtYWlsXTogdGVzdEVtYWlsLnRvTG93ZXJDYXNlKCksXG4gICAgICAgIFtGSUVMRF9OQU1FUy5tb2JpbGVdOiBmb3JtRGF0YS5tb2JpbGUsXG4gICAgICAgIFtGSUVMRF9OQU1FUy5yZWZlcnJhbENvZGVdOiB1c2VyUmVmZXJyYWxDb2RlLFxuICAgICAgICBbRklFTERfTkFNRVMucmVmZXJyZWRCeV06IGZvcm1EYXRhLnJlZmVycmFsQ29kZSB8fCAnJyxcbiAgICAgICAgW0ZJRUxEX05BTUVTLnJlZmVycmFsQm9udXNDcmVkaXRlZF06IGZhbHNlLFxuICAgICAgICBbRklFTERfTkFNRVMucGxhbl06ICdUcmlhbCcsXG4gICAgICAgIFtGSUVMRF9OQU1FUy5wbGFuRXhwaXJ5XTogbnVsbCxcbiAgICAgICAgW0ZJRUxEX05BTUVTLmFjdGl2ZURheXNdOiAwLFxuICAgICAgICBbRklFTERfTkFNRVMuam9pbmVkRGF0ZV06IFRpbWVzdGFtcC5ub3coKSxcbiAgICAgICAgW0ZJRUxEX05BTUVTLndhbGxldF06IDAsXG4gICAgICAgIFtGSUVMRF9OQU1FUy50b3RhbFZpZGVvc106IDAsXG4gICAgICAgIFtGSUVMRF9OQU1FUy50b2RheVZpZGVvc106IDAsXG4gICAgICAgIFtGSUVMRF9OQU1FUy5sYXN0VmlkZW9EYXRlXTogbnVsbCxcbiAgICAgICAgW0ZJRUxEX05BTUVTLnZpZGVvRHVyYXRpb25dOiAzMCxcbiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJ1xuICAgICAgfVxuXG4gICAgICAvLyBDcmVhdGUgZG9jdW1lbnRcbiAgICAgIGNvbnN0IHVzZXJEb2NSZWYgPSBkb2MoZGIsIENPTExFQ1RJT05TLnVzZXJzLCB1c2VyLnVpZClcbiAgICAgIGF3YWl0IHNldERvYyh1c2VyRG9jUmVmLCB1c2VyRGF0YSlcbiAgICAgIGFkZFRvUmVzdWx0KGDinIUgRG9jdW1lbnQgY3JlYXRlZCBzdWNjZXNzZnVsbHkhYClcblxuICAgICAgYWRkVG9SZXN1bHQoJ1xcbvCfjokgRk9STSByZWdpc3RyYXRpb24gdGVzdCBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5IScpXG5cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBhZGRUb1Jlc3VsdChgXFxu4p2MIEZPUk0gcmVnaXN0cmF0aW9uIHRlc3QgZmFpbGVkIWApXG4gICAgICBhZGRUb1Jlc3VsdChgRXJyb3I6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgYWRkVG9SZXN1bHQoYENvZGU6ICR7ZXJyb3IuY29kZX1gKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLXB1cnBsZS05MDAgdmlhLWJsdWUtOTAwIHRvLWluZGlnby05MDAgcC04XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTZ4bCBteC1hdXRvXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtOFwiPlxuICAgICAgICAgIHsvKiBBdXRvbWF0ZWQgVGVzdCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzLWNhcmQgcC04XCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNlwiPkF1dG9tYXRlZCBUZXN0PC9oMT5cblxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXt0ZXN0UmVnaXN0cmF0aW9ufVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSBtYi02IHctZnVsbFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAnVGVzdGluZyBSZWdpc3RyYXRpb24uLi4nIDogJ1Rlc3QgUmVnaXN0cmF0aW9uIFByb2Nlc3MnfVxuICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmxhY2svMzAgcm91bmRlZC1sZyBwLTQgdGV4dC13aGl0ZSBmb250LW1vbm8gdGV4dC1zbSB3aGl0ZXNwYWNlLXByZS13cmFwIG1heC1oLTk2IG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgICB7cmVzdWx0IHx8ICdDbGljayB0aGUgYnV0dG9uIHRvIHRlc3QgcmVnaXN0cmF0aW9uIHByb2Nlc3MuLi4nfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRm9ybSBUZXN0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3MtY2FyZCBwLThcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCI+Rm9ybSBUZXN0PC9oMT5cblxuICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e3Rlc3RGb3JtUmVnaXN0cmF0aW9ufSBjbGFzc05hbWU9XCJzcGFjZS15LTQgbWItNlwiPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgbmFtZT1cIm5hbWVcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5uYW1lfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkZ1bGwgTmFtZVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICBuYW1lPVwiZW1haWxcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5lbWFpbH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbWFpbCAobGVhdmUgZW1wdHkgZm9yIGF1dG8tZ2VuZXJhdGVkKVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZWxcIlxuICAgICAgICAgICAgICAgIG5hbWU9XCJtb2JpbGVcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5tb2JpbGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTW9iaWxlIE51bWJlclwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICBuYW1lPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wYXNzd29yZH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJQYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICBuYW1lPVwiY29uZmlybVBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlybVBhc3N3b3JkfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkNvbmZpcm0gUGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXRcIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBuYW1lPVwicmVmZXJyYWxDb2RlXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucmVmZXJyYWxDb2RlfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlJlZmVycmFsIENvZGUgKE9wdGlvbmFsKVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5IHctZnVsbFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gJ1Rlc3RpbmcuLi4nIDogJ1Rlc3QgRm9ybSBSZWdpc3RyYXRpb24nfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZm9ybT5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibGFjay8zMCByb3VuZGVkLWxnIHAtNCB0ZXh0LXdoaXRlIGZvbnQtbW9ubyB0ZXh0LXNtIHdoaXRlc3BhY2UtcHJlLXdyYXAgbWF4LWgtOTYgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgIHtyZXN1bHQgfHwgJ0ZpbGwgdGhlIGZvcm0gYW5kIHN1Ym1pdCB0byB0ZXN0Li4uJ31cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJjcmVhdGVVc2VyV2l0aEVtYWlsQW5kUGFzc3dvcmQiLCJkb2MiLCJzZXREb2MiLCJnZXREb2MiLCJUaW1lc3RhbXAiLCJhdXRoIiwiZGIiLCJGSUVMRF9OQU1FUyIsIkNPTExFQ1RJT05TIiwiZ2VuZXJhdGVVbmlxdWVSZWZlcnJhbENvZGUiLCJEZWJ1Z1JlZ2lzdHJhdGlvblNpbXBsZSIsInJlc3VsdCIsInNldFJlc3VsdCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJuYW1lIiwiZW1haWwiLCJtb2JpbGUiLCJwYXNzd29yZCIsImNvbmZpcm1QYXNzd29yZCIsInJlZmVycmFsQ29kZSIsImFkZFRvUmVzdWx0IiwidGV4dCIsInByZXYiLCJjb25zb2xlIiwibG9nIiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJlIiwidmFsdWUiLCJ0YXJnZXQiLCJ0ZXN0UmVnaXN0cmF0aW9uIiwidGVzdEVtYWlsIiwiRGF0ZSIsIm5vdyIsInRlc3RQYXNzd29yZCIsInRlc3ROYW1lIiwidGVzdE1vYmlsZSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9QUk9KRUNUX0lEIiwidXNlckNyZWRlbnRpYWwiLCJ1c2VyIiwidWlkIiwiZW1haWxWZXJpZmllZCIsImF1dGhFcnJvciIsIm1lc3NhZ2UiLCJjb2RlIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiY3VycmVudFVzZXIiLCJ1c2VyUmVmZXJyYWxDb2RlIiwicmVmRXJyb3IiLCJ1c2VyRGF0YSIsInRvTG93ZXJDYXNlIiwicmVmZXJyZWRCeSIsInJlZmVycmFsQm9udXNDcmVkaXRlZCIsInBsYW4iLCJwbGFuRXhwaXJ5IiwiYWN0aXZlRGF5cyIsImpvaW5lZERhdGUiLCJ3YWxsZXQiLCJ0b3RhbFZpZGVvcyIsInRvZGF5VmlkZW9zIiwibGFzdFZpZGVvRGF0ZSIsInZpZGVvRHVyYXRpb24iLCJzdGF0dXMiLCJPYmplY3QiLCJrZXlzIiwiam9pbiIsIkVycm9yIiwidXNlckRvY1JlZiIsInVzZXJzIiwicGF0aCIsImZpcmVzdG9yZUVycm9yIiwiSlNPTiIsInN0cmluZ2lmeSIsInZlcmlmeURvYyIsImV4aXN0cyIsImRhdGEiLCJ2ZXJpZnlFcnJvciIsImVycm9yIiwic3RhY2siLCJ0ZXN0Rm9ybVJlZ2lzdHJhdGlvbiIsInByZXZlbnREZWZhdWx0IiwidHJpbSIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiZm9ybSIsIm9uU3VibWl0IiwiaW5wdXQiLCJ0eXBlIiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsInJlcXVpcmVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/debug-registration-simple/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            console.error('Application error:', error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/img/mytube-logo.svg\",\n                            alt: \"MyTube Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-white mb-4\",\n                            children: \"Oops!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"We encountered an unexpected error. Please try again or contact support if the problem persists.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need immediate help?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-redo mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-home mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Go Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-8 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"text-white/60 cursor-pointer\",\n                            children: \"Error Details (Development)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-4 p-4 bg-red-900/20 rounded-lg text-red-300 text-sm overflow-auto\",\n                            children: [\n                                error.message,\n                                error.stack && '\\n\\n' + error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-8 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"fas fa-exclamation-triangle text-red-400 text-4xl mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-4\",\n                            children: \"An error occurred while loading this page. Please refresh and try again.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-refresh mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                \"Refresh Page\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PWAInstaller)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction PWAInstaller() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInstallButton, setShowInstallButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PWAInstaller.useEffect\": ()=>{\n            // Register service worker\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"PWAInstaller.useEffect\": (registration)=>{\n                        console.log('SW registered: ', registration);\n                    }\n                }[\"PWAInstaller.useEffect\"]).catch({\n                    \"PWAInstaller.useEffect\": (registrationError)=>{\n                        console.log('SW registration failed: ', registrationError);\n                    }\n                }[\"PWAInstaller.useEffect\"]);\n            }\n            // Listen for beforeinstallprompt event\n            const handleBeforeInstallPrompt = {\n                \"PWAInstaller.useEffect.handleBeforeInstallPrompt\": (e)=>{\n                    e.preventDefault();\n                    setDeferredPrompt(e);\n                    setShowInstallButton(true);\n                }\n            }[\"PWAInstaller.useEffect.handleBeforeInstallPrompt\"];\n            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n            // Check if app is already installed\n            if (window.matchMedia('(display-mode: standalone)').matches) {\n                setShowInstallButton(false);\n            }\n            return ({\n                \"PWAInstaller.useEffect\": ()=>{\n                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n                }\n            })[\"PWAInstaller.useEffect\"];\n        }\n    }[\"PWAInstaller.useEffect\"], []);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        deferredPrompt.prompt();\n        const { outcome } = await deferredPrompt.userChoice;\n        if (outcome === 'accepted') {\n            console.log('User accepted the install prompt');\n        } else {\n            console.log('User dismissed the install prompt');\n        }\n        setDeferredPrompt(null);\n        setShowInstallButton(false);\n    };\n    if (!showInstallButton) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleInstallClick,\n            className: \"glass-button px-4 py-3 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-download mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                \"Install App\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PWAInstaller.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/dataService.ts":
/*!********************************!*\
  !*** ./src/lib/dataService.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   FIELD_NAMES: () => (/* binding */ FIELD_NAMES),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   addTransaction: () => (/* binding */ addTransaction),\n/* harmony export */   checkQuickVideoAdvantageActive: () => (/* binding */ checkQuickVideoAdvantageActive),\n/* harmony export */   checkReferralCodeExists: () => (/* binding */ checkReferralCodeExists),\n/* harmony export */   checkWithdrawalAllowed: () => (/* binding */ checkWithdrawalAllowed),\n/* harmony export */   createWithdrawalRequest: () => (/* binding */ createWithdrawalRequest),\n/* harmony export */   deleteNotification: () => (/* binding */ deleteNotification),\n/* harmony export */   generateSequentialReferralCode: () => (/* binding */ generateSequentialReferralCode),\n/* harmony export */   generateSimpleReferralCode: () => (/* binding */ generateSimpleReferralCode),\n/* harmony export */   generateUniqueReferralCode: () => (/* binding */ generateUniqueReferralCode),\n/* harmony export */   getAllNotifications: () => (/* binding */ getAllNotifications),\n/* harmony export */   getBankDetails: () => (/* binding */ getBankDetails),\n/* harmony export */   getCurrentReferralCounter: () => (/* binding */ getCurrentReferralCounter),\n/* harmony export */   getPlanEarning: () => (/* binding */ getPlanEarning),\n/* harmony export */   getPlanValidityDays: () => (/* binding */ getPlanValidityDays),\n/* harmony export */   getPlanVideoDuration: () => (/* binding */ getPlanVideoDuration),\n/* harmony export */   getReferralBonus: () => (/* binding */ getReferralBonus),\n/* harmony export */   getReferrals: () => (/* binding */ getReferrals),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUnreadNotificationCount: () => (/* binding */ getUnreadNotificationCount),\n/* harmony export */   getUnreadNotifications: () => (/* binding */ getUnreadNotifications),\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   getUserNotifications: () => (/* binding */ getUserNotifications),\n/* harmony export */   getUserVideoSettings: () => (/* binding */ getUserVideoSettings),\n/* harmony export */   getUserWithdrawals: () => (/* binding */ getUserWithdrawals),\n/* harmony export */   getVideoCountData: () => (/* binding */ getVideoCountData),\n/* harmony export */   getWalletData: () => (/* binding */ getWalletData),\n/* harmony export */   grantQuickVideoAdvantage: () => (/* binding */ grantQuickVideoAdvantage),\n/* harmony export */   hasPendingWithdrawals: () => (/* binding */ hasPendingWithdrawals),\n/* harmony export */   hasUnreadNotifications: () => (/* binding */ hasUnreadNotifications),\n/* harmony export */   isNotificationRead: () => (/* binding */ isNotificationRead),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead),\n/* harmony export */   processReferralBonus: () => (/* binding */ processReferralBonus),\n/* harmony export */   removeQuickVideoAdvantage: () => (/* binding */ removeQuickVideoAdvantage),\n/* harmony export */   saveBankDetails: () => (/* binding */ saveBankDetails),\n/* harmony export */   updateUserData: () => (/* binding */ updateUserData),\n/* harmony export */   updateUserPlanExpiry: () => (/* binding */ updateUserPlanExpiry),\n/* harmony export */   updateUserVideoDuration: () => (/* binding */ updateUserVideoDuration),\n/* harmony export */   updateVideoCount: () => (/* binding */ updateVideoCount),\n/* harmony export */   updateWalletBalance: () => (/* binding */ updateWalletBalance)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\n// Field names for Firestore collections\nconst FIELD_NAMES = {\n    // User fields\n    name: 'name',\n    email: 'email',\n    mobile: 'mobile',\n    referralCode: 'referralCode',\n    referredBy: 'referredBy',\n    referralBonusCredited: 'referralBonusCredited',\n    plan: 'plan',\n    planExpiry: 'planExpiry',\n    activeDays: 'activeDays',\n    joinedDate: 'joinedDate',\n    // Wallet fields\n    wallet: 'wallet',\n    // Bank details fields\n    bankAccountHolderName: 'bankAccountHolderName',\n    bankAccountNumber: 'bankAccountNumber',\n    bankIfscCode: 'bankIfscCode',\n    bankName: 'bankName',\n    bankDetailsUpdated: 'bankDetailsUpdated',\n    // Video fields\n    totalVideos: 'totalVideos',\n    todayVideos: 'todayVideos',\n    lastVideoDate: 'lastVideoDate',\n    videoDuration: 'videoDuration',\n    // Quick Video Advantage fields\n    quickVideoAdvantage: 'quickVideoAdvantage',\n    quickVideoAdvantageExpiry: 'quickVideoAdvantageExpiry',\n    quickVideoAdvantageDays: 'quickVideoAdvantageDays',\n    quickVideoAdvantageSeconds: 'quickVideoAdvantageSeconds',\n    quickVideoAdvantageGrantedBy: 'quickVideoAdvantageGrantedBy',\n    quickVideoAdvantageGrantedAt: 'quickVideoAdvantageGrantedAt',\n    // Transaction fields\n    type: 'type',\n    amount: 'amount',\n    date: 'date',\n    status: 'status',\n    description: 'description',\n    userId: 'userId'\n};\n// Collection names\nconst COLLECTIONS = {\n    users: 'users',\n    transactions: 'transactions',\n    withdrawals: 'withdrawals',\n    plans: 'plans',\n    settings: 'settings',\n    notifications: 'notifications',\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Get user data\nasync function getUserData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserData:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Ensure all values are properly typed\n            const result = {\n                name: String(data[FIELD_NAMES.name] || ''),\n                email: String(data[FIELD_NAMES.email] || ''),\n                mobile: String(data[FIELD_NAMES.mobile] || ''),\n                referralCode: String(data[FIELD_NAMES.referralCode] || ''),\n                referredBy: String(data[FIELD_NAMES.referredBy] || ''),\n                plan: String(data[FIELD_NAMES.plan] || 'Trial'),\n                planExpiry: data[FIELD_NAMES.planExpiry]?.toDate() || null,\n                activeDays: Number(data[FIELD_NAMES.activeDays] || 0),\n                joinedDate: data[FIELD_NAMES.joinedDate]?.toDate() || new Date(),\n                videoDuration: Number(data[FIELD_NAMES.videoDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)),\n                // Quick Video Advantage fields\n                quickVideoAdvantage: Boolean(data[FIELD_NAMES.quickVideoAdvantage] || false),\n                quickVideoAdvantageExpiry: data[FIELD_NAMES.quickVideoAdvantageExpiry]?.toDate() || null,\n                quickVideoAdvantageDays: Number(data[FIELD_NAMES.quickVideoAdvantageDays] || 0),\n                quickVideoAdvantageSeconds: Number(data[FIELD_NAMES.quickVideoAdvantageSeconds] || 30),\n                quickVideoAdvantageGrantedBy: String(data[FIELD_NAMES.quickVideoAdvantageGrantedBy] || ''),\n                quickVideoAdvantageGrantedAt: data[FIELD_NAMES.quickVideoAdvantageGrantedAt]?.toDate() || null\n            };\n            console.log('getUserData result:', result);\n            return result;\n        }\n        return null;\n    } catch (error) {\n        console.error('Error getting user data:', error);\n        return null // Return null instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get wallet data\nasync function getWalletData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getWalletData:', userId);\n            return {\n                wallet: 0\n            };\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const result = {\n                wallet: Number(data[FIELD_NAMES.wallet] || 0)\n            };\n            console.log('getWalletData result:', result);\n            return result;\n        }\n        return {\n            wallet: 0\n        };\n    } catch (error) {\n        console.error('Error getting wallet data:', error);\n        return {\n            wallet: 0\n        } // Return default instead of throwing\n        ;\n    }\n}\n// Get video count data\nasync function getVideoCountData(userId) {\n    try {\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const totalVideos = data[FIELD_NAMES.totalVideos] || 0;\n            const todayVideos = data[FIELD_NAMES.todayVideos] || 0;\n            const lastVideoDate = data[FIELD_NAMES.lastVideoDate]?.toDate();\n            // Check if it's a new day\n            const today = new Date();\n            const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n            return {\n                totalVideos,\n                todayVideos: isNewDay ? 0 : todayVideos,\n                remainingVideos: Math.max(0, 50 - (isNewDay ? 0 : todayVideos))\n            };\n        }\n        return {\n            totalVideos: 0,\n            todayVideos: 0,\n            remainingVideos: 50\n        };\n    } catch (error) {\n        console.error('Error getting video count data:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUserData(userId, data) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), data);\n    } catch (error) {\n        console.error('Error updating user data:', error);\n        throw error;\n    }\n}\n// Add transaction\nasync function addTransaction(userId, transactionData) {\n    try {\n        const transaction = {\n            [FIELD_NAMES.userId]: userId,\n            [FIELD_NAMES.type]: transactionData.type,\n            [FIELD_NAMES.amount]: transactionData.amount,\n            [FIELD_NAMES.description]: transactionData.description,\n            [FIELD_NAMES.status]: transactionData.status || 'completed',\n            [FIELD_NAMES.date]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), transaction);\n    } catch (error) {\n        console.error('Error adding transaction:', error);\n        throw error;\n    }\n}\n// Get transactions\nasync function getTransactions(userId, limitCount = 10) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getTransactions:', userId);\n            return [];\n        }\n        // Temporary fix: Use only where clause without orderBy to avoid index requirement\n        // TODO: Create composite index in Firebase console for better performance\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.userId, '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data()[FIELD_NAMES.date]?.toDate()\n            }));\n        // Sort in memory since we can't use orderBy without index\n        transactions.sort((a, b)=>{\n            const dateA = a.date || new Date(0);\n            const dateB = b.date || new Date(0);\n            return dateB.getTime() - dateA.getTime() // Descending order\n            ;\n        });\n        return transactions;\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        return [] // Return empty array instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get referrals\nasync function getReferrals(referralCode) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referredBy, '==', referralCode));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting referrals:', error);\n        throw error;\n    }\n}\n// Update video count\nasync function updateVideoCount(userId) {\n    try {\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.todayVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n        });\n    } catch (error) {\n        console.error('Error updating video count:', error);\n        throw error;\n    }\n}\n// Update wallet balance\nasync function updateWalletBalance(userId, amount) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.wallet]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(amount)\n        });\n    } catch (error) {\n        console.error('Error updating wallet balance:', error);\n        throw error;\n    }\n}\n// Save bank details\nasync function saveBankDetails(userId, bankDetails) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            throw new Error('Invalid userId provided');\n        }\n        // Validate bank details\n        validateBankDetails(bankDetails);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),\n            [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),\n            [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),\n            [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),\n            [FIELD_NAMES.bankDetailsUpdated]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        });\n        console.log('Bank details saved successfully for user:', userId);\n    } catch (error) {\n        console.error('Error saving bank details:', error);\n        throw error;\n    }\n}\n// Get bank details\nasync function getBankDetails(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getBankDetails:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Check if bank details exist\n            if (data[FIELD_NAMES.bankAccountNumber]) {\n                const result = {\n                    accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),\n                    accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),\n                    ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),\n                    bankName: String(data[FIELD_NAMES.bankName] || '')\n                };\n                console.log('getBankDetails result found');\n                return result;\n            }\n        }\n        console.log('No bank details found for user');\n        return null;\n    } catch (error) {\n        console.error('Error getting bank details:', error);\n        return null;\n    }\n}\n// Get plan-based earning amount (per batch of 50 videos)\nfunction getPlanEarning(plan) {\n    const planEarnings = {\n        'Trial': 10,\n        'Starter': 25,\n        'Basic': 75,\n        'Premium': 150,\n        'Gold': 200,\n        'Platinum': 250,\n        'Diamond': 400\n    };\n    return planEarnings[plan] || 10 // Default to trial earning (per batch of 50 videos)\n    ;\n}\n// Get plan-based video duration (in seconds)\nfunction getPlanVideoDuration(plan) {\n    const planDurations = {\n        'Trial': 30,\n        'Starter': 300,\n        'Basic': 300,\n        'Premium': 300,\n        'Gold': 180,\n        'Platinum': 120,\n        'Diamond': 60 // 1 minute (Rs 9999 plan)\n    };\n    return planDurations[plan] || 30 // Default to trial duration (30 seconds)\n    ;\n}\n// Get plan validity duration in days\nfunction getPlanValidityDays(plan) {\n    const planValidityDays = {\n        'Trial': 2,\n        'Starter': 30,\n        'Basic': 30,\n        'Premium': 30,\n        'Gold': 30,\n        'Platinum': 30,\n        'Diamond': 30,\n        '499': 30,\n        '1499': 30,\n        '2999': 30,\n        '3999': 30,\n        '5999': 30,\n        '9999': 30 // Legacy plan mapping\n    };\n    return planValidityDays[plan] || 2 // Default to trial duration (2 days)\n    ;\n}\n// Check if user's plan is expired based on active days and plan validity\nasync function isUserPlanExpired(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                expired: true,\n                reason: 'User data not found'\n            };\n        }\n        // If user is on Trial plan, check based on joined date\n        if (userData.plan === 'Trial') {\n            const joinedDate = userData.joinedDate || new Date();\n            const today = new Date();\n            const daysSinceJoined = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n            const trialDaysLeft = Math.max(0, 2 - daysSinceJoined);\n            return {\n                expired: trialDaysLeft <= 0,\n                reason: trialDaysLeft <= 0 ? 'Trial period expired' : undefined,\n                daysLeft: trialDaysLeft,\n                activeDays: daysSinceJoined\n            };\n        }\n        // For paid plans, check if planExpiry is set\n        if (userData.planExpiry) {\n            const today = new Date();\n            const expired = today > userData.planExpiry;\n            const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                expired,\n                reason: expired ? 'Plan subscription expired' : undefined,\n                daysLeft,\n                activeDays: userData.activeDays || 0\n            };\n        }\n        // If planExpiry is not set, calculate based on active days and plan validity\n        const planValidityDays = getPlanValidityDays(userData.plan);\n        const currentActiveDays = userData.activeDays || 0;\n        const daysLeft = Math.max(0, planValidityDays - currentActiveDays);\n        const expired = daysLeft <= 0;\n        return {\n            expired,\n            reason: expired ? `Plan validity period (${planValidityDays} days) exceeded based on active days` : undefined,\n            daysLeft,\n            activeDays: currentActiveDays\n        };\n    } catch (error) {\n        console.error('Error checking plan expiry:', error);\n        return {\n            expired: true,\n            reason: 'Error checking plan status'\n        };\n    }\n}\n// Update user's plan expiry when admin changes plan\nasync function updateUserPlanExpiry(userId, newPlan, customExpiryDate) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        if (newPlan === 'Trial') {\n            // Trial plan doesn't have expiry, it's based on joined date\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: null\n            });\n        } else {\n            // Set expiry date for paid plans\n            let expiryDate;\n            if (customExpiryDate) {\n                expiryDate = customExpiryDate;\n            } else {\n                // Calculate expiry based on plan validity\n                const validityDays = getPlanValidityDays(newPlan);\n                const today = new Date();\n                expiryDate = new Date(today.getTime() + validityDays * 24 * 60 * 60 * 1000);\n            }\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiryDate)\n            });\n            console.log(`Updated plan expiry for user ${userId} to ${expiryDate.toDateString()}`);\n        }\n    } catch (error) {\n        console.error('Error updating plan expiry:', error);\n        throw error;\n    }\n}\n// Get referral bonus based on plan\nfunction getReferralBonus(plan) {\n    const referralBonuses = {\n        'Trial': 0,\n        '499': 50,\n        '1499': 150,\n        '2999': 300,\n        '3999': 400,\n        '5999': 700,\n        '9999': 1200,\n        'Starter': 50,\n        'Basic': 150,\n        'Premium': 300,\n        'Gold': 400,\n        'Platinum': 700,\n        'Diamond': 1200\n    };\n    return referralBonuses[plan] || 0;\n}\n// Process referral bonus when admin upgrades user from Trial to paid plan\nasync function processReferralBonus(userId, oldPlan, newPlan) {\n    try {\n        // Only process bonus when upgrading FROM Trial TO a paid plan\n        if (oldPlan !== 'Trial' || newPlan === 'Trial') {\n            console.log('Referral bonus only applies when upgrading from Trial to paid plan');\n            return;\n        }\n        console.log(`Processing referral bonus for user ${userId} upgrading from ${oldPlan} to ${newPlan}`);\n        // Get the user's data to find their referral info\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (!userDoc.exists()) {\n            console.log('User not found');\n            return;\n        }\n        const userData = userDoc.data();\n        const referredBy = userData[FIELD_NAMES.referredBy];\n        const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited];\n        if (!referredBy) {\n            console.log('User was not referred by anyone, skipping bonus processing');\n            return;\n        }\n        if (alreadyCredited) {\n            console.log('Referral bonus already credited for this user, skipping');\n            return;\n        }\n        console.log('Finding referrer with code:', referredBy);\n        // Find the referrer by referral code\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', referredBy), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        if (querySnapshot.empty) {\n            console.log('Referral code not found:', referredBy);\n            return;\n        }\n        const referrerDoc = querySnapshot.docs[0];\n        const referrerId = referrerDoc.id;\n        const bonusAmount = getReferralBonus(newPlan);\n        console.log(`Found referrer: ${referrerId}, bonus amount: ₹${bonusAmount}`);\n        if (bonusAmount > 0) {\n            // Add bonus to referrer's wallet\n            await updateWalletBalance(referrerId, bonusAmount);\n            // Add 50 videos to referrer's total video count\n            const referrerRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, referrerId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(referrerRef, {\n                [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(50)\n            });\n            // Mark referral bonus as credited for this user\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.referralBonusCredited]: true\n            });\n            // Add transaction record for referral bonus\n            await addTransaction(referrerId, {\n                type: 'referral_bonus',\n                amount: bonusAmount,\n                description: `Referral bonus for ${newPlan} plan upgrade + 50 bonus videos (User: ${userData[FIELD_NAMES.name]})`\n            });\n            console.log(`✅ Referral bonus processed: ₹${bonusAmount} + 50 videos for referrer ${referrerId}`);\n        } else {\n            console.log('No bonus amount calculated, skipping');\n        }\n    } catch (error) {\n        console.error('❌ Error processing referral bonus:', error);\n    // Don't throw error to avoid breaking plan update\n    }\n}\n// Get user video settings (duration and earning per batch)\nasync function getUserVideoSettings(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                videoDuration: 30,\n                earningPerBatch: 10,\n                plan: 'Trial',\n                hasQuickAdvantage: false\n            };\n        }\n        // Check if user has active quick video advantage\n        const hasActiveQuickAdvantage = checkQuickVideoAdvantageActive(userData);\n        let videoDuration = userData.videoDuration;\n        // If user has active quick video advantage, use custom seconds or default to 30\n        if (hasActiveQuickAdvantage) {\n            videoDuration = userData.quickVideoAdvantageSeconds || 30 // Use custom duration or default to 30 seconds\n            ;\n        } else {\n            // Use plan-based video duration, but allow admin overrides for non-trial users\n            if (!videoDuration || userData.plan === 'Trial') {\n                videoDuration = getPlanVideoDuration(userData.plan);\n            }\n        }\n        return {\n            videoDuration: videoDuration,\n            earningPerBatch: getPlanEarning(userData.plan),\n            plan: userData.plan,\n            hasQuickAdvantage: hasActiveQuickAdvantage,\n            quickAdvantageExpiry: userData.quickVideoAdvantageExpiry\n        };\n    } catch (error) {\n        console.error('Error getting user video settings:', error);\n        return {\n            videoDuration: 30,\n            earningPerBatch: 10,\n            plan: 'Trial',\n            hasQuickAdvantage: false\n        };\n    }\n}\n// Check if user has active quick video advantage\nfunction checkQuickVideoAdvantageActive(userData) {\n    if (!userData.quickVideoAdvantage || !userData.quickVideoAdvantageExpiry) {\n        return false;\n    }\n    const now = new Date();\n    const expiry = userData.quickVideoAdvantageExpiry;\n    return now < expiry;\n}\n// Grant quick video advantage to user (admin function)\nasync function grantQuickVideoAdvantage(userId, days, grantedBy, seconds = 30) {\n    try {\n        if (days <= 0 || days > 365) {\n            throw new Error('Days must be between 1 and 365');\n        }\n        if (seconds < 1 || seconds > 420) {\n            throw new Error('Seconds must be between 1 and 420 (7 minutes)');\n        }\n        const now = new Date();\n        const expiry = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: true,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiry),\n            [FIELD_NAMES.quickVideoAdvantageDays]: days,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: seconds,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: grantedBy,\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(now)\n        });\n        console.log(`Granted quick video advantage to user ${userId} for ${days} days until ${expiry.toDateString()}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_granted',\n            amount: 0,\n            description: `Quick video advantage granted for ${days} days by ${grantedBy}`\n        });\n        return {\n            success: true,\n            expiry\n        };\n    } catch (error) {\n        console.error('Error granting quick video advantage:', error);\n        throw error;\n    }\n}\n// Remove quick video advantage from user (admin function)\nasync function removeQuickVideoAdvantage(userId, removedBy) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: false,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: null,\n            [FIELD_NAMES.quickVideoAdvantageDays]: 0,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: 30,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: '',\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: null\n        });\n        console.log(`Removed quick video advantage from user ${userId}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_removed',\n            amount: 0,\n            description: `Quick video advantage removed by ${removedBy}`\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error removing quick video advantage:', error);\n        throw error;\n    }\n}\n// Update user video duration (admin function)\nasync function updateUserVideoDuration(userId, durationInSeconds) {\n    try {\n        // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)\n        const isQuickDuration = [\n            1,\n            10,\n            30\n        ].includes(durationInSeconds);\n        const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420;\n        if (!isQuickDuration && !isStandardDuration) {\n            throw new Error('Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration');\n        }\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.videoDuration]: durationInSeconds\n        });\n        console.log(`Updated video duration for user ${userId} to ${durationInSeconds} seconds`);\n    } catch (error) {\n        console.error('Error updating user video duration:', error);\n        throw error;\n    }\n}\n// Validate bank details\nfunction validateBankDetails(bankDetails) {\n    const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails;\n    if (!accountHolderName || accountHolderName.trim().length < 2) {\n        throw new Error('Account holder name must be at least 2 characters long');\n    }\n    if (!accountNumber || !/^\\d{9,18}$/.test(accountNumber.trim())) {\n        throw new Error('Account number must be 9-18 digits');\n    }\n    if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {\n        throw new Error('Invalid IFSC code format (e.g., SBIN0001234)');\n    }\n    if (!bankName || bankName.trim().length < 2) {\n        throw new Error('Bank name must be at least 2 characters long');\n    }\n}\n// Add notification (admin function) - All notifications are now blocking\nasync function addNotification(notification) {\n    try {\n        const notificationData = {\n            title: notification.title,\n            message: notification.message,\n            type: notification.type,\n            targetUsers: notification.targetUsers,\n            userIds: notification.userIds || [],\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdBy: notification.createdBy\n        };\n        console.log('Adding notification to Firestore:', notificationData);\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), notificationData);\n        console.log('Notification added successfully with ID:', docRef.id);\n        // Verify the notification was added\n        const addedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n        if (addedDoc.exists()) {\n            console.log('Notification verified in database:', addedDoc.data());\n        } else {\n            console.warn('Notification not found after adding');\n        }\n        return docRef.id;\n    } catch (error) {\n        console.error('Error adding notification:', error);\n        throw error;\n    }\n}\n// Get notifications for a user\nasync function getUserNotifications(userId, limitCount = 20) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading notifications for user: ${userId}`);\n        // Try to get notifications with fallback for indexing issues\n        let allUsersSnapshot, specificUserSnapshot;\n        try {\n            // Get notifications targeted to all users\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n            console.log(`Found ${allUsersSnapshot.docs.length} notifications for all users`);\n        } catch (error) {\n            console.warn('Error querying all users notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n        }\n        try {\n            // Get notifications targeted to specific user\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n            console.log(`Found ${specificUserSnapshot.docs.length} notifications for specific user`);\n        } catch (error) {\n            console.warn('Error querying specific user notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n        }\n        const notifications = [];\n        // Process all users notifications\n        allUsersSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Process specific user notifications\n        specificUserSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Sort by creation date (newest first)\n        notifications.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        const finalNotifications = notifications.slice(0, limitCount);\n        console.log(`Returning ${finalNotifications.length} total notifications for user`);\n        return finalNotifications;\n    } catch (error) {\n        console.error('Error getting user notifications:', error);\n        return [];\n    }\n}\n// Get all notifications (admin function)\nasync function getAllNotifications(limitCount = 50) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const notifications = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            }));\n        return notifications;\n    } catch (error) {\n        console.error('Error getting all notifications:', error);\n        return [];\n    }\n}\n// Delete notification (admin function)\nasync function deleteNotification(notificationId) {\n    try {\n        if (!notificationId || typeof notificationId !== 'string') {\n            throw new Error('Invalid notification ID provided');\n        }\n        console.log('Deleting notification:', notificationId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications, notificationId));\n        console.log('Notification deleted successfully');\n    } catch (error) {\n        console.error('Error deleting notification:', error);\n        throw error;\n    }\n}\n// Mark notification as read\nasync function markNotificationAsRead(notificationId, userId) {\n    try {\n        // For now, we'll store read status in localStorage since it's user-specific\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        if (!readNotifications.includes(notificationId)) {\n            readNotifications.push(notificationId);\n            localStorage.setItem(`read_notifications_${userId}`, JSON.stringify(readNotifications));\n        }\n    } catch (error) {\n        console.error('Error marking notification as read:', error);\n    }\n}\n// Check if notification is read\nfunction isNotificationRead(notificationId, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return readNotifications.includes(notificationId);\n    } catch (error) {\n        console.error('Error checking notification read status:', error);\n        return false;\n    }\n}\n// Get unread notification count\nfunction getUnreadNotificationCount(notifications, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return notifications.filter((notification)=>!readNotifications.includes(notification.id)).length;\n    } catch (error) {\n        console.error('Error getting unread notification count:', error);\n        return 0;\n    }\n}\n// Get unread notifications - All notifications are now blocking\nasync function getUnreadNotifications(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUnreadNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading unread notifications for user: ${userId}`);\n        // Get all notifications for the user\n        const allNotifications = await getUserNotifications(userId, 50);\n        // Filter for unread notifications\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        const unreadNotifications = allNotifications.filter((notification)=>notification.id && !readNotifications.includes(notification.id));\n        console.log(`Found ${unreadNotifications.length} unread notifications`);\n        return unreadNotifications;\n    } catch (error) {\n        console.error('Error getting unread notifications:', error);\n        return [];\n    }\n}\n// Check if user has unread notifications\nasync function hasUnreadNotifications(userId) {\n    try {\n        const unreadNotifications = await getUnreadNotifications(userId);\n        return unreadNotifications.length > 0;\n    } catch (error) {\n        console.error('Error checking for unread notifications:', error);\n        return false;\n    }\n}\n// Check if user has pending withdrawals\nasync function hasPendingWithdrawals(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking pending withdrawals:', error);\n        return false;\n    }\n}\n// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)\nasync function checkWithdrawalAllowed(userId) {\n    try {\n        // Check user plan first\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                allowed: false,\n                reason: 'Unable to verify user information. Please try again.'\n            };\n        }\n        // Check if user is on trial plan\n        if (userData.plan === 'Trial') {\n            return {\n                allowed: false,\n                reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'\n            };\n        }\n        // Check if user has pending withdrawals\n        const hasPending = await hasPendingWithdrawals(userId);\n        if (hasPending) {\n            return {\n                allowed: false,\n                reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'\n            };\n        }\n        const now = new Date();\n        const currentHour = now.getHours();\n        // Check time restrictions (10 AM to 6 PM)\n        if (currentHour < 10 || currentHour >= 18) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'\n            };\n        }\n        // Check admin leave day\n        const { isAdminLeaveDay } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(now);\n        if (isAdminLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on admin leave/holiday days'\n            };\n        }\n        // Check user leave day\n        const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isUserLeave = await isUserOnLeave(userId, now);\n        if (isUserLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on your leave days'\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking withdrawal allowed:', error);\n        return {\n            allowed: false,\n            reason: 'Unable to verify withdrawal eligibility. Please try again.'\n        };\n    }\n}\n// Create withdrawal request\nasync function createWithdrawalRequest(userId, amount, bankDetails) {\n    try {\n        // Check minimum withdrawal amount\n        if (amount < 50) {\n            throw new Error('Minimum withdrawal amount is ₹50');\n        }\n        // Check if withdrawal is allowed\n        const withdrawalCheck = await checkWithdrawalAllowed(userId);\n        if (!withdrawalCheck.allowed) {\n            throw new Error(withdrawalCheck.reason);\n        }\n        // Check if user has sufficient balance\n        const walletData = await getWalletData(userId);\n        if (walletData.wallet < amount) {\n            throw new Error('Insufficient wallet balance');\n        }\n        // Debit the amount from user's wallet immediately\n        await updateWalletBalance(userId, -amount);\n        // Add transaction record for withdrawal debit\n        await addTransaction(userId, {\n            type: 'withdrawal_request',\n            amount: -amount,\n            description: `Withdrawal request submitted - ₹${amount} debited from wallet`\n        });\n        const withdrawalData = {\n            userId,\n            amount,\n            bankDetails,\n            status: 'pending',\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), withdrawalData);\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating withdrawal request:', error);\n        throw error;\n    }\n}\n// Get user withdrawals\nasync function getUserWithdrawals(userId, limitCount = 20) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting user withdrawals:', error);\n        return [];\n    }\n}\n// Generate sequential referral code - using highest existing number + 1 approach\nasync function generateSequentialReferralCode() {\n    try {\n        console.log('Generating sequential referral code...');\n        // Primary Method: Find the highest existing MYN code and increment\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            // Get all MYN codes and find the highest number\n            const mynCodesQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '>=', 'MYN0000'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '<=', 'MYN9999'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(FIELD_NAMES.referralCode, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n            const mynCodesSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(mynCodesQuery);\n            let nextNumber = 1;\n            if (!mynCodesSnapshot.empty) {\n                const highestCode = mynCodesSnapshot.docs[0].data()[FIELD_NAMES.referralCode];\n                console.log('Highest existing MYN code:', highestCode);\n                if (highestCode && highestCode.startsWith('MYN')) {\n                    const numberPart = highestCode.substring(3);\n                    const currentNumber = parseInt(numberPart);\n                    if (!isNaN(currentNumber)) {\n                        nextNumber = currentNumber + 1;\n                        console.log(`Next sequential number: ${nextNumber}`);\n                    }\n                }\n            } else {\n                console.log('No existing MYN codes found, starting from MYN0001');\n            }\n            const code = `MYN${String(nextNumber).padStart(4, '0')}`;\n            console.log(`Generated sequential referral code: ${code}`);\n            return code;\n        } catch (sequentialError) {\n            console.log('Sequential method failed, trying fallback:', sequentialError);\n        }\n        // Fallback Method: Use user count + 1\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getCountFromServer)(usersCollection);\n            const count = snapshot.data().count + 1;\n            const fallbackCode = `MYN${String(count).padStart(4, '0')}`;\n            console.log(`Using count-based fallback code: ${fallbackCode}`);\n            return fallbackCode;\n        } catch (countError) {\n            console.log('Count method also failed, using random fallback:', countError);\n        }\n        // Final Fallback: Random code\n        const randomCode = `MYN${Math.floor(1000 + Math.random() * 9000)}`;\n        console.log('Using random fallback referral code:', randomCode);\n        return randomCode;\n    } catch (error) {\n        console.error('Error generating referral code:', error);\n        // Ultimate fallback\n        const emergencyCode = `MYN${Math.floor(1000 + Math.random() * 9000)}`;\n        console.log('Using emergency fallback referral code:', emergencyCode);\n        return emergencyCode;\n    }\n}\n// Check if referral code exists\nasync function checkReferralCodeExists(code) {\n    try {\n        const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n        const codeQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', code), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(codeQuery);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking referral code:', error);\n        return false;\n    }\n}\n// Generate unique referral code with retry logic\nasync function generateUniqueReferralCode(maxRetries = 5) {\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            const code = await generateSequentialReferralCode();\n            const exists = await checkReferralCodeExists(code);\n            if (!exists) {\n                console.log(`Generated unique referral code: ${code} (attempt ${attempt})`);\n                return code;\n            } else {\n                console.log(`Code ${code} already exists, retrying... (attempt ${attempt})`);\n            }\n        } catch (error) {\n            console.error(`Error in attempt ${attempt}:`, error);\n        }\n    }\n    // Final fallback with timestamp to ensure uniqueness\n    const timestamp = Date.now().toString().slice(-4);\n    const fallbackCode = `MYN${timestamp}`;\n    console.log(`Using timestamp-based fallback code: ${fallbackCode}`);\n    return fallbackCode;\n}\n// Get current referral counter (for admin purposes)\nasync function getCurrentReferralCounter() {\n    try {\n        const counterRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'system', 'referralCounter');\n        const counterDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(counterRef);\n        if (counterDoc.exists()) {\n            return counterDoc.data().lastNumber || 0;\n        }\n        return 0;\n    } catch (error) {\n        console.error('Error getting referral counter:', error);\n        return 0;\n    }\n}\n// Simple referral code generation for registration (no database queries to avoid conflicts)\nfunction generateSimpleReferralCode() {\n    // Use current timestamp + random string for better uniqueness\n    const timestamp = Date.now().toString();\n    const randomPart = Math.random().toString(36).substring(2, 8).toUpperCase();\n    // Create a more unique code using full timestamp with MYN prefix\n    const uniqueCode = `MYN${timestamp.slice(-6)}${randomPart.substring(0, 1)}`;\n    console.log('Generated simple referral code:', uniqueCode);\n    return uniqueCode;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/dataService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ\",\n    authDomain: \"mytube-india.firebaseapp.com\",\n    projectId: \"mytube-india\",\n    storageBucket: \"mytube-india.firebasestorage.app\",\n    messagingSenderId: \"************\",\n    appId: \"1:************:web:ebedaec6a492926af2056a\",\n    measurementId: \"G-R24C6N7CWJ\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)() : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2ZpcmViYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTZEO0FBQ3RCO0FBQ1U7QUFDSjtBQUU3QyxNQUFNTSxpQkFBaUI7SUFDckJDLFFBQVFDLHlDQUF3QztJQUNoREcsWUFBWUgsOEJBQTRDO0lBQ3hESyxXQUFXTCxjQUEyQztJQUN0RE8sZUFBZVAsa0NBQStDO0lBQzlEUyxtQkFBbUJULGNBQW9EO0lBQ3ZFVyxPQUFPWCwyQ0FBdUM7SUFDOUNhLGVBQWViLGNBQStDO0FBQ2hFO0FBRUEsc0JBQXNCO0FBQ3RCLE1BQU1lLE1BQU10QixxREFBT0EsR0FBR3VCLE1BQU0sR0FBR3RCLG9EQUFNQSxLQUFLRiwyREFBYUEsQ0FBQ007QUFFeEQsK0JBQStCO0FBQ3hCLE1BQU1tQixPQUFPdEIsc0RBQU9BLENBQUNvQixLQUFJO0FBQ3pCLE1BQU1HLEtBQUt0QixnRUFBWUEsQ0FBQ21CLEtBQUk7QUFDNUIsTUFBTUksVUFBVXRCLDREQUFVQSxDQUFDa0IsS0FBSTtBQUV0QyxpRUFBZUEsR0FBR0EsRUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBU1VTXFxPbmVEcml2ZVxcRGVza3RvcFxcTVkgUFJPSkVDVFNcXE5vZGUgTXl0dWJlXFxzcmNcXGxpYlxcZmlyZWJhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW5pdGlhbGl6ZUFwcCwgZ2V0QXBwcywgZ2V0QXBwIH0gZnJvbSAnZmlyZWJhc2UvYXBwJ1xuaW1wb3J0IHsgZ2V0QXV0aCB9IGZyb20gJ2ZpcmViYXNlL2F1dGgnXG5pbXBvcnQgeyBnZXRGaXJlc3RvcmUgfSBmcm9tICdmaXJlYmFzZS9maXJlc3RvcmUnXG5pbXBvcnQgeyBnZXRTdG9yYWdlIH0gZnJvbSAnZmlyZWJhc2Uvc3RvcmFnZSdcblxuY29uc3QgZmlyZWJhc2VDb25maWcgPSB7XG4gIGFwaUtleTogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfQVBJX0tFWSxcbiAgYXV0aERvbWFpbjogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfQVVUSF9ET01BSU4sXG4gIHByb2plY3RJZDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfUFJPSkVDVF9JRCxcbiAgc3RvcmFnZUJ1Y2tldDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfU1RPUkFHRV9CVUNLRVQsXG4gIG1lc3NhZ2luZ1NlbmRlcklkOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9NRVNTQUdJTkdfU0VOREVSX0lELFxuICBhcHBJZDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfQVBQX0lELFxuICBtZWFzdXJlbWVudElkOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9NRUFTVVJFTUVOVF9JRCxcbn1cblxuLy8gSW5pdGlhbGl6ZSBGaXJlYmFzZVxuY29uc3QgYXBwID0gZ2V0QXBwcygpLmxlbmd0aCA/IGdldEFwcCgpIDogaW5pdGlhbGl6ZUFwcChmaXJlYmFzZUNvbmZpZylcblxuLy8gSW5pdGlhbGl6ZSBGaXJlYmFzZSBzZXJ2aWNlc1xuZXhwb3J0IGNvbnN0IGF1dGggPSBnZXRBdXRoKGFwcClcbmV4cG9ydCBjb25zdCBkYiA9IGdldEZpcmVzdG9yZShhcHApXG5leHBvcnQgY29uc3Qgc3RvcmFnZSA9IGdldFN0b3JhZ2UoYXBwKVxuXG5leHBvcnQgZGVmYXVsdCBhcHBcbiJdLCJuYW1lcyI6WyJpbml0aWFsaXplQXBwIiwiZ2V0QXBwcyIsImdldEFwcCIsImdldEF1dGgiLCJnZXRGaXJlc3RvcmUiLCJnZXRTdG9yYWdlIiwiZmlyZWJhc2VDb25maWciLCJhcGlLZXkiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfQVBJX0tFWSIsImF1dGhEb21haW4iLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9BVVRIX0RPTUFJTiIsInByb2plY3RJZCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX1BST0pFQ1RfSUQiLCJzdG9yYWdlQnVja2V0IiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfU1RPUkFHRV9CVUNLRVQiLCJtZXNzYWdpbmdTZW5kZXJJZCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX01FU1NBR0lOR19TRU5ERVJfSUQiLCJhcHBJZCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FQUF9JRCIsIm1lYXN1cmVtZW50SWQiLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9NRUFTVVJFTUVOVF9JRCIsImFwcCIsImxlbmd0aCIsImF1dGgiLCJkYiIsInN0b3JhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/@opentelemetry","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdebug-registration-simple%2Fpage&page=%2Fdebug-registration-simple%2Fpage&appPaths=%2Fdebug-registration-simple%2Fpage&pagePath=private-next-app-dir%2Fdebug-registration-simple%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();