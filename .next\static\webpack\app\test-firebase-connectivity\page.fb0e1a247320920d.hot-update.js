"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-firebase-connectivity/page",{

/***/ "(app-pages-browser)/./src/app/test-firebase-connectivity/page.tsx":
/*!*****************************************************!*\
  !*** ./src/app/test-firebase-connectivity/page.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestFirebaseConnectivityPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TestFirebaseConnectivityPage() {\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const addToResult = (text)=>{\n        setResult((prev)=>prev + text + '\\n');\n    };\n    const testConnectivity = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        try {\n            addToResult('🌐 Testing Firebase Connectivity...\\n');\n            // Test 1: Check Firebase instances\n            addToResult('=== TEST 1: Firebase Instances ===');\n            addToResult(\"Auth instance: \".concat(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth ? '✅ Initialized' : '❌ Not initialized'));\n            addToResult(\"Firestore instance: \".concat(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db ? '✅ Initialized' : '❌ Not initialized'));\n            addToResult(\"Auth app: \".concat(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth.app ? '✅ Connected' : '❌ Not connected'));\n            addToResult(\"Firestore app: \".concat(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db.app ? '✅ Connected' : '❌ Not connected'));\n            // Test 2: Check environment variables\n            addToResult('\\n=== TEST 2: Environment Variables ===');\n            addToResult(\"API Key: \".concat( true ? '✅ Set' : 0));\n            addToResult(\"Auth Domain: \".concat( true ? '✅ Set' : 0));\n            addToResult(\"Project ID: \".concat( true ? '✅ Set' : 0));\n            addToResult(\"Project ID Value: \".concat(\"mytube-india\"));\n            // Test 3: Test anonymous authentication (simpler than email/password)\n            addToResult('\\n=== TEST 3: Anonymous Authentication ===');\n            try {\n                addToResult('Attempting anonymous sign-in...');\n                const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signInAnonymously)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n                addToResult(\"✅ Anonymous auth successful: \".concat(userCredential.user.uid));\n                // Test 4: Test Firestore write with anonymous user\n                addToResult('\\n=== TEST 4: Firestore Write Test ===');\n                const testDoc = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'connectivity_test', \"test_\".concat(Date.now()));\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)(testDoc, {\n                    test: true,\n                    timestamp: firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.Timestamp.now(),\n                    message: 'Connectivity test successful'\n                });\n                addToResult('✅ Firestore write successful');\n                // Test 5: Test Firestore read\n                addToResult('\\n=== TEST 5: Firestore Read Test ===');\n                const readDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(testDoc);\n                if (readDoc.exists()) {\n                    addToResult('✅ Firestore read successful');\n                    addToResult(\"   Data: \".concat(JSON.stringify(readDoc.data())));\n                } else {\n                    addToResult('❌ Firestore read failed - document not found');\n                }\n                // Sign out\n                await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n                addToResult('\\n✅ Signed out successfully');\n                addToResult('\\n🎉 ALL TESTS PASSED - Firebase connectivity is working!');\n                addToResult('The registration issue might be specific to email/password authentication.');\n            } catch (authError) {\n                addToResult(\"❌ Anonymous auth failed: \".concat(authError.message));\n                addToResult(\"   Error code: \".concat(authError.code));\n                if (authError.code === 'auth/network-request-failed') {\n                    addToResult('\\n🔧 NETWORK CONNECTIVITY ISSUE DETECTED:');\n                    addToResult('   1. Check your internet connection');\n                    addToResult('   2. Check if firewall/antivirus is blocking Firebase');\n                    addToResult('   3. Try using a different network (mobile hotspot)');\n                    addToResult('   4. Check if your ISP blocks Firebase services');\n                    addToResult('   5. Try using a VPN');\n                    addToResult('');\n                    addToResult('   Firebase domains that need to be accessible:');\n                    addToResult('   - firebase.google.com');\n                    addToResult('   - firestore.googleapis.com');\n                    addToResult('   - identitytoolkit.googleapis.com');\n                    addToResult('   - mytube-india.firebaseapp.com');\n                } else if (authError.code === 'auth/operation-not-allowed') {\n                    addToResult('\\n🔧 FIREBASE CONFIGURATION ISSUE:');\n                    addToResult('   1. Go to Firebase Console → Authentication → Sign-in method');\n                    addToResult('   2. Enable \"Anonymous\" authentication');\n                    addToResult('   3. Or enable \"Email/Password\" authentication');\n                }\n            }\n        } catch (error) {\n            addToResult(\"❌ Connectivity test failed: \".concat(error.message));\n            addToResult(\"   Error code: \".concat(error.code));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const testNetworkDiagnostics = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        try {\n            addToResult('🔍 Network Diagnostics...\\n');\n            // Test 1: Basic fetch to Firebase\n            addToResult('=== TEST 1: Firebase Domain Accessibility ===');\n            try {\n                const response = await fetch('https://firebase.google.com', {\n                    mode: 'no-cors'\n                });\n                addToResult('✅ Firebase.google.com is accessible');\n            } catch (fetchError) {\n                addToResult(\"❌ Firebase.google.com not accessible: \".concat(fetchError.message));\n            }\n            // Test 2: Project-specific domain\n            addToResult('\\n=== TEST 2: Project Domain Accessibility ===');\n            try {\n                const response = await fetch('https://mytube-india.firebaseapp.com', {\n                    mode: 'no-cors'\n                });\n                addToResult('✅ mytube-india.firebaseapp.com is accessible');\n            } catch (fetchError) {\n                addToResult(\"❌ mytube-india.firebaseapp.com not accessible: \".concat(fetchError.message));\n            }\n            // Test 3: Firestore API endpoint\n            addToResult('\\n=== TEST 3: Firestore API Accessibility ===');\n            try {\n                const response = await fetch('https://firestore.googleapis.com', {\n                    mode: 'no-cors'\n                });\n                addToResult('✅ firestore.googleapis.com is accessible');\n            } catch (fetchError) {\n                addToResult(\"❌ firestore.googleapis.com not accessible: \".concat(fetchError.message));\n            }\n            addToResult('\\n=== RECOMMENDATIONS ===');\n            addToResult('If any domains are not accessible:');\n            addToResult('1. Check your internet connection');\n            addToResult('2. Try disabling firewall/antivirus temporarily');\n            addToResult('3. Try using a different network (mobile hotspot)');\n            addToResult('4. Contact your ISP about Firebase access');\n            addToResult('5. Try using a VPN service');\n        } catch (error) {\n            addToResult(\"❌ Network diagnostics failed: \".concat(error.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const testSpecificUID = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        try {\n            addToResult('🔍 Testing Specific UID: b7690183-ab6b-4719-944d-c0a080a59e8c\\n');\n            // Test if this UID exists in Firestore\n            addToResult('=== Checking if UID exists in Firestore ===');\n            const specificUID = 'b7690183-ab6b-4719-944d-c0a080a59e8c';\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'users', specificUID);\n            try {\n                const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(userDocRef);\n                if (userDoc.exists()) {\n                    var _userData_joinedDate_toDate, _userData_joinedDate;\n                    const userData = userDoc.data();\n                    addToResult('✅ User document found!');\n                    addToResult(\"   Name: \".concat(userData.name || 'N/A'));\n                    addToResult(\"   Email: \".concat(userData.email || 'N/A'));\n                    addToResult(\"   Plan: \".concat(userData.plan || 'N/A'));\n                    addToResult(\"   Referral Code: \".concat(userData.referralCode || 'N/A'));\n                    addToResult(\"   Status: \".concat(userData.status || 'N/A'));\n                    addToResult(\"   Joined: \".concat(((_userData_joinedDate = userData.joinedDate) === null || _userData_joinedDate === void 0 ? void 0 : (_userData_joinedDate_toDate = _userData_joinedDate.toDate()) === null || _userData_joinedDate_toDate === void 0 ? void 0 : _userData_joinedDate_toDate.toLocaleString()) || 'N/A'));\n                    addToResult('\\n✅ This confirms Firestore is working!');\n                    addToResult('The registration issue might be specific to the registration flow.');\n                } else {\n                    addToResult('❌ User document not found');\n                    addToResult('This UID might be from Firebase Auth but Firestore document creation failed');\n                }\n            } catch (firestoreError) {\n                addToResult(\"❌ Firestore query failed: \".concat(firestoreError.message));\n                addToResult(\"   Error code: \".concat(firestoreError.code));\n                if (firestoreError.code === 'permission-denied') {\n                    addToResult('   This indicates Firestore security rules are blocking reads');\n                } else if (firestoreError.code === 'unavailable') {\n                    addToResult('   This indicates Firestore service is unavailable');\n                }\n            }\n        } catch (error) {\n            addToResult(\"❌ UID test failed: \".concat(error.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-white mb-6\",\n                    children: \"Firebase Connectivity Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testConnectivity,\n                                    disabled: isLoading,\n                                    className: \"btn-primary\",\n                                    children: isLoading ? 'Testing...' : 'Test Firebase Connectivity'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testNetworkDiagnostics,\n                                    disabled: isLoading,\n                                    className: \"btn-primary\",\n                                    children: isLoading ? 'Testing...' : 'Test Network Diagnostics'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testSpecificUID,\n                                    disabled: isLoading,\n                                    className: \"btn-primary\",\n                                    children: isLoading ? 'Testing...' : 'Test Specific UID'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testEmailPasswordAuth,\n                                    disabled: isLoading,\n                                    className: \"btn-primary\",\n                                    children: isLoading ? 'Testing...' : 'Test Email/Password Auth'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black/50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96\",\n                                children: result || 'Click a test button to start...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/register\",\n                        className: \"text-blue-400 hover:text-blue-300 underline\",\n                        children: \"← Back to Registration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(TestFirebaseConnectivityPage, \"TA2EjS24NWK+5U65aZ0FJpR3Amc=\");\n_c = TestFirebaseConnectivityPage;\nvar _c;\n$RefreshReg$(_c, \"TestFirebaseConnectivityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-firebase-connectivity/page.tsx\n"));

/***/ })

});