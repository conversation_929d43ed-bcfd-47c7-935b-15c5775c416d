(()=>{var e={};e.id=724,e.ids=[724],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1610:(e,r,t)=>{Promise.resolve().then(t.bind(t,87766))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13452:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),i=t(43210),o=t(75535),n=t(33784),a=t(3582);function d(){let[e,r]=(0,i.useState)(""),[t,d]=(0,i.useState)(!1),c=e=>{r(r=>r+e+"\n")},u=async()=>{r(""),d(!0);try{c("\uD83D\uDD0D Testing Referral Code Conflicts...\n"),c("=== TEST 1: Checking Existing Referral Codes ===");let e=(0,o.rJ)(n.db,a.COLLECTIONS.users),r=(0,o.P)(e),t=await (0,o.GG)(r),s=new Set,i=new Set;t.docs.forEach(e=>{let r=e.data()[a.FIELD_NAMES.referralCode];r&&(s.has(r)&&i.add(r),s.add(r))}),c(`Total users: ${t.docs.length}`),c(`Unique referral codes: ${s.size}`),c(`Duplicate codes found: ${i.size}`),i.size>0?(c(`❌ DUPLICATE CODES DETECTED:`),i.forEach(e=>c(`   - ${e}`))):c(`✅ No duplicate codes found`),c("\n=== TEST 2: Testing Current Generation Method ===");let d=new Set,u=new Set;for(let e=0;e<1e3;e++){let r=Date.now().toString().slice(-6),t=Math.random().toString(36).substring(2,8).toUpperCase(),s=`MYN${r}${t.substring(0,1)}`;d.has(s)&&u.add(s),d.add(s),e%100==0&&await new Promise(e=>setTimeout(e,1))}c("Generated codes: 1000"),c(`Unique codes: ${d.size}`),c(`Duplicates in generation: ${u.size}`),c(`Collision rate: ${(u.size/1e3*100).toFixed(2)}%`),u.size>0&&(c(`❌ GENERATION CONFLICTS DETECTED`),c("Sample duplicates:"),Array.from(u).slice(0,5).forEach(e=>{c(`   - ${e}`)})),c("\n=== TEST 3: Checking Conflicts with Database ===");let l=new Set;d.forEach(e=>{s.has(e)&&l.add(e)}),c(`Conflicts with existing DB codes: ${l.size}`),l.size>0&&(c(`❌ DATABASE CONFLICTS DETECTED`),Array.from(l).slice(0,5).forEach(e=>{c(`   - ${e}`)})),c("\n=== TEST 4: Analyzing Timestamp Patterns ===");let p=new Map;d.forEach(e=>{let r=e.substring(3,9);p.set(r,(p.get(r)||0)+1)});let x=Math.max(...p.values());c(`Max codes per timestamp: ${x}`),c(`Timestamp collision potential: ${x>1?"HIGH":"LOW"}`),c("\n=== TEST 5: Recommended Solution ==="),i.size>0||u.size>0||l.size>0?(c(`❌ CRITICAL ISSUE: Referral code conflicts detected!`),c(""),c("RECOMMENDED FIXES:"),c("1. Use proper sequential numbering (MYN0001, MYN0002, etc.)"),c("2. Check for duplicates before creating user"),c("3. Use longer random strings if not sequential"),c("4. Implement retry logic for conflicts"),c(""),c("IMMEDIATE ACTION NEEDED:"),c("- Fix registration to use proper referral code generation"),c("- Clean up duplicate codes in database"),c("- Add unique constraint on referralCode field")):(c(`✅ No immediate conflicts detected`),c("However, current method is still risky for scale")),c("\n\uD83C\uDFAF CONCLUSION:"),c("The current referral code generation method is UNSAFE"),c("and likely causing registration failures due to conflicts.")}catch(e){c(`❌ Test failed: ${e.message}`),c(`Error code: ${e.code}`)}finally{d(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Referral Code Conflict Test"}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("button",{onClick:u,disabled:t,className:"btn-primary mb-4",children:t?"Testing Conflicts...":"Test Referral Code Conflicts"}),(0,s.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,s.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||'Click "Test Referral Code Conflicts" to start...'})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,r,t)=>{"use strict";t.d(r,{db:()=>c,j2:()=>d});var s=t(67989),i=t(63385),o=t(75535),n=t(70146);let a=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),d=(0,i.xI)(a),c=(0,o.aU)(a);(0,n.c7)(a)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},45749:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=t(65239),i=t(48088),o=t(88170),n=t.n(o),a=t(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let c={children:["",{children:["test-referral-codes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,87766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-referral-codes\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-referral-codes\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-referral-codes/page",pathname:"/test-referral-codes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64658:(e,r,t)=>{Promise.resolve().then(t.bind(t,13452))},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87766:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-referral-codes\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[204,756,441,582],()=>t(45749));module.exports=s})();