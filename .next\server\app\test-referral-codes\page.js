/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/test-referral-codes/page";
exports.ids = ["app/test-referral-codes/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftest-referral-codes%2Fpage&page=%2Ftest-referral-codes%2Fpage&appPaths=%2Ftest-referral-codes%2Fpage&pagePath=private-next-app-dir%2Ftest-referral-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftest-referral-codes%2Fpage&page=%2Ftest-referral-codes%2Fpage&appPaths=%2Ftest-referral-codes%2Fpage&pagePath=private-next-app-dir%2Ftest-referral-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-referral-codes/page.tsx */ \"(rsc)/./src/app/test-referral-codes/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'test-referral-codes',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\"],\n'error': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/test-referral-codes/page\",\n        pathname: \"/test-referral-codes\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZ0ZXN0LXJlZmVycmFsLWNvZGVzJTJGcGFnZSZwYWdlPSUyRnRlc3QtcmVmZXJyYWwtY29kZXMlMkZwYWdlJmFwcFBhdGhzPSUyRnRlc3QtcmVmZXJyYWwtY29kZXMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGdGVzdC1yZWZlcnJhbC1jb2RlcyUyRnBhZ2UudHN4JmFwcERpcj1DJTNBJTVDVXNlcnMlNUNBU1VTJTVDT25lRHJpdmUlNUNEZXNrdG9wJTVDTVklMjBQUk9KRUNUUyU1Q05vZGUlMjBNeXR1YmUlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q0FTVVMlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNNWSUyMFBST0pFQ1RTJTVDTm9kZSUyME15dHViZSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD1leHBvcnQmcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0JBQXNCLG9KQUF1SDtBQUM3SSxzQkFBc0Isa0pBQXNIO0FBQzVJLHNCQUFzQixzSkFBd0g7QUFDOUksc0JBQXNCLDBKQUEwSDtBQUNoSixzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQixnT0FBbUY7QUFDekcsb0JBQW9CLHdMQUEwSTtBQUc1SjtBQUdBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXHNyY1xcXFxhcHBcXFxcZXJyb3IudHN4XCIpO1xuY29uc3QgbW9kdWxlMiA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXHNyY1xcXFxhcHBcXFxcbG9hZGluZy50c3hcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxub3QtZm91bmQudHN4XCIpO1xuY29uc3QgbW9kdWxlNCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTUgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIik7XG5jb25zdCBwYWdlNiA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXHNyY1xcXFxhcHBcXFxcdGVzdC1yZWZlcnJhbC1jb2Rlc1xcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAndGVzdC1yZWZlcnJhbC1jb2RlcycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFtwYWdlNiwgXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFx0ZXN0LXJlZmVycmFsLWNvZGVzXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlMCwgXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ2Vycm9yJzogW21vZHVsZTEsIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXHNyY1xcXFxhcHBcXFxcZXJyb3IudHN4XCJdLFxuJ2xvYWRpbmcnOiBbbW9kdWxlMiwgXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxsb2FkaW5nLnRzeFwiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMywgXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxub3QtZm91bmQudHN4XCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGU0LCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTUsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFx0ZXN0LXJlZmVycmFsLWNvZGVzXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi90ZXN0LXJlZmVycmFsLWNvZGVzL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL3Rlc3QtcmVmZXJyYWwtY29kZXNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftest-referral-codes%2Fpage&page=%2Ftest-referral-codes%2Fpage&appPaths=%2Ftest-referral-codes%2Fpage&pagePath=private-next-app-dir%2Ftest-referral-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(rsc)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(rsc)/./src/components/PWAInstaller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Ctest-referral-codes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Ctest-referral-codes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-referral-codes/page.tsx */ \"(rsc)/./src/app/test-referral-codes/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Rlc3QtcmVmZXJyYWwtY29kZXMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQTBJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFx0ZXN0LXJlZmVycmFsLWNvZGVzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Ctest-referral-codes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fe166ce4cd0c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFTVVNcXE9uZURyaXZlXFxEZXNrdG9wXFxNWSBQUk9KRUNUU1xcTm9kZSBNeXR1YmVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZlMTY2Y2U0Y2QwY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PWAInstaller */ \"(rsc)/./src/components/PWAInstaller.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'MyTube - Watch Videos & Earn',\n    description: 'Watch videos and earn money. Complete daily video watching tasks to earn rewards.',\n    keywords: 'video watching, earn money, online earning, video tasks, rewards',\n    authors: [\n        {\n            name: 'MyTube Team'\n        }\n    ],\n    manifest: '/manifest.json',\n    icons: {\n        icon: '/img/mytube-favicon.svg',\n        apple: '/img/mytube-favicon.svg'\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1.0,\n    maximumScale: 1.0,\n    userScalable: false,\n    themeColor: '#FF0000'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"https://cdn.jsdelivr.net/npm/sweetalert2@11\",\n                        async: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animated-bg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-white/80\",\n                    children: \"Loading MyTube...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQVNVU1xcT25lRHJpdmVcXERlc2t0b3BcXE1ZIFBST0pFQ1RTXFxOb2RlIE15dHViZVxcc3JjXFxhcHBcXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGlubmVyIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MFwiPkxvYWRpbmcgTXlUdWJlLi4uPC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/img/mytube-logo.svg\",\n                            alt: \"MyTube Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-white mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-white mb-2\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"The page you're looking for doesn't exist or has been moved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need help finding what you're looking for?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-home mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/work\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-play-circle mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Watch Videos\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/test-referral-codes/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/test-referral-codes/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-referral-codes\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\PWAInstaller.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(ssr)/./src/components/PWAInstaller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Ctest-referral-codes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Ctest-referral-codes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-referral-codes/page.tsx */ \"(ssr)/./src/app/test-referral-codes/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Rlc3QtcmVmZXJyYWwtY29kZXMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQTBJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFx0ZXN0LXJlZmVycmFsLWNvZGVzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Ctest-referral-codes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            console.error('Application error:', error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/img/mytube-logo.svg\",\n                            alt: \"MyTube Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-white mb-4\",\n                            children: \"Oops!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"We encountered an unexpected error. Please try again or contact support if the problem persists.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need immediate help?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-redo mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-home mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Go Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-8 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"text-white/60 cursor-pointer\",\n                            children: \"Error Details (Development)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-4 p-4 bg-red-900/20 rounded-lg text-red-300 text-sm overflow-auto\",\n                            children: [\n                                error.message,\n                                error.stack && '\\n\\n' + error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/test-referral-codes/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/test-referral-codes/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestReferralCodesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/dataService */ \"(ssr)/./src/lib/dataService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction TestReferralCodesPage() {\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const addToResult = (text)=>{\n        setResult((prev)=>prev + text + '\\n');\n    };\n    const testReferralCodeConflicts = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        try {\n            addToResult('🔍 Testing Referral Code Conflicts...\\n');\n            // Test 1: Check existing referral codes in database\n            addToResult('=== TEST 1: Checking Existing Referral Codes ===');\n            const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, _lib_dataService__WEBPACK_IMPORTED_MODULE_4__.COLLECTIONS.users);\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usersRef);\n            const allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(allUsersQuery);\n            const existingCodes = new Set();\n            const duplicates = new Set();\n            allUsersSnapshot.docs.forEach((doc)=>{\n                const data = doc.data();\n                const referralCode = data[_lib_dataService__WEBPACK_IMPORTED_MODULE_4__.FIELD_NAMES.referralCode];\n                if (referralCode) {\n                    if (existingCodes.has(referralCode)) {\n                        duplicates.add(referralCode);\n                    }\n                    existingCodes.add(referralCode);\n                }\n            });\n            addToResult(`Total users: ${allUsersSnapshot.docs.length}`);\n            addToResult(`Unique referral codes: ${existingCodes.size}`);\n            addToResult(`Duplicate codes found: ${duplicates.size}`);\n            if (duplicates.size > 0) {\n                addToResult(`❌ DUPLICATE CODES DETECTED:`);\n                duplicates.forEach((code)=>addToResult(`   - ${code}`));\n            } else {\n                addToResult(`✅ No duplicate codes found`);\n            }\n            // Test 2: Simulate current referral code generation\n            addToResult('\\n=== TEST 2: Testing Current Generation Method ===');\n            const generatedCodes = new Set();\n            const generationDuplicates = new Set();\n            // Generate 1000 codes using current method\n            for(let i = 0; i < 1000; i++){\n                const timestamp = Date.now().toString().slice(-4);\n                const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase();\n                const code = `MY${timestamp}${randomPart}`;\n                if (generatedCodes.has(code)) {\n                    generationDuplicates.add(code);\n                }\n                generatedCodes.add(code);\n                // Small delay to vary timestamp\n                if (i % 100 === 0) {\n                    await new Promise((resolve)=>setTimeout(resolve, 1));\n                }\n            }\n            addToResult(`Generated codes: 1000`);\n            addToResult(`Unique codes: ${generatedCodes.size}`);\n            addToResult(`Duplicates in generation: ${generationDuplicates.size}`);\n            addToResult(`Collision rate: ${(generationDuplicates.size / 1000 * 100).toFixed(2)}%`);\n            if (generationDuplicates.size > 0) {\n                addToResult(`❌ GENERATION CONFLICTS DETECTED`);\n                addToResult(`Sample duplicates:`);\n                Array.from(generationDuplicates).slice(0, 5).forEach((code)=>{\n                    addToResult(`   - ${code}`);\n                });\n            }\n            // Test 3: Check conflicts with existing database codes\n            addToResult('\\n=== TEST 3: Checking Conflicts with Database ===');\n            const conflictsWithDB = new Set();\n            generatedCodes.forEach((code)=>{\n                if (existingCodes.has(code)) {\n                    conflictsWithDB.add(code);\n                }\n            });\n            addToResult(`Conflicts with existing DB codes: ${conflictsWithDB.size}`);\n            if (conflictsWithDB.size > 0) {\n                addToResult(`❌ DATABASE CONFLICTS DETECTED`);\n                Array.from(conflictsWithDB).slice(0, 5).forEach((code)=>{\n                    addToResult(`   - ${code}`);\n                });\n            }\n            // Test 4: Analyze timestamp patterns\n            addToResult('\\n=== TEST 4: Analyzing Timestamp Patterns ===');\n            const timestampCounts = new Map();\n            generatedCodes.forEach((code)=>{\n                const timestamp = code.substring(2, 6) // Extract timestamp part\n                ;\n                timestampCounts.set(timestamp, (timestampCounts.get(timestamp) || 0) + 1);\n            });\n            const maxTimestampCount = Math.max(...timestampCounts.values());\n            addToResult(`Max codes per timestamp: ${maxTimestampCount}`);\n            addToResult(`Timestamp collision potential: ${maxTimestampCount > 1 ? 'HIGH' : 'LOW'}`);\n            // Test 5: Recommend solution\n            addToResult('\\n=== TEST 5: Recommended Solution ===');\n            if (duplicates.size > 0 || generationDuplicates.size > 0 || conflictsWithDB.size > 0) {\n                addToResult(`❌ CRITICAL ISSUE: Referral code conflicts detected!`);\n                addToResult(``);\n                addToResult(`RECOMMENDED FIXES:`);\n                addToResult(`1. Use proper sequential numbering (MY0001, MY0002, etc.)`);\n                addToResult(`2. Check for duplicates before creating user`);\n                addToResult(`3. Use longer random strings if not sequential`);\n                addToResult(`4. Implement retry logic for conflicts`);\n                addToResult(``);\n                addToResult(`IMMEDIATE ACTION NEEDED:`);\n                addToResult(`- Fix registration to use proper referral code generation`);\n                addToResult(`- Clean up duplicate codes in database`);\n                addToResult(`- Add unique constraint on referralCode field`);\n            } else {\n                addToResult(`✅ No immediate conflicts detected`);\n                addToResult(`However, current method is still risky for scale`);\n            }\n            addToResult('\\n🎯 CONCLUSION:');\n            addToResult('The current referral code generation method is UNSAFE');\n            addToResult('and likely causing registration failures due to conflicts.');\n        } catch (error) {\n            addToResult(`❌ Test failed: ${error.message}`);\n            addToResult(`Error code: ${error.code}`);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-white mb-6\",\n                    children: \"Referral Code Conflict Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testReferralCodeConflicts,\n                            disabled: isLoading,\n                            className: \"btn-primary mb-4\",\n                            children: isLoading ? 'Testing Conflicts...' : 'Test Referral Code Conflicts'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black/50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96\",\n                                children: result || 'Click \"Test Referral Code Conflicts\" to start...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/register\",\n                        className: \"text-blue-400 hover:text-blue-300 underline\",\n                        children: \"← Back to Registration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-referral-codes\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/test-referral-codes/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-8 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"fas fa-exclamation-triangle text-red-400 text-4xl mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-4\",\n                            children: \"An error occurred while loading this page. Please refresh and try again.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-refresh mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                \"Refresh Page\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PWAInstaller)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction PWAInstaller() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInstallButton, setShowInstallButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PWAInstaller.useEffect\": ()=>{\n            // Register service worker\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"PWAInstaller.useEffect\": (registration)=>{\n                        console.log('SW registered: ', registration);\n                    }\n                }[\"PWAInstaller.useEffect\"]).catch({\n                    \"PWAInstaller.useEffect\": (registrationError)=>{\n                        console.log('SW registration failed: ', registrationError);\n                    }\n                }[\"PWAInstaller.useEffect\"]);\n            }\n            // Listen for beforeinstallprompt event\n            const handleBeforeInstallPrompt = {\n                \"PWAInstaller.useEffect.handleBeforeInstallPrompt\": (e)=>{\n                    e.preventDefault();\n                    setDeferredPrompt(e);\n                    setShowInstallButton(true);\n                }\n            }[\"PWAInstaller.useEffect.handleBeforeInstallPrompt\"];\n            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n            // Check if app is already installed\n            if (window.matchMedia('(display-mode: standalone)').matches) {\n                setShowInstallButton(false);\n            }\n            return ({\n                \"PWAInstaller.useEffect\": ()=>{\n                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n                }\n            })[\"PWAInstaller.useEffect\"];\n        }\n    }[\"PWAInstaller.useEffect\"], []);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        deferredPrompt.prompt();\n        const { outcome } = await deferredPrompt.userChoice;\n        if (outcome === 'accepted') {\n            console.log('User accepted the install prompt');\n        } else {\n            console.log('User dismissed the install prompt');\n        }\n        setDeferredPrompt(null);\n        setShowInstallButton(false);\n    };\n    if (!showInstallButton) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleInstallClick,\n            className: \"glass-button px-4 py-3 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-download mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                \"Install App\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PWAInstaller.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/dataService.ts":
/*!********************************!*\
  !*** ./src/lib/dataService.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   FIELD_NAMES: () => (/* binding */ FIELD_NAMES),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   addTransaction: () => (/* binding */ addTransaction),\n/* harmony export */   checkQuickVideoAdvantageActive: () => (/* binding */ checkQuickVideoAdvantageActive),\n/* harmony export */   checkReferralCodeExists: () => (/* binding */ checkReferralCodeExists),\n/* harmony export */   checkWithdrawalAllowed: () => (/* binding */ checkWithdrawalAllowed),\n/* harmony export */   createWithdrawalRequest: () => (/* binding */ createWithdrawalRequest),\n/* harmony export */   deleteNotification: () => (/* binding */ deleteNotification),\n/* harmony export */   generateSequentialReferralCode: () => (/* binding */ generateSequentialReferralCode),\n/* harmony export */   generateSimpleReferralCode: () => (/* binding */ generateSimpleReferralCode),\n/* harmony export */   generateUniqueReferralCode: () => (/* binding */ generateUniqueReferralCode),\n/* harmony export */   getAllNotifications: () => (/* binding */ getAllNotifications),\n/* harmony export */   getBankDetails: () => (/* binding */ getBankDetails),\n/* harmony export */   getCurrentReferralCounter: () => (/* binding */ getCurrentReferralCounter),\n/* harmony export */   getPlanEarning: () => (/* binding */ getPlanEarning),\n/* harmony export */   getPlanValidityDays: () => (/* binding */ getPlanValidityDays),\n/* harmony export */   getPlanVideoDuration: () => (/* binding */ getPlanVideoDuration),\n/* harmony export */   getReferralBonus: () => (/* binding */ getReferralBonus),\n/* harmony export */   getReferrals: () => (/* binding */ getReferrals),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUnreadNotificationCount: () => (/* binding */ getUnreadNotificationCount),\n/* harmony export */   getUnreadNotifications: () => (/* binding */ getUnreadNotifications),\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   getUserNotifications: () => (/* binding */ getUserNotifications),\n/* harmony export */   getUserVideoSettings: () => (/* binding */ getUserVideoSettings),\n/* harmony export */   getUserWithdrawals: () => (/* binding */ getUserWithdrawals),\n/* harmony export */   getVideoCountData: () => (/* binding */ getVideoCountData),\n/* harmony export */   getWalletData: () => (/* binding */ getWalletData),\n/* harmony export */   grantQuickVideoAdvantage: () => (/* binding */ grantQuickVideoAdvantage),\n/* harmony export */   hasPendingWithdrawals: () => (/* binding */ hasPendingWithdrawals),\n/* harmony export */   hasUnreadNotifications: () => (/* binding */ hasUnreadNotifications),\n/* harmony export */   isNotificationRead: () => (/* binding */ isNotificationRead),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead),\n/* harmony export */   processReferralBonus: () => (/* binding */ processReferralBonus),\n/* harmony export */   removeQuickVideoAdvantage: () => (/* binding */ removeQuickVideoAdvantage),\n/* harmony export */   saveBankDetails: () => (/* binding */ saveBankDetails),\n/* harmony export */   updateUserData: () => (/* binding */ updateUserData),\n/* harmony export */   updateUserPlanExpiry: () => (/* binding */ updateUserPlanExpiry),\n/* harmony export */   updateUserVideoDuration: () => (/* binding */ updateUserVideoDuration),\n/* harmony export */   updateVideoCount: () => (/* binding */ updateVideoCount),\n/* harmony export */   updateWalletBalance: () => (/* binding */ updateWalletBalance)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\n// Field names for Firestore collections\nconst FIELD_NAMES = {\n    // User fields\n    name: 'name',\n    email: 'email',\n    mobile: 'mobile',\n    referralCode: 'referralCode',\n    referredBy: 'referredBy',\n    referralBonusCredited: 'referralBonusCredited',\n    plan: 'plan',\n    planExpiry: 'planExpiry',\n    activeDays: 'activeDays',\n    joinedDate: 'joinedDate',\n    // Wallet fields\n    wallet: 'wallet',\n    // Bank details fields\n    bankAccountHolderName: 'bankAccountHolderName',\n    bankAccountNumber: 'bankAccountNumber',\n    bankIfscCode: 'bankIfscCode',\n    bankName: 'bankName',\n    bankDetailsUpdated: 'bankDetailsUpdated',\n    // Video fields\n    totalVideos: 'totalVideos',\n    todayVideos: 'todayVideos',\n    lastVideoDate: 'lastVideoDate',\n    videoDuration: 'videoDuration',\n    // Quick Video Advantage fields\n    quickVideoAdvantage: 'quickVideoAdvantage',\n    quickVideoAdvantageExpiry: 'quickVideoAdvantageExpiry',\n    quickVideoAdvantageDays: 'quickVideoAdvantageDays',\n    quickVideoAdvantageSeconds: 'quickVideoAdvantageSeconds',\n    quickVideoAdvantageGrantedBy: 'quickVideoAdvantageGrantedBy',\n    quickVideoAdvantageGrantedAt: 'quickVideoAdvantageGrantedAt',\n    // Transaction fields\n    type: 'type',\n    amount: 'amount',\n    date: 'date',\n    status: 'status',\n    description: 'description',\n    userId: 'userId'\n};\n// Collection names\nconst COLLECTIONS = {\n    users: 'users',\n    transactions: 'transactions',\n    withdrawals: 'withdrawals',\n    plans: 'plans',\n    settings: 'settings',\n    notifications: 'notifications',\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Get user data\nasync function getUserData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserData:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Ensure all values are properly typed\n            const result = {\n                name: String(data[FIELD_NAMES.name] || ''),\n                email: String(data[FIELD_NAMES.email] || ''),\n                mobile: String(data[FIELD_NAMES.mobile] || ''),\n                referralCode: String(data[FIELD_NAMES.referralCode] || ''),\n                referredBy: String(data[FIELD_NAMES.referredBy] || ''),\n                plan: String(data[FIELD_NAMES.plan] || 'Trial'),\n                planExpiry: data[FIELD_NAMES.planExpiry]?.toDate() || null,\n                activeDays: Number(data[FIELD_NAMES.activeDays] || 0),\n                joinedDate: data[FIELD_NAMES.joinedDate]?.toDate() || new Date(),\n                videoDuration: Number(data[FIELD_NAMES.videoDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)),\n                // Quick Video Advantage fields\n                quickVideoAdvantage: Boolean(data[FIELD_NAMES.quickVideoAdvantage] || false),\n                quickVideoAdvantageExpiry: data[FIELD_NAMES.quickVideoAdvantageExpiry]?.toDate() || null,\n                quickVideoAdvantageDays: Number(data[FIELD_NAMES.quickVideoAdvantageDays] || 0),\n                quickVideoAdvantageSeconds: Number(data[FIELD_NAMES.quickVideoAdvantageSeconds] || 30),\n                quickVideoAdvantageGrantedBy: String(data[FIELD_NAMES.quickVideoAdvantageGrantedBy] || ''),\n                quickVideoAdvantageGrantedAt: data[FIELD_NAMES.quickVideoAdvantageGrantedAt]?.toDate() || null\n            };\n            console.log('getUserData result:', result);\n            return result;\n        }\n        return null;\n    } catch (error) {\n        console.error('Error getting user data:', error);\n        return null // Return null instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get wallet data\nasync function getWalletData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getWalletData:', userId);\n            return {\n                wallet: 0\n            };\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const result = {\n                wallet: Number(data[FIELD_NAMES.wallet] || 0)\n            };\n            console.log('getWalletData result:', result);\n            return result;\n        }\n        return {\n            wallet: 0\n        };\n    } catch (error) {\n        console.error('Error getting wallet data:', error);\n        return {\n            wallet: 0\n        } // Return default instead of throwing\n        ;\n    }\n}\n// Get video count data\nasync function getVideoCountData(userId) {\n    try {\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const totalVideos = data[FIELD_NAMES.totalVideos] || 0;\n            const todayVideos = data[FIELD_NAMES.todayVideos] || 0;\n            const lastVideoDate = data[FIELD_NAMES.lastVideoDate]?.toDate();\n            // Check if it's a new day\n            const today = new Date();\n            const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n            return {\n                totalVideos,\n                todayVideos: isNewDay ? 0 : todayVideos,\n                remainingVideos: Math.max(0, 50 - (isNewDay ? 0 : todayVideos))\n            };\n        }\n        return {\n            totalVideos: 0,\n            todayVideos: 0,\n            remainingVideos: 50\n        };\n    } catch (error) {\n        console.error('Error getting video count data:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUserData(userId, data) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), data);\n    } catch (error) {\n        console.error('Error updating user data:', error);\n        throw error;\n    }\n}\n// Add transaction\nasync function addTransaction(userId, transactionData) {\n    try {\n        const transaction = {\n            [FIELD_NAMES.userId]: userId,\n            [FIELD_NAMES.type]: transactionData.type,\n            [FIELD_NAMES.amount]: transactionData.amount,\n            [FIELD_NAMES.description]: transactionData.description,\n            [FIELD_NAMES.status]: transactionData.status || 'completed',\n            [FIELD_NAMES.date]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), transaction);\n    } catch (error) {\n        console.error('Error adding transaction:', error);\n        throw error;\n    }\n}\n// Get transactions\nasync function getTransactions(userId, limitCount = 10) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getTransactions:', userId);\n            return [];\n        }\n        // Temporary fix: Use only where clause without orderBy to avoid index requirement\n        // TODO: Create composite index in Firebase console for better performance\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.userId, '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data()[FIELD_NAMES.date]?.toDate()\n            }));\n        // Sort in memory since we can't use orderBy without index\n        transactions.sort((a, b)=>{\n            const dateA = a.date || new Date(0);\n            const dateB = b.date || new Date(0);\n            return dateB.getTime() - dateA.getTime() // Descending order\n            ;\n        });\n        return transactions;\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        return [] // Return empty array instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get referrals\nasync function getReferrals(referralCode) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referredBy, '==', referralCode));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting referrals:', error);\n        throw error;\n    }\n}\n// Update video count\nasync function updateVideoCount(userId) {\n    try {\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.todayVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n        });\n    } catch (error) {\n        console.error('Error updating video count:', error);\n        throw error;\n    }\n}\n// Update wallet balance\nasync function updateWalletBalance(userId, amount) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.wallet]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(amount)\n        });\n    } catch (error) {\n        console.error('Error updating wallet balance:', error);\n        throw error;\n    }\n}\n// Save bank details\nasync function saveBankDetails(userId, bankDetails) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            throw new Error('Invalid userId provided');\n        }\n        // Validate bank details\n        validateBankDetails(bankDetails);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),\n            [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),\n            [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),\n            [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),\n            [FIELD_NAMES.bankDetailsUpdated]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        });\n        console.log('Bank details saved successfully for user:', userId);\n    } catch (error) {\n        console.error('Error saving bank details:', error);\n        throw error;\n    }\n}\n// Get bank details\nasync function getBankDetails(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getBankDetails:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Check if bank details exist\n            if (data[FIELD_NAMES.bankAccountNumber]) {\n                const result = {\n                    accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),\n                    accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),\n                    ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),\n                    bankName: String(data[FIELD_NAMES.bankName] || '')\n                };\n                console.log('getBankDetails result found');\n                return result;\n            }\n        }\n        console.log('No bank details found for user');\n        return null;\n    } catch (error) {\n        console.error('Error getting bank details:', error);\n        return null;\n    }\n}\n// Get plan-based earning amount (per batch of 50 videos)\nfunction getPlanEarning(plan) {\n    const planEarnings = {\n        'Trial': 10,\n        'Starter': 25,\n        'Basic': 75,\n        'Premium': 150,\n        'Gold': 200,\n        'Platinum': 250,\n        'Diamond': 400\n    };\n    return planEarnings[plan] || 10 // Default to trial earning (per batch of 50 videos)\n    ;\n}\n// Get plan-based video duration (in seconds)\nfunction getPlanVideoDuration(plan) {\n    const planDurations = {\n        'Trial': 30,\n        'Starter': 300,\n        'Basic': 300,\n        'Premium': 300,\n        'Gold': 180,\n        'Platinum': 120,\n        'Diamond': 60 // 1 minute (Rs 9999 plan)\n    };\n    return planDurations[plan] || 30 // Default to trial duration (30 seconds)\n    ;\n}\n// Get plan validity duration in days\nfunction getPlanValidityDays(plan) {\n    const planValidityDays = {\n        'Trial': 2,\n        'Starter': 30,\n        'Basic': 30,\n        'Premium': 30,\n        'Gold': 30,\n        'Platinum': 30,\n        'Diamond': 30,\n        '499': 30,\n        '1499': 30,\n        '2999': 30,\n        '3999': 30,\n        '5999': 30,\n        '9999': 30 // Legacy plan mapping\n    };\n    return planValidityDays[plan] || 2 // Default to trial duration (2 days)\n    ;\n}\n// Check if user's plan is expired based on active days and plan validity\nasync function isUserPlanExpired(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                expired: true,\n                reason: 'User data not found'\n            };\n        }\n        // If user is on Trial plan, check based on joined date\n        if (userData.plan === 'Trial') {\n            const joinedDate = userData.joinedDate || new Date();\n            const today = new Date();\n            const daysSinceJoined = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n            const trialDaysLeft = Math.max(0, 2 - daysSinceJoined);\n            return {\n                expired: trialDaysLeft <= 0,\n                reason: trialDaysLeft <= 0 ? 'Trial period expired' : undefined,\n                daysLeft: trialDaysLeft,\n                activeDays: daysSinceJoined\n            };\n        }\n        // For paid plans, check if planExpiry is set\n        if (userData.planExpiry) {\n            const today = new Date();\n            const expired = today > userData.planExpiry;\n            const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                expired,\n                reason: expired ? 'Plan subscription expired' : undefined,\n                daysLeft,\n                activeDays: userData.activeDays || 0\n            };\n        }\n        // If planExpiry is not set, calculate based on active days and plan validity\n        const planValidityDays = getPlanValidityDays(userData.plan);\n        const currentActiveDays = userData.activeDays || 0;\n        const daysLeft = Math.max(0, planValidityDays - currentActiveDays);\n        const expired = daysLeft <= 0;\n        return {\n            expired,\n            reason: expired ? `Plan validity period (${planValidityDays} days) exceeded based on active days` : undefined,\n            daysLeft,\n            activeDays: currentActiveDays\n        };\n    } catch (error) {\n        console.error('Error checking plan expiry:', error);\n        return {\n            expired: true,\n            reason: 'Error checking plan status'\n        };\n    }\n}\n// Update user's plan expiry when admin changes plan\nasync function updateUserPlanExpiry(userId, newPlan, customExpiryDate) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        if (newPlan === 'Trial') {\n            // Trial plan doesn't have expiry, it's based on joined date\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: null\n            });\n        } else {\n            // Set expiry date for paid plans\n            let expiryDate;\n            if (customExpiryDate) {\n                expiryDate = customExpiryDate;\n            } else {\n                // Calculate expiry based on plan validity\n                const validityDays = getPlanValidityDays(newPlan);\n                const today = new Date();\n                expiryDate = new Date(today.getTime() + validityDays * 24 * 60 * 60 * 1000);\n            }\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiryDate)\n            });\n            console.log(`Updated plan expiry for user ${userId} to ${expiryDate.toDateString()}`);\n        }\n    } catch (error) {\n        console.error('Error updating plan expiry:', error);\n        throw error;\n    }\n}\n// Get referral bonus based on plan\nfunction getReferralBonus(plan) {\n    const referralBonuses = {\n        'Trial': 0,\n        '499': 50,\n        '1499': 150,\n        '2999': 300,\n        '3999': 400,\n        '5999': 700,\n        '9999': 1200,\n        'Starter': 50,\n        'Basic': 150,\n        'Premium': 300,\n        'Gold': 400,\n        'Platinum': 700,\n        'Diamond': 1200\n    };\n    return referralBonuses[plan] || 0;\n}\n// Process referral bonus when admin upgrades user from Trial to paid plan\nasync function processReferralBonus(userId, oldPlan, newPlan) {\n    try {\n        // Only process bonus when upgrading FROM Trial TO a paid plan\n        if (oldPlan !== 'Trial' || newPlan === 'Trial') {\n            console.log('Referral bonus only applies when upgrading from Trial to paid plan');\n            return;\n        }\n        console.log(`Processing referral bonus for user ${userId} upgrading from ${oldPlan} to ${newPlan}`);\n        // Get the user's data to find their referral info\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (!userDoc.exists()) {\n            console.log('User not found');\n            return;\n        }\n        const userData = userDoc.data();\n        const referredBy = userData[FIELD_NAMES.referredBy];\n        const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited];\n        if (!referredBy) {\n            console.log('User was not referred by anyone, skipping bonus processing');\n            return;\n        }\n        if (alreadyCredited) {\n            console.log('Referral bonus already credited for this user, skipping');\n            return;\n        }\n        console.log('Finding referrer with code:', referredBy);\n        // Find the referrer by referral code\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', referredBy), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        if (querySnapshot.empty) {\n            console.log('Referral code not found:', referredBy);\n            return;\n        }\n        const referrerDoc = querySnapshot.docs[0];\n        const referrerId = referrerDoc.id;\n        const bonusAmount = getReferralBonus(newPlan);\n        console.log(`Found referrer: ${referrerId}, bonus amount: ₹${bonusAmount}`);\n        if (bonusAmount > 0) {\n            // Add bonus to referrer's wallet\n            await updateWalletBalance(referrerId, bonusAmount);\n            // Add 50 videos to referrer's total video count\n            const referrerRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, referrerId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(referrerRef, {\n                [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(50)\n            });\n            // Mark referral bonus as credited for this user\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.referralBonusCredited]: true\n            });\n            // Add transaction record for referral bonus\n            await addTransaction(referrerId, {\n                type: 'referral_bonus',\n                amount: bonusAmount,\n                description: `Referral bonus for ${newPlan} plan upgrade + 50 bonus videos (User: ${userData[FIELD_NAMES.name]})`\n            });\n            console.log(`✅ Referral bonus processed: ₹${bonusAmount} + 50 videos for referrer ${referrerId}`);\n        } else {\n            console.log('No bonus amount calculated, skipping');\n        }\n    } catch (error) {\n        console.error('❌ Error processing referral bonus:', error);\n    // Don't throw error to avoid breaking plan update\n    }\n}\n// Get user video settings (duration and earning per batch)\nasync function getUserVideoSettings(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                videoDuration: 30,\n                earningPerBatch: 10,\n                plan: 'Trial',\n                hasQuickAdvantage: false\n            };\n        }\n        // Check if user has active quick video advantage\n        const hasActiveQuickAdvantage = checkQuickVideoAdvantageActive(userData);\n        let videoDuration = userData.videoDuration;\n        // If user has active quick video advantage, use custom seconds or default to 30\n        if (hasActiveQuickAdvantage) {\n            videoDuration = userData.quickVideoAdvantageSeconds || 30 // Use custom duration or default to 30 seconds\n            ;\n        } else {\n            // Use plan-based video duration, but allow admin overrides for non-trial users\n            if (!videoDuration || userData.plan === 'Trial') {\n                videoDuration = getPlanVideoDuration(userData.plan);\n            }\n        }\n        return {\n            videoDuration: videoDuration,\n            earningPerBatch: getPlanEarning(userData.plan),\n            plan: userData.plan,\n            hasQuickAdvantage: hasActiveQuickAdvantage,\n            quickAdvantageExpiry: userData.quickVideoAdvantageExpiry\n        };\n    } catch (error) {\n        console.error('Error getting user video settings:', error);\n        return {\n            videoDuration: 30,\n            earningPerBatch: 10,\n            plan: 'Trial',\n            hasQuickAdvantage: false\n        };\n    }\n}\n// Check if user has active quick video advantage\nfunction checkQuickVideoAdvantageActive(userData) {\n    if (!userData.quickVideoAdvantage || !userData.quickVideoAdvantageExpiry) {\n        return false;\n    }\n    const now = new Date();\n    const expiry = userData.quickVideoAdvantageExpiry;\n    return now < expiry;\n}\n// Grant quick video advantage to user (admin function)\nasync function grantQuickVideoAdvantage(userId, days, grantedBy, seconds = 30) {\n    try {\n        if (days <= 0 || days > 365) {\n            throw new Error('Days must be between 1 and 365');\n        }\n        if (seconds < 1 || seconds > 420) {\n            throw new Error('Seconds must be between 1 and 420 (7 minutes)');\n        }\n        const now = new Date();\n        const expiry = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: true,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiry),\n            [FIELD_NAMES.quickVideoAdvantageDays]: days,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: seconds,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: grantedBy,\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(now)\n        });\n        console.log(`Granted quick video advantage to user ${userId} for ${days} days until ${expiry.toDateString()}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_granted',\n            amount: 0,\n            description: `Quick video advantage granted for ${days} days by ${grantedBy}`\n        });\n        return {\n            success: true,\n            expiry\n        };\n    } catch (error) {\n        console.error('Error granting quick video advantage:', error);\n        throw error;\n    }\n}\n// Remove quick video advantage from user (admin function)\nasync function removeQuickVideoAdvantage(userId, removedBy) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: false,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: null,\n            [FIELD_NAMES.quickVideoAdvantageDays]: 0,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: 30,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: '',\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: null\n        });\n        console.log(`Removed quick video advantage from user ${userId}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_removed',\n            amount: 0,\n            description: `Quick video advantage removed by ${removedBy}`\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error removing quick video advantage:', error);\n        throw error;\n    }\n}\n// Update user video duration (admin function)\nasync function updateUserVideoDuration(userId, durationInSeconds) {\n    try {\n        // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)\n        const isQuickDuration = [\n            1,\n            10,\n            30\n        ].includes(durationInSeconds);\n        const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420;\n        if (!isQuickDuration && !isStandardDuration) {\n            throw new Error('Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration');\n        }\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.videoDuration]: durationInSeconds\n        });\n        console.log(`Updated video duration for user ${userId} to ${durationInSeconds} seconds`);\n    } catch (error) {\n        console.error('Error updating user video duration:', error);\n        throw error;\n    }\n}\n// Validate bank details\nfunction validateBankDetails(bankDetails) {\n    const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails;\n    if (!accountHolderName || accountHolderName.trim().length < 2) {\n        throw new Error('Account holder name must be at least 2 characters long');\n    }\n    if (!accountNumber || !/^\\d{9,18}$/.test(accountNumber.trim())) {\n        throw new Error('Account number must be 9-18 digits');\n    }\n    if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {\n        throw new Error('Invalid IFSC code format (e.g., SBIN0001234)');\n    }\n    if (!bankName || bankName.trim().length < 2) {\n        throw new Error('Bank name must be at least 2 characters long');\n    }\n}\n// Add notification (admin function) - All notifications are now blocking\nasync function addNotification(notification) {\n    try {\n        const notificationData = {\n            title: notification.title,\n            message: notification.message,\n            type: notification.type,\n            targetUsers: notification.targetUsers,\n            userIds: notification.userIds || [],\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdBy: notification.createdBy\n        };\n        console.log('Adding notification to Firestore:', notificationData);\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), notificationData);\n        console.log('Notification added successfully with ID:', docRef.id);\n        // Verify the notification was added\n        const addedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n        if (addedDoc.exists()) {\n            console.log('Notification verified in database:', addedDoc.data());\n        } else {\n            console.warn('Notification not found after adding');\n        }\n        return docRef.id;\n    } catch (error) {\n        console.error('Error adding notification:', error);\n        throw error;\n    }\n}\n// Get notifications for a user\nasync function getUserNotifications(userId, limitCount = 20) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading notifications for user: ${userId}`);\n        // Try to get notifications with fallback for indexing issues\n        let allUsersSnapshot, specificUserSnapshot;\n        try {\n            // Get notifications targeted to all users\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n            console.log(`Found ${allUsersSnapshot.docs.length} notifications for all users`);\n        } catch (error) {\n            console.warn('Error querying all users notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n        }\n        try {\n            // Get notifications targeted to specific user\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n            console.log(`Found ${specificUserSnapshot.docs.length} notifications for specific user`);\n        } catch (error) {\n            console.warn('Error querying specific user notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n        }\n        const notifications = [];\n        // Process all users notifications\n        allUsersSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Process specific user notifications\n        specificUserSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Sort by creation date (newest first)\n        notifications.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        const finalNotifications = notifications.slice(0, limitCount);\n        console.log(`Returning ${finalNotifications.length} total notifications for user`);\n        return finalNotifications;\n    } catch (error) {\n        console.error('Error getting user notifications:', error);\n        return [];\n    }\n}\n// Get all notifications (admin function)\nasync function getAllNotifications(limitCount = 50) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const notifications = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            }));\n        return notifications;\n    } catch (error) {\n        console.error('Error getting all notifications:', error);\n        return [];\n    }\n}\n// Delete notification (admin function)\nasync function deleteNotification(notificationId) {\n    try {\n        if (!notificationId || typeof notificationId !== 'string') {\n            throw new Error('Invalid notification ID provided');\n        }\n        console.log('Deleting notification:', notificationId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications, notificationId));\n        console.log('Notification deleted successfully');\n    } catch (error) {\n        console.error('Error deleting notification:', error);\n        throw error;\n    }\n}\n// Mark notification as read\nasync function markNotificationAsRead(notificationId, userId) {\n    try {\n        // For now, we'll store read status in localStorage since it's user-specific\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        if (!readNotifications.includes(notificationId)) {\n            readNotifications.push(notificationId);\n            localStorage.setItem(`read_notifications_${userId}`, JSON.stringify(readNotifications));\n        }\n    } catch (error) {\n        console.error('Error marking notification as read:', error);\n    }\n}\n// Check if notification is read\nfunction isNotificationRead(notificationId, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return readNotifications.includes(notificationId);\n    } catch (error) {\n        console.error('Error checking notification read status:', error);\n        return false;\n    }\n}\n// Get unread notification count\nfunction getUnreadNotificationCount(notifications, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return notifications.filter((notification)=>!readNotifications.includes(notification.id)).length;\n    } catch (error) {\n        console.error('Error getting unread notification count:', error);\n        return 0;\n    }\n}\n// Get unread notifications - All notifications are now blocking\nasync function getUnreadNotifications(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUnreadNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading unread notifications for user: ${userId}`);\n        // Get all notifications for the user\n        const allNotifications = await getUserNotifications(userId, 50);\n        // Filter for unread notifications\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        const unreadNotifications = allNotifications.filter((notification)=>notification.id && !readNotifications.includes(notification.id));\n        console.log(`Found ${unreadNotifications.length} unread notifications`);\n        return unreadNotifications;\n    } catch (error) {\n        console.error('Error getting unread notifications:', error);\n        return [];\n    }\n}\n// Check if user has unread notifications\nasync function hasUnreadNotifications(userId) {\n    try {\n        const unreadNotifications = await getUnreadNotifications(userId);\n        return unreadNotifications.length > 0;\n    } catch (error) {\n        console.error('Error checking for unread notifications:', error);\n        return false;\n    }\n}\n// Check if user has pending withdrawals\nasync function hasPendingWithdrawals(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking pending withdrawals:', error);\n        return false;\n    }\n}\n// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)\nasync function checkWithdrawalAllowed(userId) {\n    try {\n        // Check user plan first\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                allowed: false,\n                reason: 'Unable to verify user information. Please try again.'\n            };\n        }\n        // Check if user is on trial plan\n        if (userData.plan === 'Trial') {\n            return {\n                allowed: false,\n                reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'\n            };\n        }\n        // Check if user has pending withdrawals\n        const hasPending = await hasPendingWithdrawals(userId);\n        if (hasPending) {\n            return {\n                allowed: false,\n                reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'\n            };\n        }\n        const now = new Date();\n        const currentHour = now.getHours();\n        // Check time restrictions (10 AM to 6 PM)\n        if (currentHour < 10 || currentHour >= 18) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'\n            };\n        }\n        // Check admin leave day\n        const { isAdminLeaveDay } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(now);\n        if (isAdminLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on admin leave/holiday days'\n            };\n        }\n        // Check user leave day\n        const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isUserLeave = await isUserOnLeave(userId, now);\n        if (isUserLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on your leave days'\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking withdrawal allowed:', error);\n        return {\n            allowed: false,\n            reason: 'Unable to verify withdrawal eligibility. Please try again.'\n        };\n    }\n}\n// Create withdrawal request\nasync function createWithdrawalRequest(userId, amount, bankDetails) {\n    try {\n        // Check minimum withdrawal amount\n        if (amount < 50) {\n            throw new Error('Minimum withdrawal amount is ₹50');\n        }\n        // Check if withdrawal is allowed\n        const withdrawalCheck = await checkWithdrawalAllowed(userId);\n        if (!withdrawalCheck.allowed) {\n            throw new Error(withdrawalCheck.reason);\n        }\n        // Check if user has sufficient balance\n        const walletData = await getWalletData(userId);\n        if (walletData.wallet < amount) {\n            throw new Error('Insufficient wallet balance');\n        }\n        // Debit the amount from user's wallet immediately\n        await updateWalletBalance(userId, -amount);\n        // Add transaction record for withdrawal debit\n        await addTransaction(userId, {\n            type: 'withdrawal_request',\n            amount: -amount,\n            description: `Withdrawal request submitted - ₹${amount} debited from wallet`\n        });\n        const withdrawalData = {\n            userId,\n            amount,\n            bankDetails,\n            status: 'pending',\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), withdrawalData);\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating withdrawal request:', error);\n        throw error;\n    }\n}\n// Get user withdrawals\nasync function getUserWithdrawals(userId, limitCount = 20) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting user withdrawals:', error);\n        return [];\n    }\n}\n// Generate sequential referral code - using count-based approach like working platform\nasync function generateSequentialReferralCode() {\n    try {\n        console.log('Generating referral code...');\n        // Method 1: Try to get count from users collection\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getCountFromServer)(usersCollection);\n            const count = snapshot.data().count + 1;\n            const code = `MY${String(count).padStart(4, '0')}`;\n            // Verify this code doesn't already exist\n            const existingCodeQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', code), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n            const existingSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(existingCodeQuery);\n            if (existingSnapshot.empty) {\n                console.log(`Generated sequential referral code: ${code}`);\n                return code;\n            } else {\n                console.log(`Code ${code} already exists, trying alternative method`);\n            }\n        } catch (countError) {\n            console.log('Count method failed, trying alternative method:', countError);\n        }\n        // Method 2: Find the highest existing code and increment\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            const codesQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(FIELD_NAMES.referralCode, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n            const codesSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(codesQuery);\n            let nextNumber = 1;\n            if (!codesSnapshot.empty) {\n                const lastCode = codesSnapshot.docs[0].data()[FIELD_NAMES.referralCode];\n                if (lastCode && lastCode.startsWith('MY')) {\n                    const lastNumber = parseInt(lastCode.substring(2));\n                    if (!isNaN(lastNumber)) {\n                        nextNumber = lastNumber + 1;\n                    }\n                }\n            }\n            const code = `MY${String(nextNumber).padStart(4, '0')}`;\n            console.log(`Generated incremental referral code: ${code}`);\n            return code;\n        } catch (incrementError) {\n            console.log('Increment method failed, using fallback:', incrementError);\n        }\n        // Method 3: Fallback to random code\n        const fallbackCode = `MY${Math.floor(1000 + Math.random() * 9000)}`;\n        console.log('Using fallback referral code:', fallbackCode);\n        return fallbackCode;\n    } catch (error) {\n        console.error('Error generating referral code:', error);\n        // Final fallback to random code\n        const fallbackCode = `MY${Math.floor(1000 + Math.random() * 9000)}`;\n        console.log('Using final fallback referral code:', fallbackCode);\n        return fallbackCode;\n    }\n}\n// Check if referral code exists\nasync function checkReferralCodeExists(code) {\n    try {\n        const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n        const codeQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', code), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(codeQuery);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking referral code:', error);\n        return false;\n    }\n}\n// Generate unique referral code with retry logic\nasync function generateUniqueReferralCode(maxRetries = 5) {\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            const code = await generateSequentialReferralCode();\n            const exists = await checkReferralCodeExists(code);\n            if (!exists) {\n                console.log(`Generated unique referral code: ${code} (attempt ${attempt})`);\n                return code;\n            } else {\n                console.log(`Code ${code} already exists, retrying... (attempt ${attempt})`);\n            }\n        } catch (error) {\n            console.error(`Error in attempt ${attempt}:`, error);\n        }\n    }\n    // Final fallback with timestamp to ensure uniqueness\n    const timestamp = Date.now().toString().slice(-4);\n    const fallbackCode = `MY${timestamp}`;\n    console.log(`Using timestamp-based fallback code: ${fallbackCode}`);\n    return fallbackCode;\n}\n// Get current referral counter (for admin purposes)\nasync function getCurrentReferralCounter() {\n    try {\n        const counterRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'system', 'referralCounter');\n        const counterDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(counterRef);\n        if (counterDoc.exists()) {\n            return counterDoc.data().lastNumber || 0;\n        }\n        return 0;\n    } catch (error) {\n        console.error('Error getting referral counter:', error);\n        return 0;\n    }\n}\n// Simple referral code generation for registration (no database queries to avoid conflicts)\nfunction generateSimpleReferralCode() {\n    // Use current timestamp + random string for better uniqueness\n    const timestamp = Date.now().toString();\n    const randomPart = Math.random().toString(36).substring(2, 8).toUpperCase();\n    // Create a more unique code using full timestamp\n    const uniqueCode = `MY${timestamp.slice(-6)}${randomPart.substring(0, 2)}`;\n    console.log('Generated simple referral code:', uniqueCode);\n    return uniqueCode;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/dataService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ\",\n    authDomain: \"mytube-india.firebaseapp.com\",\n    projectId: \"mytube-india\",\n    storageBucket: \"mytube-india.firebasestorage.app\",\n    messagingSenderId: \"************\",\n    appId: \"1:************:web:ebedaec6a492926af2056a\",\n    measurementId: \"G-R24C6N7CWJ\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)() : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/protobufjs","vendor-chunks/@protobufjs","vendor-chunks/@firebase","vendor-chunks/firebase","vendor-chunks/idb","vendor-chunks/tslib","vendor-chunks/long","vendor-chunks/lodash.camelcase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftest-referral-codes%2Fpage&page=%2Ftest-referral-codes%2Fpage&appPaths=%2Ftest-referral-codes%2Fpage&pagePath=private-next-app-dir%2Ftest-referral-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();