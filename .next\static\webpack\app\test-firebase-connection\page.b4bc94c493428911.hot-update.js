"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-firebase-connection/page",{

/***/ "(app-pages-browser)/./src/app/test-firebase-connection/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/test-firebase-connection/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestFirebaseConnectionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction TestFirebaseConnectionPage() {\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const testFirebaseConnection = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        try {\n            setResult('🔍 Testing Complete Registration Flow...\\n');\n            setResult((prev)=>prev + \"Environment: \".concat(window.location.origin, \"\\n\"));\n            setResult((prev)=>prev + \"Project ID: \".concat(\"mytube-india\", \"\\n\"));\n            setResult((prev)=>prev + \"Auth Domain: \".concat(\"mytube-india.firebaseapp.com\", \"\\n\\n\"));\n            // Test 1: Firebase Auth User Creation (like registration)\n            setResult((prev)=>prev + '📡 Test 1: Firebase Auth User Creation\\n');\n            const testEmail = \"regtest\".concat(Date.now(), \"@example.com\");\n            const testPassword = 'regtest123456';\n            try {\n                setResult((prev)=>prev + \"Creating user: \".concat(testEmail, \"\\n\"));\n                const userCredential = await createUserWithEmailAndPassword(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, testEmail, testPassword);\n                const user = userCredential.user;\n                setResult((prev)=>prev + \"✅ Auth user created: \".concat(user.uid, \"\\n\"));\n                setResult((prev)=>prev + \"   Email: \".concat(user.email, \"\\n\"));\n                setResult((prev)=>prev + \"   Email verified: \".concat(user.emailVerified, \"\\n\"));\n                // Wait for auth to propagate\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                setResult((prev)=>prev + \"   Auth state: \".concat(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser ? 'authenticated' : 'not authenticated', \"\\n\"));\n                // Test 2: Create user document exactly like registration\n                setResult((prev)=>prev + '\\n📡 Test 2: Creating User Document (Registration Style)\\n');\n                // Import the exact field names and collections\n                const { FIELD_NAMES, COLLECTIONS } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_dataService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n                // Generate referral code exactly like registration\n                const timestamp = Date.now().toString().slice(-4);\n                const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase();\n                const userReferralCode = \"MY\".concat(timestamp).concat(randomPart);\n                setResult((prev)=>prev + \"Generated referral code: \".concat(userReferralCode, \"\\n\"));\n                // Create user data exactly like registration\n                const userData = {\n                    [FIELD_NAMES.name]: 'Registration Test User',\n                    [FIELD_NAMES.email]: testEmail.toLowerCase(),\n                    [FIELD_NAMES.mobile]: '9876543210',\n                    [FIELD_NAMES.referralCode]: userReferralCode,\n                    [FIELD_NAMES.referredBy]: '',\n                    [FIELD_NAMES.referralBonusCredited]: false,\n                    [FIELD_NAMES.plan]: 'Trial',\n                    [FIELD_NAMES.planExpiry]: null,\n                    [FIELD_NAMES.activeDays]: 0,\n                    [FIELD_NAMES.joinedDate]: new Date(),\n                    [FIELD_NAMES.wallet]: 0,\n                    [FIELD_NAMES.totalVideos]: 0,\n                    [FIELD_NAMES.todayVideos]: 0,\n                    [FIELD_NAMES.lastVideoDate]: null,\n                    [FIELD_NAMES.videoDuration]: 30,\n                    status: 'active'\n                };\n                setResult((prev)=>prev + \"Document path: \".concat(COLLECTIONS.users, \"/\").concat(user.uid, \"\\n\"));\n                setResult((prev)=>prev + \"Field count: \".concat(Object.keys(userData).length, \"\\n\"));\n                setResult((prev)=>{\n                    var _auth_currentUser;\n                    return prev + \"Current auth UID: \".concat((_auth_currentUser = _lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.uid, \"\\n\");\n                });\n                setResult((prev)=>prev + \"Target UID: \".concat(user.uid, \"\\n\"));\n                setResult((prev)=>{\n                    var _auth_currentUser;\n                    return prev + \"UIDs match: \".concat(((_auth_currentUser = _lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.uid) === user.uid, \"\\n\");\n                });\n                const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, COLLECTIONS.users, user.uid);\n                try {\n                    setResult((prev)=>prev + '\\nAttempting setDoc...\\n');\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)(userDocRef, userData);\n                    setResult((prev)=>prev + '✅ User document created successfully!\\n');\n                    // Verify document\n                    const verifyDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(userDocRef);\n                    if (verifyDoc.exists()) {\n                        const docData = verifyDoc.data();\n                        setResult((prev)=>prev + '✅ Document verification successful\\n');\n                        setResult((prev)=>prev + \"   Name: \".concat(docData[FIELD_NAMES.name], \"\\n\"));\n                        setResult((prev)=>prev + \"   Email: \".concat(docData[FIELD_NAMES.email], \"\\n\"));\n                        setResult((prev)=>prev + \"   Plan: \".concat(docData[FIELD_NAMES.plan], \"\\n\"));\n                        setResult((prev)=>prev + \"   Referral Code: \".concat(docData[FIELD_NAMES.referralCode], \"\\n\"));\n                        setResult((prev)=>prev + '\\n🎉 REGISTRATION TEST SUCCESSFUL!\\n');\n                        setResult((prev)=>prev + 'The registration flow works perfectly.\\n');\n                        setResult((prev)=>prev + 'If registration is failing, check for:\\n');\n                        setResult((prev)=>prev + '- Form validation errors\\n');\n                        setResult((prev)=>prev + '- Network connectivity issues\\n');\n                        setResult((prev)=>prev + '- Browser console errors\\n');\n                    } else {\n                        setResult((prev)=>prev + '❌ Document verification failed\\n');\n                    }\n                } catch (setDocError) {\n                    setResult((prev)=>prev + \"❌ setDoc failed: \".concat(setDocError.message, \"\\n\"));\n                    setResult((prev)=>prev + \"   Error code: \".concat(setDocError.code, \"\\n\"));\n                    setResult((prev)=>prev + \"   Full error: \".concat(JSON.stringify(setDocError, null, 2), \"\\n\"));\n                    if (setDocError.code === 'permission-denied') {\n                        setResult((prev)=>prev + '\\n🔧 PERMISSION ISSUE DETECTED:\\n');\n                        setResult((prev)=>prev + '   - Check Firestore security rules\\n');\n                        setResult((prev)=>prev + '   - Ensure rules allow authenticated users to write their own documents\\n');\n                        setResult((prev)=>prev + '   - Verify the user is properly authenticated\\n');\n                    }\n                }\n                // Clean up\n                try {\n                    await user.delete();\n                    setResult((prev)=>prev + '✅ Test user deleted\\n');\n                } catch (deleteError) {\n                    setResult((prev)=>prev + \"⚠️ User deletion failed: \".concat(deleteError.message, \"\\n\"));\n                }\n            } catch (authError) {\n                setResult((prev)=>prev + \"❌ Auth user creation failed: \".concat(authError.message, \"\\n\"));\n                setResult((prev)=>prev + \"   Code: \".concat(authError.code, \"\\n\"));\n                if (authError.code === 'auth/network-request-failed') {\n                    setResult((prev)=>prev + '\\n🔧 NETWORK ISSUE DETECTED:\\n');\n                    setResult((prev)=>prev + '   - Check your internet connection\\n');\n                    setResult((prev)=>prev + '   - Try disabling VPN/proxy\\n');\n                    setResult((prev)=>prev + '   - Check if firewall is blocking Firebase\\n');\n                    setResult((prev)=>prev + '   - Try testing on a different network\\n');\n                }\n            }\n        } catch (error) {\n            setResult((prev)=>prev + \"❌ Test failed: \".concat(error.message, \"\\n\"));\n            setResult((prev)=>prev + \"   Code: \".concat(error.code, \"\\n\"));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-white mb-6\",\n                    children: \"Firebase Connection Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testFirebaseConnection,\n                            disabled: isLoading,\n                            className: \"btn-primary mb-4\",\n                            children: isLoading ? 'Testing Connection...' : 'Test Firebase Connection'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black/50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-white text-sm whitespace-pre-wrap\",\n                                children: result || 'Click \"Test Firebase Connection\" to start...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/register\",\n                        className: \"text-blue-400 hover:text-blue-300 underline\",\n                        children: \"← Back to Registration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_s(TestFirebaseConnectionPage, \"TA2EjS24NWK+5U65aZ0FJpR3Amc=\");\n_c = TestFirebaseConnectionPage;\nvar _c;\n$RefreshReg$(_c, \"TestFirebaseConnectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-firebase-connection/page.tsx\n"));

/***/ })

});