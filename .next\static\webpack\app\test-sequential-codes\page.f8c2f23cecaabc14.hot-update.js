"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-sequential-codes/page",{

/***/ "(app-pages-browser)/./src/lib/dataService.ts":
/*!********************************!*\
  !*** ./src/lib/dataService.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   FIELD_NAMES: () => (/* binding */ FIELD_NAMES),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   addTransaction: () => (/* binding */ addTransaction),\n/* harmony export */   checkQuickVideoAdvantageActive: () => (/* binding */ checkQuickVideoAdvantageActive),\n/* harmony export */   checkReferralCodeExists: () => (/* binding */ checkReferralCodeExists),\n/* harmony export */   checkWithdrawalAllowed: () => (/* binding */ checkWithdrawalAllowed),\n/* harmony export */   createWithdrawalRequest: () => (/* binding */ createWithdrawalRequest),\n/* harmony export */   deleteNotification: () => (/* binding */ deleteNotification),\n/* harmony export */   generateSequentialReferralCode: () => (/* binding */ generateSequentialReferralCode),\n/* harmony export */   generateSimpleReferralCode: () => (/* binding */ generateSimpleReferralCode),\n/* harmony export */   generateUniqueReferralCode: () => (/* binding */ generateUniqueReferralCode),\n/* harmony export */   getAllNotifications: () => (/* binding */ getAllNotifications),\n/* harmony export */   getBankDetails: () => (/* binding */ getBankDetails),\n/* harmony export */   getCurrentReferralCounter: () => (/* binding */ getCurrentReferralCounter),\n/* harmony export */   getPlanEarning: () => (/* binding */ getPlanEarning),\n/* harmony export */   getPlanValidityDays: () => (/* binding */ getPlanValidityDays),\n/* harmony export */   getPlanVideoDuration: () => (/* binding */ getPlanVideoDuration),\n/* harmony export */   getReferralBonus: () => (/* binding */ getReferralBonus),\n/* harmony export */   getReferrals: () => (/* binding */ getReferrals),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUnreadNotificationCount: () => (/* binding */ getUnreadNotificationCount),\n/* harmony export */   getUnreadNotifications: () => (/* binding */ getUnreadNotifications),\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   getUserNotifications: () => (/* binding */ getUserNotifications),\n/* harmony export */   getUserVideoSettings: () => (/* binding */ getUserVideoSettings),\n/* harmony export */   getUserWithdrawals: () => (/* binding */ getUserWithdrawals),\n/* harmony export */   getVideoCountData: () => (/* binding */ getVideoCountData),\n/* harmony export */   getWalletData: () => (/* binding */ getWalletData),\n/* harmony export */   grantQuickVideoAdvantage: () => (/* binding */ grantQuickVideoAdvantage),\n/* harmony export */   hasPendingWithdrawals: () => (/* binding */ hasPendingWithdrawals),\n/* harmony export */   hasUnreadNotifications: () => (/* binding */ hasUnreadNotifications),\n/* harmony export */   isNotificationRead: () => (/* binding */ isNotificationRead),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead),\n/* harmony export */   processReferralBonus: () => (/* binding */ processReferralBonus),\n/* harmony export */   removeQuickVideoAdvantage: () => (/* binding */ removeQuickVideoAdvantage),\n/* harmony export */   saveBankDetails: () => (/* binding */ saveBankDetails),\n/* harmony export */   updateUserData: () => (/* binding */ updateUserData),\n/* harmony export */   updateUserPlanExpiry: () => (/* binding */ updateUserPlanExpiry),\n/* harmony export */   updateUserVideoDuration: () => (/* binding */ updateUserVideoDuration),\n/* harmony export */   updateVideoCount: () => (/* binding */ updateVideoCount),\n/* harmony export */   updateWalletBalance: () => (/* binding */ updateWalletBalance)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\n// Field names for Firestore collections\nconst FIELD_NAMES = {\n    // User fields\n    name: 'name',\n    email: 'email',\n    mobile: 'mobile',\n    referralCode: 'referralCode',\n    referredBy: 'referredBy',\n    referralBonusCredited: 'referralBonusCredited',\n    plan: 'plan',\n    planExpiry: 'planExpiry',\n    activeDays: 'activeDays',\n    joinedDate: 'joinedDate',\n    // Wallet fields\n    wallet: 'wallet',\n    // Bank details fields\n    bankAccountHolderName: 'bankAccountHolderName',\n    bankAccountNumber: 'bankAccountNumber',\n    bankIfscCode: 'bankIfscCode',\n    bankName: 'bankName',\n    bankDetailsUpdated: 'bankDetailsUpdated',\n    // Video fields\n    totalVideos: 'totalVideos',\n    todayVideos: 'todayVideos',\n    lastVideoDate: 'lastVideoDate',\n    videoDuration: 'videoDuration',\n    // Quick Video Advantage fields\n    quickVideoAdvantage: 'quickVideoAdvantage',\n    quickVideoAdvantageExpiry: 'quickVideoAdvantageExpiry',\n    quickVideoAdvantageDays: 'quickVideoAdvantageDays',\n    quickVideoAdvantageSeconds: 'quickVideoAdvantageSeconds',\n    quickVideoAdvantageGrantedBy: 'quickVideoAdvantageGrantedBy',\n    quickVideoAdvantageGrantedAt: 'quickVideoAdvantageGrantedAt',\n    // Transaction fields\n    type: 'type',\n    amount: 'amount',\n    date: 'date',\n    status: 'status',\n    description: 'description',\n    userId: 'userId'\n};\n// Collection names\nconst COLLECTIONS = {\n    users: 'users',\n    transactions: 'transactions',\n    withdrawals: 'withdrawals',\n    plans: 'plans',\n    settings: 'settings',\n    notifications: 'notifications',\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Get user data\nasync function getUserData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserData:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            var _data_FIELD_NAMES_planExpiry, _data_FIELD_NAMES_joinedDate, _data_FIELD_NAMES_quickVideoAdvantageExpiry, _data_FIELD_NAMES_quickVideoAdvantageGrantedAt;\n            const data = userDoc.data();\n            // Ensure all values are properly typed\n            const result = {\n                name: String(data[FIELD_NAMES.name] || ''),\n                email: String(data[FIELD_NAMES.email] || ''),\n                mobile: String(data[FIELD_NAMES.mobile] || ''),\n                referralCode: String(data[FIELD_NAMES.referralCode] || ''),\n                referredBy: String(data[FIELD_NAMES.referredBy] || ''),\n                plan: String(data[FIELD_NAMES.plan] || 'Trial'),\n                planExpiry: ((_data_FIELD_NAMES_planExpiry = data[FIELD_NAMES.planExpiry]) === null || _data_FIELD_NAMES_planExpiry === void 0 ? void 0 : _data_FIELD_NAMES_planExpiry.toDate()) || null,\n                activeDays: Number(data[FIELD_NAMES.activeDays] || 0),\n                joinedDate: ((_data_FIELD_NAMES_joinedDate = data[FIELD_NAMES.joinedDate]) === null || _data_FIELD_NAMES_joinedDate === void 0 ? void 0 : _data_FIELD_NAMES_joinedDate.toDate()) || new Date(),\n                videoDuration: Number(data[FIELD_NAMES.videoDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)),\n                // Quick Video Advantage fields\n                quickVideoAdvantage: Boolean(data[FIELD_NAMES.quickVideoAdvantage] || false),\n                quickVideoAdvantageExpiry: ((_data_FIELD_NAMES_quickVideoAdvantageExpiry = data[FIELD_NAMES.quickVideoAdvantageExpiry]) === null || _data_FIELD_NAMES_quickVideoAdvantageExpiry === void 0 ? void 0 : _data_FIELD_NAMES_quickVideoAdvantageExpiry.toDate()) || null,\n                quickVideoAdvantageDays: Number(data[FIELD_NAMES.quickVideoAdvantageDays] || 0),\n                quickVideoAdvantageSeconds: Number(data[FIELD_NAMES.quickVideoAdvantageSeconds] || 30),\n                quickVideoAdvantageGrantedBy: String(data[FIELD_NAMES.quickVideoAdvantageGrantedBy] || ''),\n                quickVideoAdvantageGrantedAt: ((_data_FIELD_NAMES_quickVideoAdvantageGrantedAt = data[FIELD_NAMES.quickVideoAdvantageGrantedAt]) === null || _data_FIELD_NAMES_quickVideoAdvantageGrantedAt === void 0 ? void 0 : _data_FIELD_NAMES_quickVideoAdvantageGrantedAt.toDate()) || null\n            };\n            console.log('getUserData result:', result);\n            return result;\n        }\n        return null;\n    } catch (error) {\n        console.error('Error getting user data:', error);\n        return null // Return null instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get wallet data\nasync function getWalletData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getWalletData:', userId);\n            return {\n                wallet: 0\n            };\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const result = {\n                wallet: Number(data[FIELD_NAMES.wallet] || 0)\n            };\n            console.log('getWalletData result:', result);\n            return result;\n        }\n        return {\n            wallet: 0\n        };\n    } catch (error) {\n        console.error('Error getting wallet data:', error);\n        return {\n            wallet: 0\n        } // Return default instead of throwing\n        ;\n    }\n}\n// Get video count data\nasync function getVideoCountData(userId) {\n    try {\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            var _data_FIELD_NAMES_lastVideoDate;\n            const data = userDoc.data();\n            const totalVideos = data[FIELD_NAMES.totalVideos] || 0;\n            const todayVideos = data[FIELD_NAMES.todayVideos] || 0;\n            const lastVideoDate = (_data_FIELD_NAMES_lastVideoDate = data[FIELD_NAMES.lastVideoDate]) === null || _data_FIELD_NAMES_lastVideoDate === void 0 ? void 0 : _data_FIELD_NAMES_lastVideoDate.toDate();\n            // Check if it's a new day\n            const today = new Date();\n            const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n            return {\n                totalVideos,\n                todayVideos: isNewDay ? 0 : todayVideos,\n                remainingVideos: Math.max(0, 50 - (isNewDay ? 0 : todayVideos))\n            };\n        }\n        return {\n            totalVideos: 0,\n            todayVideos: 0,\n            remainingVideos: 50\n        };\n    } catch (error) {\n        console.error('Error getting video count data:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUserData(userId, data) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), data);\n    } catch (error) {\n        console.error('Error updating user data:', error);\n        throw error;\n    }\n}\n// Add transaction\nasync function addTransaction(userId, transactionData) {\n    try {\n        const transaction = {\n            [FIELD_NAMES.userId]: userId,\n            [FIELD_NAMES.type]: transactionData.type,\n            [FIELD_NAMES.amount]: transactionData.amount,\n            [FIELD_NAMES.description]: transactionData.description,\n            [FIELD_NAMES.status]: transactionData.status || 'completed',\n            [FIELD_NAMES.date]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), transaction);\n    } catch (error) {\n        console.error('Error adding transaction:', error);\n        throw error;\n    }\n}\n// Get transactions\nasync function getTransactions(userId) {\n    let limitCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getTransactions:', userId);\n            return [];\n        }\n        // Temporary fix: Use only where clause without orderBy to avoid index requirement\n        // TODO: Create composite index in Firebase console for better performance\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.userId, '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = querySnapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_date;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: (_doc_data_FIELD_NAMES_date = doc.data()[FIELD_NAMES.date]) === null || _doc_data_FIELD_NAMES_date === void 0 ? void 0 : _doc_data_FIELD_NAMES_date.toDate()\n            };\n        });\n        // Sort in memory since we can't use orderBy without index\n        transactions.sort((a, b)=>{\n            const dateA = a.date || new Date(0);\n            const dateB = b.date || new Date(0);\n            return dateB.getTime() - dateA.getTime() // Descending order\n            ;\n        });\n        return transactions;\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        return [] // Return empty array instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get referrals\nasync function getReferrals(referralCode) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referredBy, '==', referralCode));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_joinedDate;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: (_doc_data_FIELD_NAMES_joinedDate = doc.data()[FIELD_NAMES.joinedDate]) === null || _doc_data_FIELD_NAMES_joinedDate === void 0 ? void 0 : _doc_data_FIELD_NAMES_joinedDate.toDate()\n            };\n        });\n    } catch (error) {\n        console.error('Error getting referrals:', error);\n        throw error;\n    }\n}\n// Update video count\nasync function updateVideoCount(userId) {\n    try {\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.todayVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n        });\n    } catch (error) {\n        console.error('Error updating video count:', error);\n        throw error;\n    }\n}\n// Update wallet balance\nasync function updateWalletBalance(userId, amount) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.wallet]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(amount)\n        });\n    } catch (error) {\n        console.error('Error updating wallet balance:', error);\n        throw error;\n    }\n}\n// Save bank details\nasync function saveBankDetails(userId, bankDetails) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            throw new Error('Invalid userId provided');\n        }\n        // Validate bank details\n        validateBankDetails(bankDetails);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),\n            [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),\n            [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),\n            [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),\n            [FIELD_NAMES.bankDetailsUpdated]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        });\n        console.log('Bank details saved successfully for user:', userId);\n    } catch (error) {\n        console.error('Error saving bank details:', error);\n        throw error;\n    }\n}\n// Get bank details\nasync function getBankDetails(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getBankDetails:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Check if bank details exist\n            if (data[FIELD_NAMES.bankAccountNumber]) {\n                const result = {\n                    accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),\n                    accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),\n                    ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),\n                    bankName: String(data[FIELD_NAMES.bankName] || '')\n                };\n                console.log('getBankDetails result found');\n                return result;\n            }\n        }\n        console.log('No bank details found for user');\n        return null;\n    } catch (error) {\n        console.error('Error getting bank details:', error);\n        return null;\n    }\n}\n// Get plan-based earning amount (per batch of 50 videos)\nfunction getPlanEarning(plan) {\n    const planEarnings = {\n        'Trial': 10,\n        'Starter': 25,\n        'Basic': 75,\n        'Premium': 150,\n        'Gold': 200,\n        'Platinum': 250,\n        'Diamond': 400\n    };\n    return planEarnings[plan] || 10 // Default to trial earning (per batch of 50 videos)\n    ;\n}\n// Get plan-based video duration (in seconds)\nfunction getPlanVideoDuration(plan) {\n    const planDurations = {\n        'Trial': 30,\n        'Starter': 300,\n        'Basic': 300,\n        'Premium': 300,\n        'Gold': 180,\n        'Platinum': 120,\n        'Diamond': 60 // 1 minute (Rs 9999 plan)\n    };\n    return planDurations[plan] || 30 // Default to trial duration (30 seconds)\n    ;\n}\n// Get plan validity duration in days\nfunction getPlanValidityDays(plan) {\n    const planValidityDays = {\n        'Trial': 2,\n        'Starter': 30,\n        'Basic': 30,\n        'Premium': 30,\n        'Gold': 30,\n        'Platinum': 30,\n        'Diamond': 30,\n        '499': 30,\n        '1499': 30,\n        '2999': 30,\n        '3999': 30,\n        '5999': 30,\n        '9999': 30 // Legacy plan mapping\n    };\n    return planValidityDays[plan] || 2 // Default to trial duration (2 days)\n    ;\n}\n// Check if user's plan is expired based on active days and plan validity\nasync function isUserPlanExpired(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                expired: true,\n                reason: 'User data not found'\n            };\n        }\n        // If user is on Trial plan, check based on joined date\n        if (userData.plan === 'Trial') {\n            const joinedDate = userData.joinedDate || new Date();\n            const today = new Date();\n            const daysSinceJoined = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n            const trialDaysLeft = Math.max(0, 2 - daysSinceJoined);\n            return {\n                expired: trialDaysLeft <= 0,\n                reason: trialDaysLeft <= 0 ? 'Trial period expired' : undefined,\n                daysLeft: trialDaysLeft,\n                activeDays: daysSinceJoined\n            };\n        }\n        // For paid plans, check if planExpiry is set\n        if (userData.planExpiry) {\n            const today = new Date();\n            const expired = today > userData.planExpiry;\n            const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                expired,\n                reason: expired ? 'Plan subscription expired' : undefined,\n                daysLeft,\n                activeDays: userData.activeDays || 0\n            };\n        }\n        // If planExpiry is not set, calculate based on active days and plan validity\n        const planValidityDays = getPlanValidityDays(userData.plan);\n        const currentActiveDays = userData.activeDays || 0;\n        const daysLeft = Math.max(0, planValidityDays - currentActiveDays);\n        const expired = daysLeft <= 0;\n        return {\n            expired,\n            reason: expired ? \"Plan validity period (\".concat(planValidityDays, \" days) exceeded based on active days\") : undefined,\n            daysLeft,\n            activeDays: currentActiveDays\n        };\n    } catch (error) {\n        console.error('Error checking plan expiry:', error);\n        return {\n            expired: true,\n            reason: 'Error checking plan status'\n        };\n    }\n}\n// Update user's plan expiry when admin changes plan\nasync function updateUserPlanExpiry(userId, newPlan, customExpiryDate) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        if (newPlan === 'Trial') {\n            // Trial plan doesn't have expiry, it's based on joined date\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: null\n            });\n        } else {\n            // Set expiry date for paid plans\n            let expiryDate;\n            if (customExpiryDate) {\n                expiryDate = customExpiryDate;\n            } else {\n                // Calculate expiry based on plan validity\n                const validityDays = getPlanValidityDays(newPlan);\n                const today = new Date();\n                expiryDate = new Date(today.getTime() + validityDays * 24 * 60 * 60 * 1000);\n            }\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiryDate)\n            });\n            console.log(\"Updated plan expiry for user \".concat(userId, \" to \").concat(expiryDate.toDateString()));\n        }\n    } catch (error) {\n        console.error('Error updating plan expiry:', error);\n        throw error;\n    }\n}\n// Get referral bonus based on plan\nfunction getReferralBonus(plan) {\n    const referralBonuses = {\n        'Trial': 0,\n        '499': 50,\n        '1499': 150,\n        '2999': 300,\n        '3999': 400,\n        '5999': 700,\n        '9999': 1200,\n        'Starter': 50,\n        'Basic': 150,\n        'Premium': 300,\n        'Gold': 400,\n        'Platinum': 700,\n        'Diamond': 1200\n    };\n    return referralBonuses[plan] || 0;\n}\n// Process referral bonus when admin upgrades user from Trial to paid plan\nasync function processReferralBonus(userId, oldPlan, newPlan) {\n    try {\n        // Only process bonus when upgrading FROM Trial TO a paid plan\n        if (oldPlan !== 'Trial' || newPlan === 'Trial') {\n            console.log('Referral bonus only applies when upgrading from Trial to paid plan');\n            return;\n        }\n        console.log(\"Processing referral bonus for user \".concat(userId, \" upgrading from \").concat(oldPlan, \" to \").concat(newPlan));\n        // Get the user's data to find their referral info\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (!userDoc.exists()) {\n            console.log('User not found');\n            return;\n        }\n        const userData = userDoc.data();\n        const referredBy = userData[FIELD_NAMES.referredBy];\n        const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited];\n        if (!referredBy) {\n            console.log('User was not referred by anyone, skipping bonus processing');\n            return;\n        }\n        if (alreadyCredited) {\n            console.log('Referral bonus already credited for this user, skipping');\n            return;\n        }\n        console.log('Finding referrer with code:', referredBy);\n        // Find the referrer by referral code\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', referredBy), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        if (querySnapshot.empty) {\n            console.log('Referral code not found:', referredBy);\n            return;\n        }\n        const referrerDoc = querySnapshot.docs[0];\n        const referrerId = referrerDoc.id;\n        const bonusAmount = getReferralBonus(newPlan);\n        console.log(\"Found referrer: \".concat(referrerId, \", bonus amount: ₹\").concat(bonusAmount));\n        if (bonusAmount > 0) {\n            // Add bonus to referrer's wallet\n            await updateWalletBalance(referrerId, bonusAmount);\n            // Add 50 videos to referrer's total video count\n            const referrerRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, referrerId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(referrerRef, {\n                [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(50)\n            });\n            // Mark referral bonus as credited for this user\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.referralBonusCredited]: true\n            });\n            // Add transaction record for referral bonus\n            await addTransaction(referrerId, {\n                type: 'referral_bonus',\n                amount: bonusAmount,\n                description: \"Referral bonus for \".concat(newPlan, \" plan upgrade + 50 bonus videos (User: \").concat(userData[FIELD_NAMES.name], \")\")\n            });\n            console.log(\"✅ Referral bonus processed: ₹\".concat(bonusAmount, \" + 50 videos for referrer \").concat(referrerId));\n        } else {\n            console.log('No bonus amount calculated, skipping');\n        }\n    } catch (error) {\n        console.error('❌ Error processing referral bonus:', error);\n    // Don't throw error to avoid breaking plan update\n    }\n}\n// Get user video settings (duration and earning per batch)\nasync function getUserVideoSettings(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                videoDuration: 30,\n                earningPerBatch: 10,\n                plan: 'Trial',\n                hasQuickAdvantage: false\n            };\n        }\n        // Check if user has active quick video advantage\n        const hasActiveQuickAdvantage = checkQuickVideoAdvantageActive(userData);\n        let videoDuration = userData.videoDuration;\n        // If user has active quick video advantage, use custom seconds or default to 30\n        if (hasActiveQuickAdvantage) {\n            videoDuration = userData.quickVideoAdvantageSeconds || 30 // Use custom duration or default to 30 seconds\n            ;\n        } else {\n            // Use plan-based video duration, but allow admin overrides for non-trial users\n            if (!videoDuration || userData.plan === 'Trial') {\n                videoDuration = getPlanVideoDuration(userData.plan);\n            }\n        }\n        return {\n            videoDuration: videoDuration,\n            earningPerBatch: getPlanEarning(userData.plan),\n            plan: userData.plan,\n            hasQuickAdvantage: hasActiveQuickAdvantage,\n            quickAdvantageExpiry: userData.quickVideoAdvantageExpiry\n        };\n    } catch (error) {\n        console.error('Error getting user video settings:', error);\n        return {\n            videoDuration: 30,\n            earningPerBatch: 10,\n            plan: 'Trial',\n            hasQuickAdvantage: false\n        };\n    }\n}\n// Check if user has active quick video advantage\nfunction checkQuickVideoAdvantageActive(userData) {\n    if (!userData.quickVideoAdvantage || !userData.quickVideoAdvantageExpiry) {\n        return false;\n    }\n    const now = new Date();\n    const expiry = userData.quickVideoAdvantageExpiry;\n    return now < expiry;\n}\n// Grant quick video advantage to user (admin function)\nasync function grantQuickVideoAdvantage(userId, days, grantedBy) {\n    let seconds = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 30;\n    try {\n        if (days <= 0 || days > 365) {\n            throw new Error('Days must be between 1 and 365');\n        }\n        if (seconds < 1 || seconds > 420) {\n            throw new Error('Seconds must be between 1 and 420 (7 minutes)');\n        }\n        const now = new Date();\n        const expiry = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: true,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiry),\n            [FIELD_NAMES.quickVideoAdvantageDays]: days,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: seconds,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: grantedBy,\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(now)\n        });\n        console.log(\"Granted quick video advantage to user \".concat(userId, \" for \").concat(days, \" days until \").concat(expiry.toDateString()));\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_granted',\n            amount: 0,\n            description: \"Quick video advantage granted for \".concat(days, \" days by \").concat(grantedBy)\n        });\n        return {\n            success: true,\n            expiry\n        };\n    } catch (error) {\n        console.error('Error granting quick video advantage:', error);\n        throw error;\n    }\n}\n// Remove quick video advantage from user (admin function)\nasync function removeQuickVideoAdvantage(userId, removedBy) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: false,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: null,\n            [FIELD_NAMES.quickVideoAdvantageDays]: 0,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: 30,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: '',\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: null\n        });\n        console.log(\"Removed quick video advantage from user \".concat(userId));\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_removed',\n            amount: 0,\n            description: \"Quick video advantage removed by \".concat(removedBy)\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error removing quick video advantage:', error);\n        throw error;\n    }\n}\n// Update user video duration (admin function)\nasync function updateUserVideoDuration(userId, durationInSeconds) {\n    try {\n        // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)\n        const isQuickDuration = [\n            1,\n            10,\n            30\n        ].includes(durationInSeconds);\n        const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420;\n        if (!isQuickDuration && !isStandardDuration) {\n            throw new Error('Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration');\n        }\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.videoDuration]: durationInSeconds\n        });\n        console.log(\"Updated video duration for user \".concat(userId, \" to \").concat(durationInSeconds, \" seconds\"));\n    } catch (error) {\n        console.error('Error updating user video duration:', error);\n        throw error;\n    }\n}\n// Validate bank details\nfunction validateBankDetails(bankDetails) {\n    const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails;\n    if (!accountHolderName || accountHolderName.trim().length < 2) {\n        throw new Error('Account holder name must be at least 2 characters long');\n    }\n    if (!accountNumber || !/^\\d{9,18}$/.test(accountNumber.trim())) {\n        throw new Error('Account number must be 9-18 digits');\n    }\n    if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {\n        throw new Error('Invalid IFSC code format (e.g., SBIN0001234)');\n    }\n    if (!bankName || bankName.trim().length < 2) {\n        throw new Error('Bank name must be at least 2 characters long');\n    }\n}\n// Add notification (admin function) - All notifications are now blocking\nasync function addNotification(notification) {\n    try {\n        const notificationData = {\n            title: notification.title,\n            message: notification.message,\n            type: notification.type,\n            targetUsers: notification.targetUsers,\n            userIds: notification.userIds || [],\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdBy: notification.createdBy\n        };\n        console.log('Adding notification to Firestore:', notificationData);\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), notificationData);\n        console.log('Notification added successfully with ID:', docRef.id);\n        // Verify the notification was added\n        const addedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n        if (addedDoc.exists()) {\n            console.log('Notification verified in database:', addedDoc.data());\n        } else {\n            console.warn('Notification not found after adding');\n        }\n        return docRef.id;\n    } catch (error) {\n        console.error('Error adding notification:', error);\n        throw error;\n    }\n}\n// Get notifications for a user\nasync function getUserNotifications(userId) {\n    let limitCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserNotifications:', userId);\n            return [];\n        }\n        console.log(\"Loading notifications for user: \".concat(userId));\n        // Try to get notifications with fallback for indexing issues\n        let allUsersSnapshot, specificUserSnapshot;\n        try {\n            // Get notifications targeted to all users\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n            console.log(\"Found \".concat(allUsersSnapshot.docs.length, \" notifications for all users\"));\n        } catch (error) {\n            console.warn('Error querying all users notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n        }\n        try {\n            // Get notifications targeted to specific user\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n            console.log(\"Found \".concat(specificUserSnapshot.docs.length, \" notifications for specific user\"));\n        } catch (error) {\n            console.warn('Error querying specific user notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n        }\n        const notifications = [];\n        // Process all users notifications\n        allUsersSnapshot.docs.forEach((doc)=>{\n            var _doc_data_createdAt;\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: ((_doc_data_createdAt = doc.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate()) || new Date()\n            });\n        });\n        // Process specific user notifications\n        specificUserSnapshot.docs.forEach((doc)=>{\n            var _doc_data_createdAt;\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: ((_doc_data_createdAt = doc.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate()) || new Date()\n            });\n        });\n        // Sort by creation date (newest first)\n        notifications.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        const finalNotifications = notifications.slice(0, limitCount);\n        console.log(\"Returning \".concat(finalNotifications.length, \" total notifications for user\"));\n        return finalNotifications;\n    } catch (error) {\n        console.error('Error getting user notifications:', error);\n        return [];\n    }\n}\n// Get all notifications (admin function)\nasync function getAllNotifications() {\n    let limitCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50;\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const notifications = querySnapshot.docs.map((doc)=>{\n            var _doc_data_createdAt;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                createdAt: ((_doc_data_createdAt = doc.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate()) || new Date()\n            };\n        });\n        return notifications;\n    } catch (error) {\n        console.error('Error getting all notifications:', error);\n        return [];\n    }\n}\n// Delete notification (admin function)\nasync function deleteNotification(notificationId) {\n    try {\n        if (!notificationId || typeof notificationId !== 'string') {\n            throw new Error('Invalid notification ID provided');\n        }\n        console.log('Deleting notification:', notificationId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications, notificationId));\n        console.log('Notification deleted successfully');\n    } catch (error) {\n        console.error('Error deleting notification:', error);\n        throw error;\n    }\n}\n// Mark notification as read\nasync function markNotificationAsRead(notificationId, userId) {\n    try {\n        // For now, we'll store read status in localStorage since it's user-specific\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        if (!readNotifications.includes(notificationId)) {\n            readNotifications.push(notificationId);\n            localStorage.setItem(\"read_notifications_\".concat(userId), JSON.stringify(readNotifications));\n        }\n    } catch (error) {\n        console.error('Error marking notification as read:', error);\n    }\n}\n// Check if notification is read\nfunction isNotificationRead(notificationId, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        return readNotifications.includes(notificationId);\n    } catch (error) {\n        console.error('Error checking notification read status:', error);\n        return false;\n    }\n}\n// Get unread notification count\nfunction getUnreadNotificationCount(notifications, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        return notifications.filter((notification)=>!readNotifications.includes(notification.id)).length;\n    } catch (error) {\n        console.error('Error getting unread notification count:', error);\n        return 0;\n    }\n}\n// Get unread notifications - All notifications are now blocking\nasync function getUnreadNotifications(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUnreadNotifications:', userId);\n            return [];\n        }\n        console.log(\"Loading unread notifications for user: \".concat(userId));\n        // Get all notifications for the user\n        const allNotifications = await getUserNotifications(userId, 50);\n        // Filter for unread notifications\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        const unreadNotifications = allNotifications.filter((notification)=>notification.id && !readNotifications.includes(notification.id));\n        console.log(\"Found \".concat(unreadNotifications.length, \" unread notifications\"));\n        return unreadNotifications;\n    } catch (error) {\n        console.error('Error getting unread notifications:', error);\n        return [];\n    }\n}\n// Check if user has unread notifications\nasync function hasUnreadNotifications(userId) {\n    try {\n        const unreadNotifications = await getUnreadNotifications(userId);\n        return unreadNotifications.length > 0;\n    } catch (error) {\n        console.error('Error checking for unread notifications:', error);\n        return false;\n    }\n}\n// Check if user has pending withdrawals\nasync function hasPendingWithdrawals(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking pending withdrawals:', error);\n        return false;\n    }\n}\n// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)\nasync function checkWithdrawalAllowed(userId) {\n    try {\n        // Check user plan first\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                allowed: false,\n                reason: 'Unable to verify user information. Please try again.'\n            };\n        }\n        // Check if user is on trial plan\n        if (userData.plan === 'Trial') {\n            return {\n                allowed: false,\n                reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'\n            };\n        }\n        // Check if user has pending withdrawals\n        const hasPending = await hasPendingWithdrawals(userId);\n        if (hasPending) {\n            return {\n                allowed: false,\n                reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'\n            };\n        }\n        const now = new Date();\n        const currentHour = now.getHours();\n        // Check time restrictions (10 AM to 6 PM)\n        if (currentHour < 10 || currentHour >= 18) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'\n            };\n        }\n        // Check admin leave day\n        const { isAdminLeaveDay } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(now);\n        if (isAdminLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on admin leave/holiday days'\n            };\n        }\n        // Check user leave day\n        const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\"));\n        const isUserLeave = await isUserOnLeave(userId, now);\n        if (isUserLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on your leave days'\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking withdrawal allowed:', error);\n        return {\n            allowed: false,\n            reason: 'Unable to verify withdrawal eligibility. Please try again.'\n        };\n    }\n}\n// Create withdrawal request\nasync function createWithdrawalRequest(userId, amount, bankDetails) {\n    try {\n        // Check minimum withdrawal amount\n        if (amount < 50) {\n            throw new Error('Minimum withdrawal amount is ₹50');\n        }\n        // Check if withdrawal is allowed\n        const withdrawalCheck = await checkWithdrawalAllowed(userId);\n        if (!withdrawalCheck.allowed) {\n            throw new Error(withdrawalCheck.reason);\n        }\n        // Check if user has sufficient balance\n        const walletData = await getWalletData(userId);\n        if (walletData.wallet < amount) {\n            throw new Error('Insufficient wallet balance');\n        }\n        // Debit the amount from user's wallet immediately\n        await updateWalletBalance(userId, -amount);\n        // Add transaction record for withdrawal debit\n        await addTransaction(userId, {\n            type: 'withdrawal_request',\n            amount: -amount,\n            description: \"Withdrawal request submitted - ₹\".concat(amount, \" debited from wallet\")\n        });\n        const withdrawalData = {\n            userId,\n            amount,\n            bankDetails,\n            status: 'pending',\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), withdrawalData);\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating withdrawal request:', error);\n        throw error;\n    }\n}\n// Get user withdrawals\nasync function getUserWithdrawals(userId) {\n    let limitCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>{\n            var _doc_data_date;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: (_doc_data_date = doc.data().date) === null || _doc_data_date === void 0 ? void 0 : _doc_data_date.toDate()\n            };\n        });\n    } catch (error) {\n        console.error('Error getting user withdrawals:', error);\n        return [];\n    }\n}\n// Generate sequential referral code - using highest existing number + 1 approach\nasync function generateSequentialReferralCode() {\n    try {\n        console.log('Generating sequential referral code...');\n        // Primary Method: Find the highest existing MYN code and increment\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            // Get all MYN codes and find the highest number\n            const mynCodesQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '>=', 'MYN0000'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '<=', 'MYN9999'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(FIELD_NAMES.referralCode, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n            const mynCodesSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(mynCodesQuery);\n            let nextNumber = 1;\n            if (!mynCodesSnapshot.empty) {\n                const highestCode = mynCodesSnapshot.docs[0].data()[FIELD_NAMES.referralCode];\n                console.log('Highest existing MYN code:', highestCode);\n                if (highestCode && highestCode.startsWith('MYN')) {\n                    const numberPart = highestCode.substring(3);\n                    const currentNumber = parseInt(numberPart);\n                    if (!isNaN(currentNumber)) {\n                        nextNumber = currentNumber + 1;\n                        console.log(\"Next sequential number: \".concat(nextNumber));\n                    }\n                }\n            } else {\n                console.log('No existing MYN codes found, starting from MYN0001');\n            }\n            const code = \"MYN\".concat(String(nextNumber).padStart(4, '0'));\n            console.log(\"Generated sequential referral code: \".concat(code));\n            return code;\n        } catch (sequentialError) {\n            console.log('Sequential method failed, trying fallback:', sequentialError);\n        }\n        // Fallback Method: Use user count + 1\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getCountFromServer)(usersCollection);\n            const count = snapshot.data().count + 1;\n            const fallbackCode = \"MYN\".concat(String(count).padStart(4, '0'));\n            console.log(\"Using count-based fallback code: \".concat(fallbackCode));\n            return fallbackCode;\n        } catch (countError) {\n            console.log('Count method also failed, using random fallback:', countError);\n        }\n        // Final Fallback: Random code\n        const randomCode = \"MYN\".concat(Math.floor(1000 + Math.random() * 9000));\n        console.log('Using random fallback referral code:', randomCode);\n        return randomCode;\n    } catch (error) {\n        console.error('Error generating referral code:', error);\n        // Ultimate fallback\n        const emergencyCode = \"MYN\".concat(Math.floor(1000 + Math.random() * 9000));\n        console.log('Using emergency fallback referral code:', emergencyCode);\n        return emergencyCode;\n    }\n}\n// Check if referral code exists\nasync function checkReferralCodeExists(code) {\n    try {\n        const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n        const codeQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', code), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(codeQuery);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking referral code:', error);\n        return false;\n    }\n}\n// Generate unique referral code with retry logic\nasync function generateUniqueReferralCode() {\n    let maxRetries = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            const code = await generateSequentialReferralCode();\n            const exists = await checkReferralCodeExists(code);\n            if (!exists) {\n                console.log(\"Generated unique referral code: \".concat(code, \" (attempt \").concat(attempt, \")\"));\n                return code;\n            } else {\n                console.log(\"Code \".concat(code, \" already exists, retrying... (attempt \").concat(attempt, \")\"));\n            }\n        } catch (error) {\n            console.error(\"Error in attempt \".concat(attempt, \":\"), error);\n        }\n    }\n    // Final fallback with timestamp to ensure uniqueness\n    const timestamp = Date.now().toString().slice(-4);\n    const fallbackCode = \"MYN\".concat(timestamp);\n    console.log(\"Using timestamp-based fallback code: \".concat(fallbackCode));\n    return fallbackCode;\n}\n// Get current referral counter (for admin purposes)\nasync function getCurrentReferralCounter() {\n    try {\n        const counterRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'system', 'referralCounter');\n        const counterDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(counterRef);\n        if (counterDoc.exists()) {\n            return counterDoc.data().lastNumber || 0;\n        }\n        return 0;\n    } catch (error) {\n        console.error('Error getting referral counter:', error);\n        return 0;\n    }\n}\n// Simple referral code generation for registration (no database queries to avoid conflicts)\nfunction generateSimpleReferralCode() {\n    // Use current timestamp + random string for better uniqueness\n    const timestamp = Date.now().toString();\n    const randomPart = Math.random().toString(36).substring(2, 8).toUpperCase();\n    // Create a more unique code using full timestamp with MYN prefix\n    const uniqueCode = \"MYN\".concat(timestamp.slice(-6)).concat(randomPart.substring(0, 1));\n    console.log('Generated simple referral code:', uniqueCode);\n    return uniqueCode;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/dataService.ts\n"));

/***/ })

});