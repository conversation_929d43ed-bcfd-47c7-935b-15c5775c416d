(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779,8733],{12:(e,t,a)=>{"use strict";a.d(t,{M4:()=>o,_f:()=>l});var r=a(6104),s=a(4752),n=a.n(s);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await n().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await r.j2.signOut(),n().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),n().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},2899:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var r=a(5155),s=a(2115),n=a(6874),i=a.n(n),l=a(6681),o=a(6779),d=a(3592),c=a(3737),u=a(4752),m=a.n(u);function x(){let{user:e,loading:t,isAdmin:a}=(0,l.wC)(),[n,u]=(0,s.useState)([]),[x,g]=(0,s.useState)(!0),[h,p]=(0,s.useState)(""),[f,y]=(0,s.useState)(!1),[b,v]=(0,s.useState)(null),[w,j]=(0,s.useState)(!1),[N,D]=(0,s.useState)({name:"",email:"",mobile:"",referralCode:"",referredBy:"",plan:"",activeDays:0,totalVideos:0,todayVideos:0,wallet:0,status:"active",videoDuration:300}),[k,C]=(0,s.useState)(!1),[S,B]=(0,s.useState)(1),[E,V]=(0,s.useState)(!0),[A,P]=(0,s.useState)(null),[I,U]=(0,s.useState)(!1),[T,M]=(0,s.useState)(7);(0,s.useEffect)(()=>{a&&_()},[a]);let _=async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];try{g(!0);let t=await (0,o.lo)(50,e?null:A);e?(u(t.users),B(1)):u(e=>[...e,...t.users]),P(t.lastDoc),V(t.hasMore)}catch(e){console.error("Error loading users:",e),m().fire({icon:"error",title:"Error",text:"Failed to load users. Please try again."})}finally{g(!1)}},G=async()=>{if(!h.trim())return void _();try{y(!0);let e=await (0,o.Ki)(h.trim());u(e),V(!1)}catch(e){console.error("Error searching users:",e),m().fire({icon:"error",title:"Search Failed",text:"Failed to search users. Please try again."})}finally{y(!1)}},R=e=>{v(e),D({name:e.name,email:e.email,mobile:e.mobile,referralCode:e.referralCode,referredBy:e.referredBy,plan:e.plan,activeDays:e.activeDays,totalVideos:e.totalVideos,todayVideos:e.todayVideos,wallet:e.wallet||0,status:e.status,videoDuration:e.videoDuration||300}),j(!0)},Y=async()=>{if(b)try{C(!0);let e=b.plan,t=N.plan,a=e!==t,r={name:N.name,email:N.email,mobile:N.mobile,referralCode:N.referralCode,referredBy:N.referredBy,plan:N.plan,activeDays:N.activeDays,totalVideos:N.totalVideos,todayVideos:N.todayVideos,wallet:N.wallet,status:N.status};if(await (0,o.TK)(b.id,r),N.videoDuration!==(b.videoDuration||300)&&await (0,d.Gl)(b.id,N.videoDuration),a)try{await (0,d.II)(b.id,t),console.log("Updated plan expiry for user ".concat(b.id,": ").concat(e," -> ").concat(t))}catch(e){console.error("Error updating plan expiry:",e)}if(a&&"Trial"===e&&"Trial"!==t)try{console.log("Processing referral bonus for user ".concat(b.id,": ").concat(e," -> ").concat(t)),await (0,d.IK)(b.id,e,t),m().fire({icon:"success",title:"User Updated & Referral Bonus Processed",html:'\n              <div class="text-left">\n                <p><strong>User plan updated:</strong> '.concat(e," → ").concat(t,"</p>\n                <p><strong>Referral bonus:</strong> Processed for referrer (if applicable)</p>\n              </div>\n            "),timer:4e3,showConfirmButton:!1})}catch(a){console.error("Error processing referral bonus:",a),m().fire({icon:"warning",title:"User Updated (Referral Bonus Issue)",html:'\n              <div class="text-left">\n                <p><strong>User plan updated successfully:</strong> '.concat(e," → ").concat(t,'</p>\n                <p><strong>Referral bonus:</strong> Could not be processed automatically</p>\n                <p class="text-sm text-gray-600 mt-2">Please check referral bonus manually if needed.</p>\n              </div>\n            '),timer:5e3,showConfirmButton:!1})}else m().fire({icon:"success",title:"User Updated",text:"User information has been updated successfully",timer:2e3,showConfirmButton:!1});u(e=>e.map(e=>e.id===b.id?{...e,...r,videoDuration:N.videoDuration}:e)),j(!1),v(null)}catch(e){console.error("Error updating user:",e),m().fire({icon:"error",title:"Update Failed",text:"Failed to update user. Please try again."})}finally{C(!1)}},F=async e=>{if((await m().fire({icon:"warning",title:"Delete User",text:"Are you sure you want to delete ".concat(e.name,"? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{await (0,o.hG)(e.id),u(t=>t.filter(t=>t.id!==e.id)),m().fire({icon:"success",title:"User Deleted",text:"User has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting user:",e),m().fire({icon:"error",title:"Delete Failed",text:"Failed to delete user. Please try again."})}},L=async e=>{let{value:t}=await m().fire({title:"Grant Quick Video Advantage",html:"\n        <p>Grant quick video advantage to <strong>".concat(e.name,'</strong></p>\n        <p class="text-sm text-gray-600 mb-4">User will get 30-second video duration for the selected period</p>\n        <label class="block text-left mb-2">Number of days:</label>\n      '),input:"number",inputValue:7,inputAttributes:{min:"1",max:"365",step:"1"},showCancelButton:!0,confirmButtonText:"Grant Advantage",cancelButtonText:"Cancel",inputValidator:e=>{let t=parseInt(e);if(!e||t<1||t>365)return"Please enter a number between 1 and 365"}});if(t)try{await (0,d.w1)(e.id,parseInt(t),e.email||"admin"),await _(),m().fire("Success!","Quick video advantage granted for ".concat(t," days"),"success")}catch(e){console.error("Error granting quick advantage:",e),m().fire("Error",e.message||"Failed to grant quick advantage","error")}},W=async e=>{if((await m().fire({title:"Remove Quick Video Advantage",text:"Remove quick video advantage from ".concat(e.name,"?"),icon:"warning",showCancelButton:!0,confirmButtonColor:"#dc2626",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, remove",cancelButtonText:"Cancel"})).isConfirmed)try{await (0,d.wT)(e.id,e.email||"admin"),await _(),m().fire("Removed!","Quick video advantage has been removed.","success")}catch(e){console.error("Error removing quick advantage:",e),m().fire("Error",e.message||"Failed to remove quick advantage","error")}},q=e=>null==e||isNaN(e)?"₹0.00":"₹".concat(e.toFixed(2)),J=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},Q=e=>{switch(e){case"active":return"bg-green-500";case"inactive":return"bg-red-500";case"suspended":return"bg-yellow-500";default:return"bg-gray-500"}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)(i(),{href:"/admin",className:"text-gray-600 hover:text-gray-800",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(i(),{href:"/admin/upload-users",className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]}),(0,r.jsxs)("button",{onClick:()=>{if(0===n.length)return void m().fire({icon:"warning",title:"No Data",text:"No users to export."});let e=(0,c.Fz)(n);(0,c.Bf)(e,"users"),m().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(n.length," users to CSV file."),timer:2e3,showConfirmButton:!1})},className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,r.jsxs)("button",{onClick:()=>_(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("input",{type:"text",value:h,onChange:e=>p(e.target.value),placeholder:"Search by mobile number...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onKeyPress:e=>"Enter"===e.key&&G()}),(0,r.jsx)("button",{onClick:G,disabled:f,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Searching..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-search mr-2"}),"Search"]})}),h&&(0,r.jsx)("button",{onClick:()=>{p(""),_()},className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:(0,r.jsx)("i",{className:"fas fa-times"})})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Videos"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duration"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quick Advantage"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:x&&0===n.length?(0,r.jsx)("tr",{children:(0,r.jsxs)("td",{colSpan:9,className:"px-6 py-4 text-center",children:[(0,r.jsx)("div",{className:"spinner mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading users..."})]})}):0===n.length?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:9,className:"px-6 py-4 text-center text-gray-500",children:"No users found"})}):n.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Joined: ",e.joinedDate.toLocaleDateString()]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.mobile}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Code: ",e.referralCode]}),e.referredBy&&(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Ref: ",e.referredBy]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(J(e.plan)),children:e.plan}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["Days: ",e.activeDays]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:["Total: ",e.totalVideos]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Today: ",e.todayVideos]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:60>(e.videoDuration||300)?"".concat(e.videoDuration||300,"s"):"".concat(Math.round((e.videoDuration||300)/60),"m")}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:60>(e.videoDuration||300)?"".concat(e.videoDuration||300," second").concat((e.videoDuration||300)>1?"s":""):"".concat(Math.round((e.videoDuration||300)/60)," minute").concat(Math.round((e.videoDuration||300)/60)>1?"s":"")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.quickVideoAdvantage&&e.quickVideoAdvantageExpiry&&new Date<e.quickVideoAdvantageExpiry?(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-green-500",children:"Active"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Until: ",e.quickVideoAdvantageExpiry.toLocaleDateString()]})]}):(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-gray-500",children:"None"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsx)("i",{className:"fas fa-wallet mr-1 text-green-500"}),q(e.wallet||0)]}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Total Balance"})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(Q(e.status)),children:e.status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>R(e),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:(0,r.jsx)("i",{className:"fas fa-edit"})}),e.quickVideoAdvantage&&e.quickVideoAdvantageExpiry&&new Date<e.quickVideoAdvantageExpiry?(0,r.jsx)("button",{onClick:()=>W(e),className:"text-orange-600 hover:text-orange-900",title:"Remove Quick Advantage",children:(0,r.jsx)("i",{className:"fas fa-clock"})}):(0,r.jsx)("button",{onClick:()=>L(e),className:"text-green-600 hover:text-green-900",title:"Grant Quick Advantage",children:(0,r.jsx)("i",{className:"fas fa-bolt"})}),(0,r.jsx)("button",{onClick:()=>F(e),className:"text-red-600 hover:text-red-900",title:"Delete User",children:(0,r.jsx)("i",{className:"fas fa-trash"})})]})})]},e.id))})]})}),E&&!x&&n.length>0&&(0,r.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 text-center",children:(0,r.jsxs)("button",{onClick:()=>{E&&!x&&_(!1)},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:[(0,r.jsx)("i",{className:"fas fa-chevron-down mr-2"}),"Load More Users"]})})]})}),w&&b&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 overflow-y-auto modal-scrollable",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-md my-8 max-h-[calc(100vh-2rem)] flex flex-col shadow-xl",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 flex-shrink-0",children:(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"Edit User"})}),(0,r.jsxs)("div",{className:"p-6 space-y-4 flex-1 overflow-y-auto modal-scrollable",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,r.jsx)("input",{type:"text",value:N.name,onChange:e=>D(t=>({...t,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,r.jsx)("input",{type:"email",value:N.email,onChange:e=>D(t=>({...t,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mobile"}),(0,r.jsx)("input",{type:"text",value:N.mobile,onChange:e=>D(t=>({...t,mobile:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referral Code"}),(0,r.jsx)("input",{type:"text",value:N.referralCode,onChange:e=>D(t=>({...t,referralCode:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referred By"}),(0,r.jsx)("input",{type:"text",value:N.referredBy,onChange:e=>D(t=>({...t,referredBy:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan"}),(0,r.jsxs)("select",{value:N.plan,onChange:e=>D(t=>({...t,plan:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"Trial",children:"Trial"}),(0,r.jsx)("option",{value:"Starter",children:"Starter"}),(0,r.jsx)("option",{value:"Basic",children:"Basic"}),(0,r.jsx)("option",{value:"Premium",children:"Premium"}),(0,r.jsx)("option",{value:"Gold",children:"Gold"}),(0,r.jsx)("option",{value:"Platinum",children:"Platinum"}),(0,r.jsx)("option",{value:"Diamond",children:"Diamond"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Active Days"}),(0,r.jsx)("input",{type:"number",value:N.activeDays,onChange:e=>D(t=>({...t,activeDays:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Videos"}),(0,r.jsx)("input",{type:"number",value:N.totalVideos,onChange:e=>D(t=>({...t,totalVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Today Videos"}),(0,r.jsx)("input",{type:"number",value:N.todayVideos,onChange:e=>D(t=>({...t,todayVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Wallet Balance (₹)"}),(0,r.jsx)("input",{type:"number",step:"0.01",value:N.wallet,onChange:e=>D(t=>({...t,wallet:parseFloat(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Video Duration"}),(0,r.jsxs)("select",{value:N.videoDuration,onChange:e=>D(t=>({...t,videoDuration:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsxs)("optgroup",{label:"\uD83D\uDE80 Quick Duration",children:[(0,r.jsx)("option",{value:1,children:"1 second"}),(0,r.jsx)("option",{value:10,children:"10 seconds"}),(0,r.jsx)("option",{value:30,children:"30 seconds"})]}),(0,r.jsxs)("optgroup",{label:"⏱️ Standard Duration",children:[(0,r.jsx)("option",{value:60,children:"1 minute"}),(0,r.jsx)("option",{value:120,children:"2 minutes"}),(0,r.jsx)("option",{value:180,children:"3 minutes"}),(0,r.jsx)("option",{value:240,children:"4 minutes"}),(0,r.jsx)("option",{value:300,children:"5 minutes"}),(0,r.jsx)("option",{value:360,children:"6 minutes"}),(0,r.jsx)("option",{value:420,children:"7 minutes"})]})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:N.videoDuration<60?"".concat(N.videoDuration," second").concat(N.videoDuration>1?"s":""):"".concat(Math.round(N.videoDuration/60)," minute").concat(Math.round(N.videoDuration/60)>1?"s":"")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("select",{value:N.status,onChange:e=>D(t=>({...t,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"active",children:"Active"}),(0,r.jsx)("option",{value:"inactive",children:"Inactive"}),(0,r.jsx)("option",{value:"suspended",children:"Suspended"})]})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 flex-shrink-0",children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("button",{onClick:Y,disabled:k,className:"flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:k?"Saving...":"Save Changes"}),(0,r.jsx)("button",{onClick:()=>j(!1),className:"flex-1 bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700",children:"Cancel"})]})})]})})]})}},3737:(e,t,a)=>{"use strict";function r(e,t,a){if(!e||0===e.length)return void alert("No data to export");let r=a||Object.keys(e[0]),s=new Blob([[r.join(","),...e.map(e=>r.map(t=>{let a=e[t];if(null==a)return"";if("string"==typeof a){let e=a.replace(/"/g,'""');return e.includes(",")?'"'.concat(e,'"'):e}return a instanceof Date?a.toLocaleDateString():String(a)}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(void 0!==n.download){let e=URL.createObjectURL(s);n.setAttribute("href",e),n.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)}}function s(e){return e.map(e=>{var t;return{Name:e.name,Email:e.email,Mobile:e.mobile,"Referral Code":e.referralCode,"Referred By":e.referredBy||"Direct",Plan:e.plan,"Active Days":e.activeDays,"Total Videos":e.totalVideos,"Today Videos":e.todayVideos,"Video Duration (seconds)":e.videoDuration||300,"Wallet Balance":e.wallet||0,Status:e.status,"Joined Date":(null==(t=e.joinedDate)?void 0:t.toLocaleDateString())||""}})}function n(e){return e.map(e=>{var t;return{"User ID":e.userId,"User Name":e.userName||"","User Email":e.userEmail||"",Type:e.type,Amount:e.amount,Description:e.description,Status:e.status,Date:(null==(t=e.date)?void 0:t.toLocaleDateString())||""}})}function i(e){return e.map(e=>{var t,a,r,s,n;return{"User Name":e.userName,"User Email":e.userEmail,"Mobile Number":e.userMobile||"","User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount,"Account Holder":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":(null==(r=e.bankDetails)?void 0:r.accountNumber)||"","IFSC Code":(null==(s=e.bankDetails)?void 0:s.ifscCode)||"",Status:e.status,"Request Date":(null==(n=e.requestDate)?void 0:n.toLocaleDateString())||"","Admin Notes":e.adminNotes||""}})}function l(e){return e.map(e=>{var t,a;return{Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":(null==(t=e.createdAt)?void 0:t.toLocaleDateString())||"","Sent Date":(null==(a=e.sentAt)?void 0:a.toLocaleDateString())||""}})}a.d(t,{Bf:()=>r,Fz:()=>s,Pe:()=>l,dB:()=>i,sL:()=>n})},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>d,j2:()=>o});var r=a(3915),s=a(3004),n=a(5317),i=a(858);let l=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,s.xI)(l),d=(0,n.aU)(l);(0,i.c7)(l)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>o,hD:()=>l,wC:()=>d});var r=a(2115),s=a(3004),n=a(6104),i=a(12);function l(){let[e,t]=(0,r.useState)(null),[a,l]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,s.hg)(n.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),l(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),l(!1)}},[]);let o=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:o}}function o(){let{user:e,loading:t}=l();return(0,r.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function d(){let{user:e,loading:t}=l(),[a,s]=(0,r.useState)(!1),[n,i]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");s(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||n,isAdmin:a}}},6779:(e,t,a)=>{"use strict";a.d(t,{Ki:()=>d,Pn:()=>l,TK:()=>u,getWithdrawals:()=>c,hG:()=>m,lo:()=>o,updateWithdrawalStatus:()=>x});var r=a(5317),s=a(6104),n=a(3592);let i=new Map;async function l(){let e="dashboard-stats",t=function(e){let t=i.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=r.Dc.fromDate(t),l=await (0,r.GG)((0,r.rJ)(s.db,n.I.users)),o=l.size,d=(0,r.P)((0,r.rJ)(s.db,n.I.users),(0,r._M)(n.Yr.joinedDate,">=",a)),c=(await (0,r.GG)(d)).size,u=0,m=0,x=0,g=0;l.forEach(e=>{var a;let r=e.data();u+=r[n.Yr.totalVideos]||0,m+=r[n.Yr.wallet]||0;let s=null==(a=r[n.Yr.lastVideoDate])?void 0:a.toDate();s&&s.toDateString()===t.toDateString()&&(x+=r[n.Yr.todayVideos]||0)});try{let e=(0,r.P)((0,r.rJ)(s.db,n.I.transactions),(0,r._M)(n.Yr.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.GG)(e)).forEach(e=>{var a;let r=e.data(),s=null==(a=r[n.Yr.date])?void 0:a.toDate();s&&s>=t&&(g+=r[n.Yr.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let h=(0,r.P)((0,r.rJ)(s.db,n.I.withdrawals),(0,r._M)("status","==","pending")),p=(await (0,r.GG)(h)).size,f=(0,r.P)((0,r.rJ)(s.db,n.I.withdrawals),(0,r._M)("date",">=",a)),y=(await (0,r.GG)(f)).size,b={totalUsers:o,totalVideos:u,totalEarnings:m,pendingWithdrawals:p,todayUsers:c,todayVideos:x,todayEarnings:g,todayWithdrawals:y};return i.set(e,{data:b,timestamp:Date.now()}),b}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.rJ)(s.db,n.I.users),(0,r.My)(n.Yr.joinedDate,"desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.rJ)(s.db,n.I.users),(0,r.My)(n.Yr.joinedDate,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let i=await (0,r.GG)(a);return{users:i.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[n.Yr.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[n.Yr.planExpiry])?void 0:a.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{let t=(0,r.P)((0,r.rJ)(s.db,n.I.users),(0,r._M)(n.Yr.mobile,"==",e));return(await (0,r.GG)(t)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[n.Yr.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[n.Yr.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error searching users by mobile:",e),e}}async function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.rJ)(s.db,n.I.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.rJ)(s.db,n.I.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(t),(0,r.AB)(e)));let i=await (0,r.GG)(a);return{withdrawals:i.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function u(e,t){try{await (0,r.mZ)((0,r.H9)(s.db,n.I.users,e),t),i.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function m(e){try{await (0,r.kd)((0,r.H9)(s.db,n.I.users,e)),i.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function x(e,t,l){try{let o=await (0,r.x7)((0,r.H9)(s.db,n.I.withdrawals,e));if(!o.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=o.data(),m={status:t,updatedAt:r.Dc.now()};if(l&&(m.adminNotes=l),await (0,r.mZ)((0,r.H9)(s.db,n.I.withdrawals,e),m),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(d,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}i.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},7428:(e,t,a)=>{Promise.resolve().then(a.bind(a,2899))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>t(7428)),_N_E=e.O()}]);