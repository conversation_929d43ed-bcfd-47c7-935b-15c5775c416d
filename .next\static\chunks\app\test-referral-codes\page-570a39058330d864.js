(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2724],{6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>o});var s=a(3915),r=a(3004),i=a(5317),n=a(858);let c=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,r.xI)(c),l=(0,i.aU)(c);(0,n.c7)(c)},6934:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var s=a(5155),r=a(2115),i=a(5317),n=a(6104),c=a(3592);function o(){let[e,t]=(0,r.useState)(""),[a,o]=(0,r.useState)(!1),l=e=>{t(t=>t+e+"\n")},d=async()=>{t(""),o(!0);try{l("\uD83D\uDD0D Testing Referral Code Conflicts...\n"),l("=== TEST 1: Checking Existing Referral Codes ===");let e=(0,i.rJ)(n.db,c.COLLECTIONS.users),t=(0,i.P)(e),a=await (0,i.GG)(t),s=new Set,r=new Set;a.docs.forEach(e=>{let t=e.data()[c.FIELD_NAMES.referralCode];t&&(s.has(t)&&r.add(t),s.add(t))}),l("Total users: ".concat(a.docs.length)),l("Unique referral codes: ".concat(s.size)),l("Duplicate codes found: ".concat(r.size)),r.size>0?(l("❌ DUPLICATE CODES DETECTED:"),r.forEach(e=>l("   - ".concat(e)))):l("✅ No duplicate codes found"),l("\n=== TEST 2: Testing Current Generation Method ===");let o=new Set,d=new Set;for(let e=0;e<1e3;e++){let t=Date.now().toString().slice(-6),a=Math.random().toString(36).substring(2,8).toUpperCase(),s="MYN".concat(t).concat(a.substring(0,1));o.has(s)&&d.add(s),o.add(s),e%100==0&&await new Promise(e=>setTimeout(e,1))}l("Generated codes: 1000"),l("Unique codes: ".concat(o.size)),l("Duplicates in generation: ".concat(d.size)),l("Collision rate: ".concat((d.size/1e3*100).toFixed(2),"%")),d.size>0&&(l("❌ GENERATION CONFLICTS DETECTED"),l("Sample duplicates:"),Array.from(d).slice(0,5).forEach(e=>{l("   - ".concat(e))})),l("\n=== TEST 3: Checking Conflicts with Database ===");let m=new Set;o.forEach(e=>{s.has(e)&&m.add(e)}),l("Conflicts with existing DB codes: ".concat(m.size)),m.size>0&&(l("❌ DATABASE CONFLICTS DETECTED"),Array.from(m).slice(0,5).forEach(e=>{l("   - ".concat(e))})),l("\n=== TEST 4: Analyzing Timestamp Patterns ===");let u=new Map;o.forEach(e=>{let t=e.substring(3,9);u.set(t,(u.get(t)||0)+1)});let f=Math.max(...Array.from(u.values()));l("Max codes per timestamp: ".concat(f)),l("Timestamp collision potential: ".concat(f>1?"HIGH":"LOW")),l("\n=== TEST 5: Recommended Solution ==="),r.size>0||d.size>0||m.size>0?(l("❌ CRITICAL ISSUE: Referral code conflicts detected!"),l(""),l("RECOMMENDED FIXES:"),l("1. Use proper sequential numbering (MYN0001, MYN0002, etc.)"),l("2. Check for duplicates before creating user"),l("3. Use longer random strings if not sequential"),l("4. Implement retry logic for conflicts"),l(""),l("IMMEDIATE ACTION NEEDED:"),l("- Fix registration to use proper referral code generation"),l("- Clean up duplicate codes in database"),l("- Add unique constraint on referralCode field")):(l("✅ No immediate conflicts detected"),l("However, current method is still risky for scale")),l("\n\uD83C\uDFAF CONCLUSION:"),l("The current referral code generation method is UNSAFE"),l("and likely causing registration failures due to conflicts.")}catch(e){l("❌ Test failed: ".concat(e.message)),l("Error code: ".concat(e.code))}finally{o(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Referral Code Conflict Test"}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("button",{onClick:d,disabled:a,className:"btn-primary mb-4",children:a?"Testing Conflicts...":"Test Referral Code Conflicts"}),(0,s.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,s.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||'Click "Test Referral Code Conflicts" to start...'})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},7262:(e,t,a)=>{Promise.resolve().then(a.bind(a,6934))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,3592,8441,1684,7358],()=>t(7262)),_N_E=e.O()}]);