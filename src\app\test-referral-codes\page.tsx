'use client'

import { useState } from 'react'
import { collection, query, where, getDocs, doc, setDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS } from '@/lib/dataService'

export default function TestReferralCodesPage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const addToResult = (text: string) => {
    setResult(prev => prev + text + '\n')
  }

  const testReferralCodeConflicts = async () => {
    setResult('')
    setIsLoading(true)
    
    try {
      addToResult('🔍 Testing Referral Code Conflicts...\n')
      
      // Test 1: Check existing referral codes in database
      addToResult('=== TEST 1: Checking Existing Referral Codes ===')
      const usersRef = collection(db, COLLECTIONS.users)
      const allUsersQuery = query(usersRef)
      const allUsersSnapshot = await getDocs(allUsersQuery)
      
      const existingCodes = new Set<string>()
      const duplicates = new Set<string>()
      
      allUsersSnapshot.docs.forEach(doc => {
        const data = doc.data()
        const referralCode = data[FIELD_NAMES.referralCode]
        if (referralCode) {
          if (existingCodes.has(referralCode)) {
            duplicates.add(referralCode)
          }
          existingCodes.add(referralCode)
        }
      })
      
      addToResult(`Total users: ${allUsersSnapshot.docs.length}`)
      addToResult(`Unique referral codes: ${existingCodes.size}`)
      addToResult(`Duplicate codes found: ${duplicates.size}`)
      
      if (duplicates.size > 0) {
        addToResult(`❌ DUPLICATE CODES DETECTED:`)
        duplicates.forEach(code => addToResult(`   - ${code}`))
      } else {
        addToResult(`✅ No duplicate codes found`)
      }
      
      // Test 2: Simulate current referral code generation
      addToResult('\n=== TEST 2: Testing Current Generation Method ===')
      const generatedCodes = new Set<string>()
      const generationDuplicates = new Set<string>()
      
      // Generate 1000 codes using current method
      for (let i = 0; i < 1000; i++) {
        const timestamp = Date.now().toString().slice(-4)
        const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase()
        const code = `MY${timestamp}${randomPart}`
        
        if (generatedCodes.has(code)) {
          generationDuplicates.add(code)
        }
        generatedCodes.add(code)
        
        // Small delay to vary timestamp
        if (i % 100 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1))
        }
      }
      
      addToResult(`Generated codes: 1000`)
      addToResult(`Unique codes: ${generatedCodes.size}`)
      addToResult(`Duplicates in generation: ${generationDuplicates.size}`)
      addToResult(`Collision rate: ${(generationDuplicates.size / 1000 * 100).toFixed(2)}%`)
      
      if (generationDuplicates.size > 0) {
        addToResult(`❌ GENERATION CONFLICTS DETECTED`)
        addToResult(`Sample duplicates:`)
        Array.from(generationDuplicates).slice(0, 5).forEach(code => {
          addToResult(`   - ${code}`)
        })
      }
      
      // Test 3: Check conflicts with existing database codes
      addToResult('\n=== TEST 3: Checking Conflicts with Database ===')
      const conflictsWithDB = new Set<string>()
      
      generatedCodes.forEach(code => {
        if (existingCodes.has(code)) {
          conflictsWithDB.add(code)
        }
      })
      
      addToResult(`Conflicts with existing DB codes: ${conflictsWithDB.size}`)
      if (conflictsWithDB.size > 0) {
        addToResult(`❌ DATABASE CONFLICTS DETECTED`)
        Array.from(conflictsWithDB).slice(0, 5).forEach(code => {
          addToResult(`   - ${code}`)
        })
      }
      
      // Test 4: Analyze timestamp patterns
      addToResult('\n=== TEST 4: Analyzing Timestamp Patterns ===')
      const timestampCounts = new Map<string, number>()
      
      generatedCodes.forEach(code => {
        const timestamp = code.substring(2, 6) // Extract timestamp part
        timestampCounts.set(timestamp, (timestampCounts.get(timestamp) || 0) + 1)
      })
      
      const maxTimestampCount = Math.max(...timestampCounts.values())
      addToResult(`Max codes per timestamp: ${maxTimestampCount}`)
      addToResult(`Timestamp collision potential: ${maxTimestampCount > 1 ? 'HIGH' : 'LOW'}`)
      
      // Test 5: Recommend solution
      addToResult('\n=== TEST 5: Recommended Solution ===')
      if (duplicates.size > 0 || generationDuplicates.size > 0 || conflictsWithDB.size > 0) {
        addToResult(`❌ CRITICAL ISSUE: Referral code conflicts detected!`)
        addToResult(``)
        addToResult(`RECOMMENDED FIXES:`)
        addToResult(`1. Use proper sequential numbering (MY0001, MY0002, etc.)`)
        addToResult(`2. Check for duplicates before creating user`)
        addToResult(`3. Use longer random strings if not sequential`)
        addToResult(`4. Implement retry logic for conflicts`)
        addToResult(``)
        addToResult(`IMMEDIATE ACTION NEEDED:`)
        addToResult(`- Fix registration to use proper referral code generation`)
        addToResult(`- Clean up duplicate codes in database`)
        addToResult(`- Add unique constraint on referralCode field`)
      } else {
        addToResult(`✅ No immediate conflicts detected`)
        addToResult(`However, current method is still risky for scale`)
      }
      
      addToResult('\n🎯 CONCLUSION:')
      addToResult('The current referral code generation method is UNSAFE')
      addToResult('and likely causing registration failures due to conflicts.')
      
    } catch (error: any) {
      addToResult(`❌ Test failed: ${error.message}`)
      addToResult(`Error code: ${error.code}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Referral Code Conflict Test</h1>
        
        <div className="glass-card p-6 mb-6">
          <button
            onClick={testReferralCodeConflicts}
            disabled={isLoading}
            className="btn-primary mb-4"
          >
            {isLoading ? 'Testing Conflicts...' : 'Test Referral Code Conflicts'}
          </button>
          
          <div className="bg-black/50 p-4 rounded-lg">
            <pre className="text-white text-sm whitespace-pre-wrap overflow-auto max-h-96">
              {result || 'Click "Test Referral Code Conflicts" to start...'}
            </pre>
          </div>
        </div>
        
        <div className="text-center">
          <a href="/register" className="text-blue-400 hover:text-blue-300 underline">
            ← Back to Registration
          </a>
        </div>
      </div>
    </div>
  )
}
