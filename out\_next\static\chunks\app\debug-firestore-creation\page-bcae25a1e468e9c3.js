(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[432],{6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>o});var r=a(3915),n=a(3004),c=a(5317),i=a(858);let s=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,n.xI)(s),l=(0,c.aU)(s);(0,i.c7)(s)},9618:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(5155),n=a(2115),c=a(3004),i=a(5317),s=a(6104),o=a(3592);function l(){let[e,t]=(0,n.useState)(""),[a,l]=(0,n.useState)(!1),u=e=>{t(t=>t+e+"\n")},d=async()=>{t(""),l(!0);try{var e,a,r,n,d;let t;u("\uD83D\uDD0D Debugging Firestore Document Creation...\n"),u("=== STEP 1: Firebase Auth Test ===");let l="debug".concat(Date.now(),"@example.com");u("Creating auth user: ".concat(l));let m=(await (0,c.eJ)(s.j2,l,"debug123456")).user;u("✅ Auth user created: ".concat(m.uid)),u("   Email: ".concat(m.email)),u("   Email verified: ".concat(m.emailVerified)),await new Promise(e=>setTimeout(e,2e3)),u("   Current auth user: ".concat(null==(e=s.j2.currentUser)?void 0:e.uid)),u("   Auth state: ".concat(s.j2.currentUser?"authenticated":"not authenticated")),u("\n=== STEP 2: Referral Code Generation ===");try{t=await (0,o.x4)(),u("✅ Referral code generated: ".concat(t))}catch(e){u("❌ Referral code generation failed: ".concat(e.message)),t="MYN".concat(Date.now().toString().slice(-4)),u("   Using fallback code: ".concat(t))}u("\n=== STEP 3: Preparing User Data ===");let E={[o.FIELD_NAMES.name]:"Debug Test User",[o.FIELD_NAMES.email]:l.toLowerCase(),[o.FIELD_NAMES.mobile]:"9876543210",[o.FIELD_NAMES.referralCode]:t,[o.FIELD_NAMES.referredBy]:"",[o.FIELD_NAMES.referralBonusCredited]:!1,[o.FIELD_NAMES.plan]:"Trial",[o.FIELD_NAMES.planExpiry]:null,[o.FIELD_NAMES.activeDays]:0,[o.FIELD_NAMES.joinedDate]:i.Dc.now(),[o.FIELD_NAMES.wallet]:0,[o.FIELD_NAMES.totalVideos]:0,[o.FIELD_NAMES.todayVideos]:0,[o.FIELD_NAMES.lastVideoDate]:null,[o.FIELD_NAMES.videoDuration]:30,status:"active"};u("✅ User data prepared"),u("   Fields count: ".concat(Object.keys(E).length)),u("   Document path: ".concat(o.COLLECTIONS.users,"/").concat(m.uid)),u("\n=== STEP 4: Testing Firestore Permissions ==="),u("   Current user UID: ".concat(null==(a=s.j2.currentUser)?void 0:a.uid)),u("   Target document UID: ".concat(m.uid)),u("   UIDs match: ".concat((null==(r=s.j2.currentUser)?void 0:r.uid)===m.uid)),u("   User email: ".concat(null==(n=s.j2.currentUser)?void 0:n.email)),u("   Is admin email: ".concat((null==(d=s.j2.currentUser)?void 0:d.email)==="<EMAIL>")),u("\n=== STEP 5: Creating Firestore Document ===");let D=(0,i.H9)(s.db,o.COLLECTIONS.users,m.uid);u("   Document reference: ".concat(D.path));try{u("   Attempting setDoc..."),await (0,i.BN)(D,E),u("✅ setDoc completed successfully"),u("\n=== STEP 6: Verifying Document Creation ===");let e=await (0,i.x7)(D);if(e.exists()){let t=e.data();u("✅ Document verification successful"),u("   Document ID: ".concat(e.id)),u("   Document fields: ".concat(Object.keys(t).length)),u("   Name: ".concat(t[o.FIELD_NAMES.name])),u("   Email: ".concat(t[o.FIELD_NAMES.email])),u("   Plan: ".concat(t[o.FIELD_NAMES.plan])),u("   Referral Code: ".concat(t[o.FIELD_NAMES.referralCode])),u("\n\uD83C\uDF89 SUCCESS: Firestore document creation works!"),u("The issue might be in the registration form logic.")}else u("❌ Document verification failed - document does not exist"),u("   This indicates a Firestore rules or permissions issue")}catch(e){u("❌ setDoc failed: ".concat(e.message)),u("   Error code: ".concat(e.code)),u("   Error name: ".concat(e.name)),"permission-denied"===e.code?(u("\n\uD83D\uDD27 PERMISSION DENIED ANALYSIS:"),u("   - Check Firestore security rules"),u("   - Verify user authentication state"),u("   - Ensure rules allow authenticated users to create their own documents")):"unavailable"===e.code&&(u("\n\uD83D\uDD27 FIRESTORE UNAVAILABLE:"),u("   - Check internet connection"),u("   - Verify Firestore is enabled in Firebase console"),u("   - Check Firebase project configuration")),u("\n   Full error object:"),u("   ".concat(JSON.stringify(e,null,2)))}u("\n=== STEP 7: Cleanup ===");try{await m.delete(),u("✅ Test user deleted")}catch(e){u("⚠️ User deletion failed: ".concat(e.message))}try{await (0,c.CI)(s.j2),u("✅ Signed out successfully")}catch(e){u("⚠️ Sign out failed: ".concat(e.message))}}catch(e){u("❌ Debug test failed: ".concat(e.message)),u("   Error code: ".concat(e.code)),u("   Full error: ".concat(JSON.stringify(e,null,2)))}finally{l(!1)}};return(0,r.jsx)("div",{className:"min-h-screen p-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Debug Firestore Document Creation"}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsx)("button",{onClick:d,disabled:a,className:"btn-primary mb-4",children:a?"Running Debug Test...":"Debug Firestore Creation"}),(0,r.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,r.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||'Click "Debug Firestore Creation" to start...'})})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},9712:(e,t,a)=>{Promise.resolve().then(a.bind(a,9618))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,3592,8441,1684,7358],()=>t(9712)),_N_E=e.O()}]);