"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-referral-codes/page",{

/***/ "(app-pages-browser)/./src/lib/dataService.ts":
/*!********************************!*\
  !*** ./src/lib/dataService.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   FIELD_NAMES: () => (/* binding */ FIELD_NAMES),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   addTransaction: () => (/* binding */ addTransaction),\n/* harmony export */   checkQuickVideoAdvantageActive: () => (/* binding */ checkQuickVideoAdvantageActive),\n/* harmony export */   checkReferralCodeExists: () => (/* binding */ checkReferralCodeExists),\n/* harmony export */   checkWithdrawalAllowed: () => (/* binding */ checkWithdrawalAllowed),\n/* harmony export */   createWithdrawalRequest: () => (/* binding */ createWithdrawalRequest),\n/* harmony export */   deleteNotification: () => (/* binding */ deleteNotification),\n/* harmony export */   generateSequentialReferralCode: () => (/* binding */ generateSequentialReferralCode),\n/* harmony export */   generateSimpleReferralCode: () => (/* binding */ generateSimpleReferralCode),\n/* harmony export */   generateUniqueReferralCode: () => (/* binding */ generateUniqueReferralCode),\n/* harmony export */   getAllNotifications: () => (/* binding */ getAllNotifications),\n/* harmony export */   getBankDetails: () => (/* binding */ getBankDetails),\n/* harmony export */   getCurrentReferralCounter: () => (/* binding */ getCurrentReferralCounter),\n/* harmony export */   getPlanEarning: () => (/* binding */ getPlanEarning),\n/* harmony export */   getPlanValidityDays: () => (/* binding */ getPlanValidityDays),\n/* harmony export */   getPlanVideoDuration: () => (/* binding */ getPlanVideoDuration),\n/* harmony export */   getReferralBonus: () => (/* binding */ getReferralBonus),\n/* harmony export */   getReferrals: () => (/* binding */ getReferrals),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUnreadNotificationCount: () => (/* binding */ getUnreadNotificationCount),\n/* harmony export */   getUnreadNotifications: () => (/* binding */ getUnreadNotifications),\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   getUserNotifications: () => (/* binding */ getUserNotifications),\n/* harmony export */   getUserVideoSettings: () => (/* binding */ getUserVideoSettings),\n/* harmony export */   getUserWithdrawals: () => (/* binding */ getUserWithdrawals),\n/* harmony export */   getVideoCountData: () => (/* binding */ getVideoCountData),\n/* harmony export */   getWalletData: () => (/* binding */ getWalletData),\n/* harmony export */   grantQuickVideoAdvantage: () => (/* binding */ grantQuickVideoAdvantage),\n/* harmony export */   hasPendingWithdrawals: () => (/* binding */ hasPendingWithdrawals),\n/* harmony export */   hasUnreadNotifications: () => (/* binding */ hasUnreadNotifications),\n/* harmony export */   isNotificationRead: () => (/* binding */ isNotificationRead),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead),\n/* harmony export */   processReferralBonus: () => (/* binding */ processReferralBonus),\n/* harmony export */   removeQuickVideoAdvantage: () => (/* binding */ removeQuickVideoAdvantage),\n/* harmony export */   saveBankDetails: () => (/* binding */ saveBankDetails),\n/* harmony export */   updateUserData: () => (/* binding */ updateUserData),\n/* harmony export */   updateUserPlanExpiry: () => (/* binding */ updateUserPlanExpiry),\n/* harmony export */   updateUserVideoDuration: () => (/* binding */ updateUserVideoDuration),\n/* harmony export */   updateVideoCount: () => (/* binding */ updateVideoCount),\n/* harmony export */   updateWalletBalance: () => (/* binding */ updateWalletBalance)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\n// Field names for Firestore collections\nconst FIELD_NAMES = {\n    // User fields\n    name: 'name',\n    email: 'email',\n    mobile: 'mobile',\n    referralCode: 'referralCode',\n    referredBy: 'referredBy',\n    referralBonusCredited: 'referralBonusCredited',\n    plan: 'plan',\n    planExpiry: 'planExpiry',\n    activeDays: 'activeDays',\n    joinedDate: 'joinedDate',\n    // Wallet fields\n    wallet: 'wallet',\n    // Bank details fields\n    bankAccountHolderName: 'bankAccountHolderName',\n    bankAccountNumber: 'bankAccountNumber',\n    bankIfscCode: 'bankIfscCode',\n    bankName: 'bankName',\n    bankDetailsUpdated: 'bankDetailsUpdated',\n    // Video fields\n    totalVideos: 'totalVideos',\n    todayVideos: 'todayVideos',\n    lastVideoDate: 'lastVideoDate',\n    videoDuration: 'videoDuration',\n    // Quick Video Advantage fields\n    quickVideoAdvantage: 'quickVideoAdvantage',\n    quickVideoAdvantageExpiry: 'quickVideoAdvantageExpiry',\n    quickVideoAdvantageDays: 'quickVideoAdvantageDays',\n    quickVideoAdvantageSeconds: 'quickVideoAdvantageSeconds',\n    quickVideoAdvantageGrantedBy: 'quickVideoAdvantageGrantedBy',\n    quickVideoAdvantageGrantedAt: 'quickVideoAdvantageGrantedAt',\n    // Transaction fields\n    type: 'type',\n    amount: 'amount',\n    date: 'date',\n    status: 'status',\n    description: 'description',\n    userId: 'userId'\n};\n// Collection names\nconst COLLECTIONS = {\n    users: 'users',\n    transactions: 'transactions',\n    withdrawals: 'withdrawals',\n    plans: 'plans',\n    settings: 'settings',\n    notifications: 'notifications',\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Get user data\nasync function getUserData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserData:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            var _data_FIELD_NAMES_planExpiry, _data_FIELD_NAMES_joinedDate, _data_FIELD_NAMES_quickVideoAdvantageExpiry, _data_FIELD_NAMES_quickVideoAdvantageGrantedAt;\n            const data = userDoc.data();\n            // Ensure all values are properly typed\n            const result = {\n                name: String(data[FIELD_NAMES.name] || ''),\n                email: String(data[FIELD_NAMES.email] || ''),\n                mobile: String(data[FIELD_NAMES.mobile] || ''),\n                referralCode: String(data[FIELD_NAMES.referralCode] || ''),\n                referredBy: String(data[FIELD_NAMES.referredBy] || ''),\n                plan: String(data[FIELD_NAMES.plan] || 'Trial'),\n                planExpiry: ((_data_FIELD_NAMES_planExpiry = data[FIELD_NAMES.planExpiry]) === null || _data_FIELD_NAMES_planExpiry === void 0 ? void 0 : _data_FIELD_NAMES_planExpiry.toDate()) || null,\n                activeDays: Number(data[FIELD_NAMES.activeDays] || 0),\n                joinedDate: ((_data_FIELD_NAMES_joinedDate = data[FIELD_NAMES.joinedDate]) === null || _data_FIELD_NAMES_joinedDate === void 0 ? void 0 : _data_FIELD_NAMES_joinedDate.toDate()) || new Date(),\n                videoDuration: Number(data[FIELD_NAMES.videoDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)),\n                // Quick Video Advantage fields\n                quickVideoAdvantage: Boolean(data[FIELD_NAMES.quickVideoAdvantage] || false),\n                quickVideoAdvantageExpiry: ((_data_FIELD_NAMES_quickVideoAdvantageExpiry = data[FIELD_NAMES.quickVideoAdvantageExpiry]) === null || _data_FIELD_NAMES_quickVideoAdvantageExpiry === void 0 ? void 0 : _data_FIELD_NAMES_quickVideoAdvantageExpiry.toDate()) || null,\n                quickVideoAdvantageDays: Number(data[FIELD_NAMES.quickVideoAdvantageDays] || 0),\n                quickVideoAdvantageSeconds: Number(data[FIELD_NAMES.quickVideoAdvantageSeconds] || 30),\n                quickVideoAdvantageGrantedBy: String(data[FIELD_NAMES.quickVideoAdvantageGrantedBy] || ''),\n                quickVideoAdvantageGrantedAt: ((_data_FIELD_NAMES_quickVideoAdvantageGrantedAt = data[FIELD_NAMES.quickVideoAdvantageGrantedAt]) === null || _data_FIELD_NAMES_quickVideoAdvantageGrantedAt === void 0 ? void 0 : _data_FIELD_NAMES_quickVideoAdvantageGrantedAt.toDate()) || null\n            };\n            console.log('getUserData result:', result);\n            return result;\n        }\n        return null;\n    } catch (error) {\n        console.error('Error getting user data:', error);\n        return null // Return null instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get wallet data\nasync function getWalletData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getWalletData:', userId);\n            return {\n                wallet: 0\n            };\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const result = {\n                wallet: Number(data[FIELD_NAMES.wallet] || 0)\n            };\n            console.log('getWalletData result:', result);\n            return result;\n        }\n        return {\n            wallet: 0\n        };\n    } catch (error) {\n        console.error('Error getting wallet data:', error);\n        return {\n            wallet: 0\n        } // Return default instead of throwing\n        ;\n    }\n}\n// Get video count data\nasync function getVideoCountData(userId) {\n    try {\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            var _data_FIELD_NAMES_lastVideoDate;\n            const data = userDoc.data();\n            const totalVideos = data[FIELD_NAMES.totalVideos] || 0;\n            const todayVideos = data[FIELD_NAMES.todayVideos] || 0;\n            const lastVideoDate = (_data_FIELD_NAMES_lastVideoDate = data[FIELD_NAMES.lastVideoDate]) === null || _data_FIELD_NAMES_lastVideoDate === void 0 ? void 0 : _data_FIELD_NAMES_lastVideoDate.toDate();\n            // Check if it's a new day\n            const today = new Date();\n            const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n            return {\n                totalVideos,\n                todayVideos: isNewDay ? 0 : todayVideos,\n                remainingVideos: Math.max(0, 50 - (isNewDay ? 0 : todayVideos))\n            };\n        }\n        return {\n            totalVideos: 0,\n            todayVideos: 0,\n            remainingVideos: 50\n        };\n    } catch (error) {\n        console.error('Error getting video count data:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUserData(userId, data) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), data);\n    } catch (error) {\n        console.error('Error updating user data:', error);\n        throw error;\n    }\n}\n// Add transaction\nasync function addTransaction(userId, transactionData) {\n    try {\n        const transaction = {\n            [FIELD_NAMES.userId]: userId,\n            [FIELD_NAMES.type]: transactionData.type,\n            [FIELD_NAMES.amount]: transactionData.amount,\n            [FIELD_NAMES.description]: transactionData.description,\n            [FIELD_NAMES.status]: transactionData.status || 'completed',\n            [FIELD_NAMES.date]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), transaction);\n    } catch (error) {\n        console.error('Error adding transaction:', error);\n        throw error;\n    }\n}\n// Get transactions\nasync function getTransactions(userId) {\n    let limitCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getTransactions:', userId);\n            return [];\n        }\n        // Temporary fix: Use only where clause without orderBy to avoid index requirement\n        // TODO: Create composite index in Firebase console for better performance\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.userId, '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = querySnapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_date;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: (_doc_data_FIELD_NAMES_date = doc.data()[FIELD_NAMES.date]) === null || _doc_data_FIELD_NAMES_date === void 0 ? void 0 : _doc_data_FIELD_NAMES_date.toDate()\n            };\n        });\n        // Sort in memory since we can't use orderBy without index\n        transactions.sort((a, b)=>{\n            const dateA = a.date || new Date(0);\n            const dateB = b.date || new Date(0);\n            return dateB.getTime() - dateA.getTime() // Descending order\n            ;\n        });\n        return transactions;\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        return [] // Return empty array instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get referrals\nasync function getReferrals(referralCode) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referredBy, '==', referralCode));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>{\n            var _doc_data_FIELD_NAMES_joinedDate;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: (_doc_data_FIELD_NAMES_joinedDate = doc.data()[FIELD_NAMES.joinedDate]) === null || _doc_data_FIELD_NAMES_joinedDate === void 0 ? void 0 : _doc_data_FIELD_NAMES_joinedDate.toDate()\n            };\n        });\n    } catch (error) {\n        console.error('Error getting referrals:', error);\n        throw error;\n    }\n}\n// Update video count\nasync function updateVideoCount(userId) {\n    try {\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.todayVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n        });\n    } catch (error) {\n        console.error('Error updating video count:', error);\n        throw error;\n    }\n}\n// Update wallet balance\nasync function updateWalletBalance(userId, amount) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.wallet]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(amount)\n        });\n    } catch (error) {\n        console.error('Error updating wallet balance:', error);\n        throw error;\n    }\n}\n// Save bank details\nasync function saveBankDetails(userId, bankDetails) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            throw new Error('Invalid userId provided');\n        }\n        // Validate bank details\n        validateBankDetails(bankDetails);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),\n            [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),\n            [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),\n            [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),\n            [FIELD_NAMES.bankDetailsUpdated]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        });\n        console.log('Bank details saved successfully for user:', userId);\n    } catch (error) {\n        console.error('Error saving bank details:', error);\n        throw error;\n    }\n}\n// Get bank details\nasync function getBankDetails(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getBankDetails:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Check if bank details exist\n            if (data[FIELD_NAMES.bankAccountNumber]) {\n                const result = {\n                    accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),\n                    accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),\n                    ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),\n                    bankName: String(data[FIELD_NAMES.bankName] || '')\n                };\n                console.log('getBankDetails result found');\n                return result;\n            }\n        }\n        console.log('No bank details found for user');\n        return null;\n    } catch (error) {\n        console.error('Error getting bank details:', error);\n        return null;\n    }\n}\n// Get plan-based earning amount (per batch of 50 videos)\nfunction getPlanEarning(plan) {\n    const planEarnings = {\n        'Trial': 10,\n        'Starter': 25,\n        'Basic': 75,\n        'Premium': 150,\n        'Gold': 200,\n        'Platinum': 250,\n        'Diamond': 400\n    };\n    return planEarnings[plan] || 10 // Default to trial earning (per batch of 50 videos)\n    ;\n}\n// Get plan-based video duration (in seconds)\nfunction getPlanVideoDuration(plan) {\n    const planDurations = {\n        'Trial': 30,\n        'Starter': 300,\n        'Basic': 300,\n        'Premium': 300,\n        'Gold': 180,\n        'Platinum': 120,\n        'Diamond': 60 // 1 minute (Rs 9999 plan)\n    };\n    return planDurations[plan] || 30 // Default to trial duration (30 seconds)\n    ;\n}\n// Get plan validity duration in days\nfunction getPlanValidityDays(plan) {\n    const planValidityDays = {\n        'Trial': 2,\n        'Starter': 30,\n        'Basic': 30,\n        'Premium': 30,\n        'Gold': 30,\n        'Platinum': 30,\n        'Diamond': 30,\n        '499': 30,\n        '1499': 30,\n        '2999': 30,\n        '3999': 30,\n        '5999': 30,\n        '9999': 30 // Legacy plan mapping\n    };\n    return planValidityDays[plan] || 2 // Default to trial duration (2 days)\n    ;\n}\n// Check if user's plan is expired based on active days and plan validity\nasync function isUserPlanExpired(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                expired: true,\n                reason: 'User data not found'\n            };\n        }\n        // If user is on Trial plan, check based on joined date\n        if (userData.plan === 'Trial') {\n            const joinedDate = userData.joinedDate || new Date();\n            const today = new Date();\n            const daysSinceJoined = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n            const trialDaysLeft = Math.max(0, 2 - daysSinceJoined);\n            return {\n                expired: trialDaysLeft <= 0,\n                reason: trialDaysLeft <= 0 ? 'Trial period expired' : undefined,\n                daysLeft: trialDaysLeft,\n                activeDays: daysSinceJoined\n            };\n        }\n        // For paid plans, check if planExpiry is set\n        if (userData.planExpiry) {\n            const today = new Date();\n            const expired = today > userData.planExpiry;\n            const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                expired,\n                reason: expired ? 'Plan subscription expired' : undefined,\n                daysLeft,\n                activeDays: userData.activeDays || 0\n            };\n        }\n        // If planExpiry is not set, calculate based on active days and plan validity\n        const planValidityDays = getPlanValidityDays(userData.plan);\n        const currentActiveDays = userData.activeDays || 0;\n        const daysLeft = Math.max(0, planValidityDays - currentActiveDays);\n        const expired = daysLeft <= 0;\n        return {\n            expired,\n            reason: expired ? \"Plan validity period (\".concat(planValidityDays, \" days) exceeded based on active days\") : undefined,\n            daysLeft,\n            activeDays: currentActiveDays\n        };\n    } catch (error) {\n        console.error('Error checking plan expiry:', error);\n        return {\n            expired: true,\n            reason: 'Error checking plan status'\n        };\n    }\n}\n// Update user's plan expiry when admin changes plan\nasync function updateUserPlanExpiry(userId, newPlan, customExpiryDate) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        if (newPlan === 'Trial') {\n            // Trial plan doesn't have expiry, it's based on joined date\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: null\n            });\n        } else {\n            // Set expiry date for paid plans\n            let expiryDate;\n            if (customExpiryDate) {\n                expiryDate = customExpiryDate;\n            } else {\n                // Calculate expiry based on plan validity\n                const validityDays = getPlanValidityDays(newPlan);\n                const today = new Date();\n                expiryDate = new Date(today.getTime() + validityDays * 24 * 60 * 60 * 1000);\n            }\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiryDate)\n            });\n            console.log(\"Updated plan expiry for user \".concat(userId, \" to \").concat(expiryDate.toDateString()));\n        }\n    } catch (error) {\n        console.error('Error updating plan expiry:', error);\n        throw error;\n    }\n}\n// Get referral bonus based on plan\nfunction getReferralBonus(plan) {\n    const referralBonuses = {\n        'Trial': 0,\n        '499': 50,\n        '1499': 150,\n        '2999': 300,\n        '3999': 400,\n        '5999': 700,\n        '9999': 1200,\n        'Starter': 50,\n        'Basic': 150,\n        'Premium': 300,\n        'Gold': 400,\n        'Platinum': 700,\n        'Diamond': 1200\n    };\n    return referralBonuses[plan] || 0;\n}\n// Process referral bonus when admin upgrades user from Trial to paid plan\nasync function processReferralBonus(userId, oldPlan, newPlan) {\n    try {\n        // Only process bonus when upgrading FROM Trial TO a paid plan\n        if (oldPlan !== 'Trial' || newPlan === 'Trial') {\n            console.log('Referral bonus only applies when upgrading from Trial to paid plan');\n            return;\n        }\n        console.log(\"Processing referral bonus for user \".concat(userId, \" upgrading from \").concat(oldPlan, \" to \").concat(newPlan));\n        // Get the user's data to find their referral info\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (!userDoc.exists()) {\n            console.log('User not found');\n            return;\n        }\n        const userData = userDoc.data();\n        const referredBy = userData[FIELD_NAMES.referredBy];\n        const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited];\n        if (!referredBy) {\n            console.log('User was not referred by anyone, skipping bonus processing');\n            return;\n        }\n        if (alreadyCredited) {\n            console.log('Referral bonus already credited for this user, skipping');\n            return;\n        }\n        console.log('Finding referrer with code:', referredBy);\n        // Find the referrer by referral code\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', referredBy), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        if (querySnapshot.empty) {\n            console.log('Referral code not found:', referredBy);\n            return;\n        }\n        const referrerDoc = querySnapshot.docs[0];\n        const referrerId = referrerDoc.id;\n        const bonusAmount = getReferralBonus(newPlan);\n        console.log(\"Found referrer: \".concat(referrerId, \", bonus amount: ₹\").concat(bonusAmount));\n        if (bonusAmount > 0) {\n            // Add bonus to referrer's wallet\n            await updateWalletBalance(referrerId, bonusAmount);\n            // Add 50 videos to referrer's total video count\n            const referrerRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, referrerId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(referrerRef, {\n                [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(50)\n            });\n            // Mark referral bonus as credited for this user\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.referralBonusCredited]: true\n            });\n            // Add transaction record for referral bonus\n            await addTransaction(referrerId, {\n                type: 'referral_bonus',\n                amount: bonusAmount,\n                description: \"Referral bonus for \".concat(newPlan, \" plan upgrade + 50 bonus videos (User: \").concat(userData[FIELD_NAMES.name], \")\")\n            });\n            console.log(\"✅ Referral bonus processed: ₹\".concat(bonusAmount, \" + 50 videos for referrer \").concat(referrerId));\n        } else {\n            console.log('No bonus amount calculated, skipping');\n        }\n    } catch (error) {\n        console.error('❌ Error processing referral bonus:', error);\n    // Don't throw error to avoid breaking plan update\n    }\n}\n// Get user video settings (duration and earning per batch)\nasync function getUserVideoSettings(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                videoDuration: 30,\n                earningPerBatch: 10,\n                plan: 'Trial',\n                hasQuickAdvantage: false\n            };\n        }\n        // Check if user has active quick video advantage\n        const hasActiveQuickAdvantage = checkQuickVideoAdvantageActive(userData);\n        let videoDuration = userData.videoDuration;\n        // If user has active quick video advantage, use custom seconds or default to 30\n        if (hasActiveQuickAdvantage) {\n            videoDuration = userData.quickVideoAdvantageSeconds || 30 // Use custom duration or default to 30 seconds\n            ;\n        } else {\n            // Use plan-based video duration, but allow admin overrides for non-trial users\n            if (!videoDuration || userData.plan === 'Trial') {\n                videoDuration = getPlanVideoDuration(userData.plan);\n            }\n        }\n        return {\n            videoDuration: videoDuration,\n            earningPerBatch: getPlanEarning(userData.plan),\n            plan: userData.plan,\n            hasQuickAdvantage: hasActiveQuickAdvantage,\n            quickAdvantageExpiry: userData.quickVideoAdvantageExpiry\n        };\n    } catch (error) {\n        console.error('Error getting user video settings:', error);\n        return {\n            videoDuration: 30,\n            earningPerBatch: 10,\n            plan: 'Trial',\n            hasQuickAdvantage: false\n        };\n    }\n}\n// Check if user has active quick video advantage\nfunction checkQuickVideoAdvantageActive(userData) {\n    if (!userData.quickVideoAdvantage || !userData.quickVideoAdvantageExpiry) {\n        return false;\n    }\n    const now = new Date();\n    const expiry = userData.quickVideoAdvantageExpiry;\n    return now < expiry;\n}\n// Grant quick video advantage to user (admin function)\nasync function grantQuickVideoAdvantage(userId, days, grantedBy) {\n    let seconds = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 30;\n    try {\n        if (days <= 0 || days > 365) {\n            throw new Error('Days must be between 1 and 365');\n        }\n        if (seconds < 1 || seconds > 420) {\n            throw new Error('Seconds must be between 1 and 420 (7 minutes)');\n        }\n        const now = new Date();\n        const expiry = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: true,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiry),\n            [FIELD_NAMES.quickVideoAdvantageDays]: days,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: seconds,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: grantedBy,\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(now)\n        });\n        console.log(\"Granted quick video advantage to user \".concat(userId, \" for \").concat(days, \" days until \").concat(expiry.toDateString()));\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_granted',\n            amount: 0,\n            description: \"Quick video advantage granted for \".concat(days, \" days by \").concat(grantedBy)\n        });\n        return {\n            success: true,\n            expiry\n        };\n    } catch (error) {\n        console.error('Error granting quick video advantage:', error);\n        throw error;\n    }\n}\n// Remove quick video advantage from user (admin function)\nasync function removeQuickVideoAdvantage(userId, removedBy) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: false,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: null,\n            [FIELD_NAMES.quickVideoAdvantageDays]: 0,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: 30,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: '',\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: null\n        });\n        console.log(\"Removed quick video advantage from user \".concat(userId));\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_removed',\n            amount: 0,\n            description: \"Quick video advantage removed by \".concat(removedBy)\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error removing quick video advantage:', error);\n        throw error;\n    }\n}\n// Update user video duration (admin function)\nasync function updateUserVideoDuration(userId, durationInSeconds) {\n    try {\n        // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)\n        const isQuickDuration = [\n            1,\n            10,\n            30\n        ].includes(durationInSeconds);\n        const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420;\n        if (!isQuickDuration && !isStandardDuration) {\n            throw new Error('Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration');\n        }\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.videoDuration]: durationInSeconds\n        });\n        console.log(\"Updated video duration for user \".concat(userId, \" to \").concat(durationInSeconds, \" seconds\"));\n    } catch (error) {\n        console.error('Error updating user video duration:', error);\n        throw error;\n    }\n}\n// Validate bank details\nfunction validateBankDetails(bankDetails) {\n    const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails;\n    if (!accountHolderName || accountHolderName.trim().length < 2) {\n        throw new Error('Account holder name must be at least 2 characters long');\n    }\n    if (!accountNumber || !/^\\d{9,18}$/.test(accountNumber.trim())) {\n        throw new Error('Account number must be 9-18 digits');\n    }\n    if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {\n        throw new Error('Invalid IFSC code format (e.g., SBIN0001234)');\n    }\n    if (!bankName || bankName.trim().length < 2) {\n        throw new Error('Bank name must be at least 2 characters long');\n    }\n}\n// Add notification (admin function) - All notifications are now blocking\nasync function addNotification(notification) {\n    try {\n        const notificationData = {\n            title: notification.title,\n            message: notification.message,\n            type: notification.type,\n            targetUsers: notification.targetUsers,\n            userIds: notification.userIds || [],\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdBy: notification.createdBy\n        };\n        console.log('Adding notification to Firestore:', notificationData);\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), notificationData);\n        console.log('Notification added successfully with ID:', docRef.id);\n        // Verify the notification was added\n        const addedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n        if (addedDoc.exists()) {\n            console.log('Notification verified in database:', addedDoc.data());\n        } else {\n            console.warn('Notification not found after adding');\n        }\n        return docRef.id;\n    } catch (error) {\n        console.error('Error adding notification:', error);\n        throw error;\n    }\n}\n// Get notifications for a user\nasync function getUserNotifications(userId) {\n    let limitCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserNotifications:', userId);\n            return [];\n        }\n        console.log(\"Loading notifications for user: \".concat(userId));\n        // Try to get notifications with fallback for indexing issues\n        let allUsersSnapshot, specificUserSnapshot;\n        try {\n            // Get notifications targeted to all users\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n            console.log(\"Found \".concat(allUsersSnapshot.docs.length, \" notifications for all users\"));\n        } catch (error) {\n            console.warn('Error querying all users notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n        }\n        try {\n            // Get notifications targeted to specific user\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n            console.log(\"Found \".concat(specificUserSnapshot.docs.length, \" notifications for specific user\"));\n        } catch (error) {\n            console.warn('Error querying specific user notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n        }\n        const notifications = [];\n        // Process all users notifications\n        allUsersSnapshot.docs.forEach((doc)=>{\n            var _doc_data_createdAt;\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: ((_doc_data_createdAt = doc.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate()) || new Date()\n            });\n        });\n        // Process specific user notifications\n        specificUserSnapshot.docs.forEach((doc)=>{\n            var _doc_data_createdAt;\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: ((_doc_data_createdAt = doc.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate()) || new Date()\n            });\n        });\n        // Sort by creation date (newest first)\n        notifications.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        const finalNotifications = notifications.slice(0, limitCount);\n        console.log(\"Returning \".concat(finalNotifications.length, \" total notifications for user\"));\n        return finalNotifications;\n    } catch (error) {\n        console.error('Error getting user notifications:', error);\n        return [];\n    }\n}\n// Get all notifications (admin function)\nasync function getAllNotifications() {\n    let limitCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50;\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const notifications = querySnapshot.docs.map((doc)=>{\n            var _doc_data_createdAt;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                createdAt: ((_doc_data_createdAt = doc.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate()) || new Date()\n            };\n        });\n        return notifications;\n    } catch (error) {\n        console.error('Error getting all notifications:', error);\n        return [];\n    }\n}\n// Delete notification (admin function)\nasync function deleteNotification(notificationId) {\n    try {\n        if (!notificationId || typeof notificationId !== 'string') {\n            throw new Error('Invalid notification ID provided');\n        }\n        console.log('Deleting notification:', notificationId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications, notificationId));\n        console.log('Notification deleted successfully');\n    } catch (error) {\n        console.error('Error deleting notification:', error);\n        throw error;\n    }\n}\n// Mark notification as read\nasync function markNotificationAsRead(notificationId, userId) {\n    try {\n        // For now, we'll store read status in localStorage since it's user-specific\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        if (!readNotifications.includes(notificationId)) {\n            readNotifications.push(notificationId);\n            localStorage.setItem(\"read_notifications_\".concat(userId), JSON.stringify(readNotifications));\n        }\n    } catch (error) {\n        console.error('Error marking notification as read:', error);\n    }\n}\n// Check if notification is read\nfunction isNotificationRead(notificationId, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        return readNotifications.includes(notificationId);\n    } catch (error) {\n        console.error('Error checking notification read status:', error);\n        return false;\n    }\n}\n// Get unread notification count\nfunction getUnreadNotificationCount(notifications, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        return notifications.filter((notification)=>!readNotifications.includes(notification.id)).length;\n    } catch (error) {\n        console.error('Error getting unread notification count:', error);\n        return 0;\n    }\n}\n// Get unread notifications - All notifications are now blocking\nasync function getUnreadNotifications(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUnreadNotifications:', userId);\n            return [];\n        }\n        console.log(\"Loading unread notifications for user: \".concat(userId));\n        // Get all notifications for the user\n        const allNotifications = await getUserNotifications(userId, 50);\n        // Filter for unread notifications\n        const readNotifications = JSON.parse(localStorage.getItem(\"read_notifications_\".concat(userId)) || '[]');\n        const unreadNotifications = allNotifications.filter((notification)=>notification.id && !readNotifications.includes(notification.id));\n        console.log(\"Found \".concat(unreadNotifications.length, \" unread notifications\"));\n        return unreadNotifications;\n    } catch (error) {\n        console.error('Error getting unread notifications:', error);\n        return [];\n    }\n}\n// Check if user has unread notifications\nasync function hasUnreadNotifications(userId) {\n    try {\n        const unreadNotifications = await getUnreadNotifications(userId);\n        return unreadNotifications.length > 0;\n    } catch (error) {\n        console.error('Error checking for unread notifications:', error);\n        return false;\n    }\n}\n// Check if user has pending withdrawals\nasync function hasPendingWithdrawals(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking pending withdrawals:', error);\n        return false;\n    }\n}\n// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)\nasync function checkWithdrawalAllowed(userId) {\n    try {\n        // Check user plan first\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                allowed: false,\n                reason: 'Unable to verify user information. Please try again.'\n            };\n        }\n        // Check if user is on trial plan\n        if (userData.plan === 'Trial') {\n            return {\n                allowed: false,\n                reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'\n            };\n        }\n        // Check if user has pending withdrawals\n        const hasPending = await hasPendingWithdrawals(userId);\n        if (hasPending) {\n            return {\n                allowed: false,\n                reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'\n            };\n        }\n        const now = new Date();\n        const currentHour = now.getHours();\n        // Check time restrictions (10 AM to 6 PM)\n        if (currentHour < 10 || currentHour >= 18) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'\n            };\n        }\n        // Check admin leave day\n        const { isAdminLeaveDay } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(now);\n        if (isAdminLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on admin leave/holiday days'\n            };\n        }\n        // Check user leave day\n        const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\"));\n        const isUserLeave = await isUserOnLeave(userId, now);\n        if (isUserLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on your leave days'\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking withdrawal allowed:', error);\n        return {\n            allowed: false,\n            reason: 'Unable to verify withdrawal eligibility. Please try again.'\n        };\n    }\n}\n// Create withdrawal request\nasync function createWithdrawalRequest(userId, amount, bankDetails) {\n    try {\n        // Check minimum withdrawal amount\n        if (amount < 50) {\n            throw new Error('Minimum withdrawal amount is ₹50');\n        }\n        // Check if withdrawal is allowed\n        const withdrawalCheck = await checkWithdrawalAllowed(userId);\n        if (!withdrawalCheck.allowed) {\n            throw new Error(withdrawalCheck.reason);\n        }\n        // Check if user has sufficient balance\n        const walletData = await getWalletData(userId);\n        if (walletData.wallet < amount) {\n            throw new Error('Insufficient wallet balance');\n        }\n        // Debit the amount from user's wallet immediately\n        await updateWalletBalance(userId, -amount);\n        // Add transaction record for withdrawal debit\n        await addTransaction(userId, {\n            type: 'withdrawal_request',\n            amount: -amount,\n            description: \"Withdrawal request submitted - ₹\".concat(amount, \" debited from wallet\")\n        });\n        const withdrawalData = {\n            userId,\n            amount,\n            bankDetails,\n            status: 'pending',\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), withdrawalData);\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating withdrawal request:', error);\n        throw error;\n    }\n}\n// Get user withdrawals\nasync function getUserWithdrawals(userId) {\n    let limitCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>{\n            var _doc_data_date;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: (_doc_data_date = doc.data().date) === null || _doc_data_date === void 0 ? void 0 : _doc_data_date.toDate()\n            };\n        });\n    } catch (error) {\n        console.error('Error getting user withdrawals:', error);\n        return [];\n    }\n}\n// Generate sequential referral code - using count-based approach like working platform\nasync function generateSequentialReferralCode() {\n    try {\n        console.log('Generating referral code...');\n        // Method 1: Try to get count from users collection\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getCountFromServer)(usersCollection);\n            const count = snapshot.data().count + 1;\n            const code = \"MYN\".concat(String(count).padStart(4, '0'));\n            // Verify this code doesn't already exist\n            const existingCodeQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', code), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n            const existingSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(existingCodeQuery);\n            if (existingSnapshot.empty) {\n                console.log(\"Generated sequential referral code: \".concat(code));\n                return code;\n            } else {\n                console.log(\"Code \".concat(code, \" already exists, trying alternative method\"));\n            }\n        } catch (countError) {\n            console.log('Count method failed, trying alternative method:', countError);\n        }\n        // Method 2: Find the highest existing code and increment\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            const codesQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(FIELD_NAMES.referralCode, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n            const codesSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(codesQuery);\n            let nextNumber = 1;\n            if (!codesSnapshot.empty) {\n                const lastCode = codesSnapshot.docs[0].data()[FIELD_NAMES.referralCode];\n                if (lastCode && lastCode.startsWith('MYN')) {\n                    const lastNumber = parseInt(lastCode.substring(3));\n                    if (!isNaN(lastNumber)) {\n                        nextNumber = lastNumber + 1;\n                    }\n                }\n            }\n            const code = \"MYN\".concat(String(nextNumber).padStart(4, '0'));\n            console.log(\"Generated incremental referral code: \".concat(code));\n            return code;\n        } catch (incrementError) {\n            console.log('Increment method failed, using fallback:', incrementError);\n        }\n        // Method 3: Fallback to random code\n        const fallbackCode = \"MYN\".concat(Math.floor(1000 + Math.random() * 9000));\n        console.log('Using fallback referral code:', fallbackCode);\n        return fallbackCode;\n    } catch (error) {\n        console.error('Error generating referral code:', error);\n        // Final fallback to random code\n        const fallbackCode = \"MYN\".concat(Math.floor(1000 + Math.random() * 9000));\n        console.log('Using final fallback referral code:', fallbackCode);\n        return fallbackCode;\n    }\n}\n// Check if referral code exists\nasync function checkReferralCodeExists(code) {\n    try {\n        const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n        const codeQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', code), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(codeQuery);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking referral code:', error);\n        return false;\n    }\n}\n// Generate unique referral code with retry logic\nasync function generateUniqueReferralCode() {\n    let maxRetries = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            const code = await generateSequentialReferralCode();\n            const exists = await checkReferralCodeExists(code);\n            if (!exists) {\n                console.log(\"Generated unique referral code: \".concat(code, \" (attempt \").concat(attempt, \")\"));\n                return code;\n            } else {\n                console.log(\"Code \".concat(code, \" already exists, retrying... (attempt \").concat(attempt, \")\"));\n            }\n        } catch (error) {\n            console.error(\"Error in attempt \".concat(attempt, \":\"), error);\n        }\n    }\n    // Final fallback with timestamp to ensure uniqueness\n    const timestamp = Date.now().toString().slice(-4);\n    const fallbackCode = \"MY\".concat(timestamp);\n    console.log(\"Using timestamp-based fallback code: \".concat(fallbackCode));\n    return fallbackCode;\n}\n// Get current referral counter (for admin purposes)\nasync function getCurrentReferralCounter() {\n    try {\n        const counterRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'system', 'referralCounter');\n        const counterDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(counterRef);\n        if (counterDoc.exists()) {\n            return counterDoc.data().lastNumber || 0;\n        }\n        return 0;\n    } catch (error) {\n        console.error('Error getting referral counter:', error);\n        return 0;\n    }\n}\n// Simple referral code generation for registration (no database queries to avoid conflicts)\nfunction generateSimpleReferralCode() {\n    // Use current timestamp + random string for better uniqueness\n    const timestamp = Date.now().toString();\n    const randomPart = Math.random().toString(36).substring(2, 8).toUpperCase();\n    // Create a more unique code using full timestamp with MYN prefix\n    const uniqueCode = \"MYN\".concat(timestamp.slice(-6)).concat(randomPart.substring(0, 1));\n    console.log('Generated simple referral code:', uniqueCode);\n    return uniqueCode;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/dataService.ts\n"));

/***/ })

});