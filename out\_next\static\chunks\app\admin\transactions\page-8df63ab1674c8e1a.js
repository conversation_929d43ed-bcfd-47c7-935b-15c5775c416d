(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4426],{12:(e,t,a)=>{"use strict";a.d(t,{M4:()=>o,_f:()=>l});var s=a(6104),r=a(4752),n=a.n(r);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await n().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await s.j2.signOut(),n().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),n().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},3737:(e,t,a)=>{"use strict";function s(e,t,a){if(!e||0===e.length)return void alert("No data to export");let s=a||Object.keys(e[0]),r=new Blob([[s.join(","),...e.map(e=>s.map(t=>{let a=e[t];if(null==a)return"";if("string"==typeof a){let e=a.replace(/"/g,'""');return e.includes(",")?'"'.concat(e,'"'):e}return a instanceof Date?a.toLocaleDateString():String(a)}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(void 0!==n.download){let e=URL.createObjectURL(r);n.setAttribute("href",e),n.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)}}function r(e){return e.map(e=>{var t;return{Name:e.name,Email:e.email,Mobile:e.mobile,"Referral Code":e.referralCode,"Referred By":e.referredBy||"Direct",Plan:e.plan,"Active Days":e.activeDays,"Total Videos":e.totalVideos,"Today Videos":e.todayVideos,"Video Duration (seconds)":e.videoDuration||300,"Wallet Balance":e.wallet||0,Status:e.status,"Joined Date":(null==(t=e.joinedDate)?void 0:t.toLocaleDateString())||""}})}function n(e){return e.map(e=>{var t;return{"User ID":e.userId,"User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":e.userMobile||"","User Number":e.userNumber||"",Type:e.type,Amount:e.amount,Description:e.description,Status:e.status,Date:(null==(t=e.date)?void 0:t.toLocaleDateString())||""}})}function i(e){return e.map(e=>{var t,a,s,r,n;return{"User Name":e.userName,"User Email":e.userEmail,"Mobile Number":e.userMobile||"","User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount,"Account Holder":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":(null==(s=e.bankDetails)?void 0:s.accountNumber)||"","IFSC Code":(null==(r=e.bankDetails)?void 0:r.ifscCode)||"",Status:e.status,"Request Date":(null==(n=e.requestDate)?void 0:n.toLocaleDateString())||"","Admin Notes":e.adminNotes||""}})}function l(e){return e.map(e=>{var t,a;return{Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":(null==(t=e.createdAt)?void 0:t.toLocaleDateString())||"","Sent Date":(null==(a=e.sentAt)?void 0:a.toLocaleDateString())||""}})}a.d(t,{Bf:()=>s,Fz:()=>r,Pe:()=>l,dB:()=>i,sL:()=>n})},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>c,j2:()=>o});var s=a(3915),r=a(3004),n=a(5317),i=a(858);let l=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,r.xI)(l),c=(0,n.aU)(l);(0,i.c7)(l)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>o,hD:()=>l,wC:()=>c});var s=a(2115),r=a(3004),n=a(6104),i=a(12);function l(){let[e,t]=(0,s.useState)(null),[a,l]=(0,s.useState)(!0);(0,s.useEffect)(()=>{try{let e=(0,r.hg)(n.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),l(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),l(!1)}},[]);let o=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:o}}function o(){let{user:e,loading:t}=l();return(0,s.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=l(),[a,r]=(0,s.useState)(!1),[n,i]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||n,isAdmin:a}}},6740:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var s=a(5155),r=a(2115),n=a(6874),i=a.n(n),l=a(6681),o=a(5317),c=a(6104),d=a(3592),u=a(3737),m=a(4752),x=a.n(m);function h(){let{user:e,loading:t,isAdmin:a}=(0,l.wC)(),[n,m]=(0,r.useState)([]),[h,g]=(0,r.useState)(!0),[p,f]=(0,r.useState)(""),[y,b]=(0,r.useState)(""),[w,N]=(0,r.useState)(""),[v,j]=(0,r.useState)(1);(0,r.useEffect)(()=>{a&&C()},[a]);let C=async()=>{try{g(!0);let t=(0,o.P)((0,o.rJ)(c.db,d.COLLECTIONS.transactions),(0,o.My)("date","desc"),(0,o.AB)(500)),a=await (0,o.GG)(t),s=[];for(let t of a.docs){var e;let a=t.data(),r="Unknown User",n="<EMAIL>",i="N/A",l="N/A";try{let e=await (0,o.GG)((0,o.P)((0,o.rJ)(c.db,d.COLLECTIONS.users),(0,o._M)("__name__","==",a.userId)));if(!e.empty){let t=e.docs[0].data();r=t.name||"Unknown User",n=t.email||"<EMAIL>",i=t.mobile||"N/A",l=t.referralCode||"N/A"}}catch(e){console.error("Error fetching user data:",e)}s.push({id:t.id,userId:a.userId,userName:r,userEmail:n,userMobile:i,userNumber:l,type:a.type,amount:a.amount,description:a.description,date:(null==(e=a.date)?void 0:e.toDate())||new Date,status:a.status||"completed"})}m(s)}catch(e){console.error("Error loading transactions:",e),x().fire({icon:"error",title:"Error",text:"Failed to load transactions. Please try again."})}finally{g(!1)}},S=n.filter(e=>{let t=!p||e.type===p,a=!y||e.status===y,s=!w||e.userName.toLowerCase().includes(w.toLowerCase())||e.userEmail.toLowerCase().includes(w.toLowerCase())||e.userMobile.toLowerCase().includes(w.toLowerCase())||e.userNumber.toLowerCase().includes(w.toLowerCase())||e.description.toLowerCase().includes(w.toLowerCase());return t&&a&&s}),D=Math.ceil(S.length/20),k=(v-1)*20,L=S.slice(k,k+20),E=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),_=e=>{switch(e){case"video_earning":return"Video Earning";case"withdrawal":return"Withdrawal";case"bonus":return"Bonus";case"referral":return"Referral";default:return e.charAt(0).toUpperCase()+e.slice(1)}},A=e=>{switch(e){case"video_earning":return"fas fa-play-circle text-green-500";case"withdrawal":return"fas fa-download text-red-500";case"bonus":return"fas fa-gift text-yellow-500";case"referral":return"fas fa-users text-blue-500";default:return"fas fa-exchange-alt text-gray-500"}};return t||h?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading transactions..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(i(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Transactions"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",S.length]}),(0,s.jsxs)("button",{onClick:()=>{if(0===S.length)return void x().fire({icon:"warning",title:"No Data",text:"No transactions to export."});let e=(0,u.sL)(S);(0,u.Bf)(e,"transactions"),x().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(S.length," transactions to CSV file."),timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:C,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,s.jsx)("input",{type:"text",value:w,onChange:e=>N(e.target.value),placeholder:"Search user, email, mobile, user number, or description...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,s.jsxs)("select",{value:p,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Types"}),(0,s.jsx)("option",{value:"video_earning",children:"Video Earning"}),(0,s.jsx)("option",{value:"withdrawal",children:"Withdrawal"}),(0,s.jsx)("option",{value:"bonus",children:"Bonus"}),(0,s.jsx)("option",{value:"referral",children:"Referral"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,s.jsxs)("select",{value:y,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Status"}),(0,s.jsx)("option",{value:"completed",children:"Completed"}),(0,s.jsx)("option",{value:"pending",children:"Pending"}),(0,s.jsx)("option",{value:"failed",children:"Failed"}),(0,s.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{onClick:()=>{f(""),b(""),N(""),j(1)},className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:"Clear Filters"})})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Mobile"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User Number"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:L.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm text-gray-900",children:e.userMobile})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"text-sm font-medium text-blue-600",children:e.userNumber})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"".concat(A(e.type)," mr-2")}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:_(e.type)})]})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.description})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:"text-sm font-medium ".concat(e.amount>0?"text-green-600":"text-red-600"),children:[e.amount>0?"+":"",E(e.amount)]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.date.toLocaleDateString()," ",e.date.toLocaleTimeString()]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"failed"===e.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id))})]})}),D>1&&(0,s.jsx)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",k+1," to ",Math.min(k+20,S.length)," of ",S.length," results"]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>j(e=>Math.max(1,e-1)),disabled:1===v,className:"px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded disabled:opacity-50",children:"Previous"}),(0,s.jsxs)("span",{className:"px-3 py-1 text-sm bg-blue-500 text-white rounded",children:[v," of ",D]}),(0,s.jsx)("button",{onClick:()=>j(e=>Math.min(D,e+1)),disabled:v===D,className:"px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded disabled:opacity-50",children:"Next"})]})]})})]})})]})}},7699:(e,t,a)=>{Promise.resolve().then(a.bind(a,6740))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>t(7699)),_N_E=e.O()}]);