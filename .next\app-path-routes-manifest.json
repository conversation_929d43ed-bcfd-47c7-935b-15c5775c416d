{"/admin/leaves/page": "/admin/leaves", "/_not-found/page": "/_not-found", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/settings/page": "/admin/settings", "/admin/users/page": "/admin/users", "/admin/upload-users/page": "/admin/upload-users", "/admin/login/page": "/admin/login", "/admin/test-blocking/page": "/admin/test-blocking", "/dashboard/page": "/dashboard", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-firestore/page": "/debug-firestore", "/forgot-password/page": "/forgot-password", "/debug-registration/page": "/debug-registration", "/admin/transactions/page": "/admin/transactions", "/admin/setup/page": "/admin/setup", "/login/page": "/login", "/page": "/", "/plans/page": "/plans", "/profile/page": "/profile", "/register/page": "/register", "/refer/page": "/refer", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-registration/page": "/test-registration", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}