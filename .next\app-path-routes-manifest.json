{"/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/setup/page": "/admin/setup", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/transactions/page": "/admin/transactions", "/admin/upload-users/page": "/admin/upload-users", "/debug-firestore/page": "/debug-firestore", "/forgot-password/page": "/forgot-password", "/admin/users/page": "/admin/users", "/dashboard/page": "/dashboard", "/page": "/", "/debug-registration/page": "/debug-registration", "/login/page": "/login", "/admin/withdrawals/page": "/admin/withdrawals", "/plans/page": "/plans", "/profile/page": "/profile", "/refer/page": "/refer", "/register/page": "/register", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firebase/page": "/test-firebase", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firestore/page": "/test-firestore", "/test-registration/page": "/test-registration", "/test-sequential-codes/page": "/test-sequential-codes", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/test-referral-codes/page": "/test-referral-codes", "/admin/page": "/admin", "/_not-found/page": "/_not-found", "/work/page": "/work"}