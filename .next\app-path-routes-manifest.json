{"/admin/fix-permissions/page": "/admin/fix-permissions", "/_not-found/page": "/_not-found", "/admin/login/page": "/admin/login", "/admin/page": "/admin", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/setup/page": "/admin/setup", "/admin/upload-users/page": "/admin/upload-users", "/admin/transactions/page": "/admin/transactions", "/admin/withdrawals/page": "/admin/withdrawals", "/dashboard/page": "/dashboard", "/debug-firestore/page": "/debug-firestore", "/admin/users/page": "/admin/users", "/forgot-password/page": "/forgot-password", "/debug-registration/page": "/debug-registration", "/page": "/", "/login/page": "/login", "/plans/page": "/plans", "/profile/page": "/profile", "/refer/page": "/refer", "/reset-password/page": "/reset-password", "/register/page": "/register", "/test-firestore/page": "/test-firestore", "/support/page": "/support", "/test-simple-registration/page": "/test-simple-registration", "/test-firebase/page": "/test-firebase", "/test-registration/page": "/test-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}