'use client'

import { useState } from 'react'
import { createUserWithEmailAndPassword, signOut, deleteUser } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp, collection, addDoc } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS } from '@/lib/dataService'

export default function DebugFirestoreIssuePage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const addToResult = (text: string) => {
    setResult(prev => prev + text + '\n')
  }

  const debugFirestoreIssue = async () => {
    setResult('')
    setIsLoading(true)
    
    let testUser: any = null
    
    try {
      addToResult('🔍 Debugging Firestore Document Creation Issue...\n')
      
      // Step 1: Test Firebase Configuration
      addToResult('=== STEP 1: Firebase Configuration Test ===')
      addToResult(`Auth instance: ${auth ? '✅ Initialized' : '❌ Not initialized'}`)
      addToResult(`Firestore instance: ${db ? '✅ Initialized' : '❌ Not initialized'}`)
      addToResult(`Current user: ${auth.currentUser?.uid || 'None'}`)
      
      // Step 2: Test Basic Firestore Write (No Auth Required)
      addToResult('\n=== STEP 2: Basic Firestore Write Test ===')
      try {
        const testDocRef = doc(db, 'test_collection', `test_${Date.now()}`)
        await setDoc(testDocRef, { 
          test: true, 
          timestamp: Timestamp.now(),
          message: 'Basic write test'
        })
        addToResult('✅ Basic Firestore write works')
        
        // Verify read
        const readDoc = await getDoc(testDocRef)
        if (readDoc.exists()) {
          addToResult('✅ Basic Firestore read works')
        } else {
          addToResult('❌ Basic Firestore read failed')
        }
      } catch (basicError: any) {
        addToResult(`❌ Basic Firestore write failed: ${basicError.message}`)
        addToResult(`   Error code: ${basicError.code}`)
      }
      
      // Step 3: Test Firebase Auth
      addToResult('\n=== STEP 3: Firebase Auth Test ===')
      const testEmail = `debug${Date.now()}@test.com`
      const testPassword = 'debug123456'
      
      try {
        const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
        testUser = userCredential.user
        addToResult(`✅ Auth user created: ${testUser.uid}`)
        addToResult(`   Email: ${testUser.email}`)
        addToResult(`   Email verified: ${testUser.emailVerified}`)
        
        // Wait for auth state to propagate
        await new Promise(resolve => setTimeout(resolve, 1000))
        addToResult(`   Current auth user: ${auth.currentUser?.uid}`)
        addToResult(`   Auth state matches: ${auth.currentUser?.uid === testUser.uid}`)
        
      } catch (authError: any) {
        addToResult(`❌ Auth creation failed: ${authError.message}`)
        addToResult(`   Error code: ${authError.code}`)
        return
      }
      
      // Step 4: Test Users Collection Write (With Auth)
      addToResult('\n=== STEP 4: Users Collection Write Test ===')
      try {
        const userDocRef = doc(db, COLLECTIONS.users, testUser.uid)
        addToResult(`   Document path: ${userDocRef.path}`)
        
        const minimalUserData = {
          name: 'Debug Test User',
          email: testEmail,
          mobile: '9876543210',
          plan: 'Trial',
          joinedDate: Timestamp.now(),
          status: 'active'
        }
        
        addToResult('   Attempting minimal user document creation...')
        await setDoc(userDocRef, minimalUserData)
        addToResult('✅ Minimal user document created')
        
        // Verify
        const verifyDoc = await getDoc(userDocRef)
        if (verifyDoc.exists()) {
          addToResult('✅ User document verification successful')
          const data = verifyDoc.data()
          addToResult(`   Name: ${data.name}`)
          addToResult(`   Email: ${data.email}`)
          addToResult(`   Plan: ${data.plan}`)
        } else {
          addToResult('❌ User document verification failed')
        }
        
      } catch (userDocError: any) {
        addToResult(`❌ User document creation failed: ${userDocError.message}`)
        addToResult(`   Error code: ${userDocError.code}`)
        addToResult(`   Error details: ${JSON.stringify(userDocError, null, 2)}`)
        
        if (userDocError.code === 'permission-denied') {
          addToResult('\n🔧 PERMISSION DENIED ANALYSIS:')
          addToResult('   This indicates Firestore security rules are blocking the write')
          addToResult('   Possible causes:')
          addToResult('   1. Rules require authentication but user is not properly authenticated')
          addToResult('   2. Rules have specific conditions that are not met')
          addToResult('   3. Rules are too restrictive for user document creation')
        }
      }
      
      // Step 5: Test Full Registration Data
      addToResult('\n=== STEP 5: Full Registration Data Test ===')
      try {
        const fullUserData = {
          [FIELD_NAMES.name]: 'Debug Full Test User',
          [FIELD_NAMES.email]: testEmail.toLowerCase(),
          [FIELD_NAMES.mobile]: '9876543210',
          [FIELD_NAMES.referralCode]: `MY${Date.now().toString().slice(-4)}AB`,
          [FIELD_NAMES.referredBy]: '',
          [FIELD_NAMES.referralBonusCredited]: false,
          [FIELD_NAMES.plan]: 'Trial',
          [FIELD_NAMES.planExpiry]: null,
          [FIELD_NAMES.activeDays]: 0,
          [FIELD_NAMES.joinedDate]: Timestamp.now(),
          [FIELD_NAMES.wallet]: 0,
          [FIELD_NAMES.totalVideos]: 0,
          [FIELD_NAMES.todayVideos]: 0,
          [FIELD_NAMES.lastVideoDate]: null,
          [FIELD_NAMES.videoDuration]: 30,
          status: 'active'
        }
        
        const fullDocRef = doc(db, COLLECTIONS.users, `${testUser.uid}_full`)
        addToResult('   Attempting full registration data creation...')
        await setDoc(fullDocRef, fullUserData)
        addToResult('✅ Full registration data document created')
        
        // Verify
        const verifyFullDoc = await getDoc(fullDocRef)
        if (verifyFullDoc.exists()) {
          addToResult('✅ Full document verification successful')
          const data = verifyFullDoc.data()
          addToResult(`   Fields count: ${Object.keys(data).length}`)
          addToResult(`   Referral code: ${data[FIELD_NAMES.referralCode]}`)
          addToResult(`   Wallet: ${data[FIELD_NAMES.wallet]}`)
        }
        
      } catch (fullDataError: any) {
        addToResult(`❌ Full registration data creation failed: ${fullDataError.message}`)
        addToResult(`   Error code: ${fullDataError.code}`)
        
        // Test individual field issues
        addToResult('\n   Testing individual fields...')
        const problematicFields = []
        
        for (const [key, value] of Object.entries(fullUserData)) {
          try {
            const testDoc = doc(db, 'test_fields', `field_${key}_${Date.now()}`)
            await setDoc(testDoc, { [key]: value })
          } catch (fieldError: any) {
            problematicFields.push(`${key}: ${fieldError.message}`)
          }
        }
        
        if (problematicFields.length > 0) {
          addToResult(`   Problematic fields: ${problematicFields.join(', ')}`)
        } else {
          addToResult('   All individual fields work fine')
        }
      }
      
      // Step 6: Test Alternative Collection
      addToResult('\n=== STEP 6: Alternative Collection Test ===')
      try {
        const altCollection = collection(db, 'debug_users')
        const altDoc = await addDoc(altCollection, {
          name: 'Alternative Test',
          email: testEmail,
          createdAt: Timestamp.now(),
          userId: testUser.uid
        })
        addToResult(`✅ Alternative collection write works: ${altDoc.id}`)
      } catch (altError: any) {
        addToResult(`❌ Alternative collection failed: ${altError.message}`)
      }
      
      // Step 7: Summary and Recommendations
      addToResult('\n=== STEP 7: Summary and Recommendations ===')
      addToResult('Based on the test results above:')
      addToResult('')
      addToResult('If basic Firestore write works but user document fails:')
      addToResult('  → Check Firestore security rules for users collection')
      addToResult('  → Verify authentication state is properly propagated')
      addToResult('')
      addToResult('If permission denied errors occur:')
      addToResult('  → Review firestore.rules file')
      addToResult('  → Ensure rules allow authenticated users to create their own documents')
      addToResult('')
      addToResult('If specific fields cause issues:')
      addToResult('  → Check for reserved field names or invalid data types')
      addToResult('  → Verify FIELD_NAMES constants are correct')
      
    } catch (error: any) {
      addToResult(`❌ Debug test failed: ${error.message}`)
      addToResult(`   Error code: ${error.code}`)
    } finally {
      // Cleanup
      if (testUser) {
        try {
          await deleteUser(testUser)
          addToResult('\n✅ Test user cleaned up')
        } catch (cleanupError: any) {
          addToResult(`\n⚠️ Cleanup failed: ${cleanupError.message}`)
        }
      }
      
      try {
        await signOut(auth)
        addToResult('✅ Signed out')
      } catch (signOutError: any) {
        addToResult(`⚠️ Sign out failed: ${signOutError.message}`)
      }
      
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Debug Firestore Document Creation Issue</h1>
        
        <div className="glass-card p-6 mb-6">
          <button
            onClick={debugFirestoreIssue}
            disabled={isLoading}
            className="btn-primary mb-4"
          >
            {isLoading ? 'Running Diagnostic...' : 'Run Firestore Diagnostic'}
          </button>
          
          <div className="bg-black/50 p-4 rounded-lg">
            <pre className="text-white text-sm whitespace-pre-wrap overflow-auto max-h-96">
              {result || 'Click "Run Firestore Diagnostic" to start...'}
            </pre>
          </div>
        </div>
        
        <div className="text-center">
          <a href="/register" className="text-blue-400 hover:text-blue-300 underline">
            ← Back to Registration
          </a>
        </div>
      </div>
    </div>
  )
}
