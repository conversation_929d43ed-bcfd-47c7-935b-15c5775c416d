'use client'

import { useState } from 'react'
import { auth, db } from '@/lib/firebase'
import { signInAnonymously } from 'firebase/auth'
import { doc, setDoc, getDoc } from 'firebase/firestore'

export default function TestFirebaseConnectionPage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const testFirebaseConnection = async () => {
    setResult('')
    setIsLoading(true)
    
    try {
      setResult('🔍 Testing Firebase Connection...\n')
      setResult(prev => prev + `Environment: ${window.location.origin}\n`)
      setResult(prev => prev + `Project ID: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}\n`)
      setResult(prev => prev + `Auth Domain: ${process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}\n\n`)
      
      // Test 1: Firebase Auth Connection
      setResult(prev => prev + '📡 Test 1: Firebase Auth Connection\n')
      try {
        const userCredential = await signInAnonymously(auth)
        setResult(prev => prev + `✅ Anonymous auth successful: ${userCredential.user.uid}\n`)
        
        // Test 2: Firestore Connection
        setResult(prev => prev + '\n📡 Test 2: Firestore Connection\n')
        const testDocRef = doc(db, 'test_collection', 'connection_test')
        const testData = {
          timestamp: new Date().toISOString(),
          test: 'Firebase connection test',
          userAgent: navigator.userAgent
        }
        
        await setDoc(testDocRef, testData)
        setResult(prev => prev + '✅ Firestore write successful\n')
        
        const readDoc = await getDoc(testDocRef)
        if (readDoc.exists()) {
          setResult(prev => prev + '✅ Firestore read successful\n')
          setResult(prev => prev + `   Data: ${JSON.stringify(readDoc.data(), null, 2)}\n`)
        } else {
          setResult(prev => prev + '❌ Firestore read failed - document not found\n')
        }
        
        setResult(prev => prev + '\n🎉 All Firebase connections successful!\n')
        setResult(prev => prev + 'The registration issue is likely in the application logic, not connectivity.\n')
        
        // Clean up
        await userCredential.user.delete()
        setResult(prev => prev + '✅ Anonymous user cleaned up\n')
        
      } catch (authError: any) {
        setResult(prev => prev + `❌ Firebase Auth failed: ${authError.message}\n`)
        setResult(prev => prev + `   Code: ${authError.code}\n`)
        
        if (authError.code === 'auth/network-request-failed') {
          setResult(prev => prev + '\n🔧 NETWORK ISSUE DETECTED:\n')
          setResult(prev => prev + '   - Check your internet connection\n')
          setResult(prev => prev + '   - Try disabling VPN/proxy\n')
          setResult(prev => prev + '   - Check if firewall is blocking Firebase\n')
          setResult(prev => prev + '   - Try testing on a different network\n')
        }
      }
      
    } catch (error: any) {
      setResult(prev => prev + `❌ Test failed: ${error.message}\n`)
      setResult(prev => prev + `   Code: ${error.code}\n`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Firebase Connection Test</h1>
        
        <div className="glass-card p-6 mb-6">
          <button
            onClick={testFirebaseConnection}
            disabled={isLoading}
            className="btn-primary mb-4"
          >
            {isLoading ? 'Testing Connection...' : 'Test Firebase Connection'}
          </button>
          
          <div className="bg-black/50 p-4 rounded-lg">
            <pre className="text-white text-sm whitespace-pre-wrap">
              {result || 'Click "Test Firebase Connection" to start...'}
            </pre>
          </div>
        </div>
        
        <div className="text-center">
          <a href="/register" className="text-blue-400 hover:text-blue-300 underline">
            ← Back to Registration
          </a>
        </div>
      </div>
    </div>
  )
}
