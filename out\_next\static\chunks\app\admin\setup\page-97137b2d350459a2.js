(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6912],{1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return c},getImageProps:function(){return r}});let i=s(8229),a=s(8883),n=s(3063),l=i._(s(1193));function r(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let c=n.Image},3557:(e,t,s)=>{Promise.resolve().then(s.bind(s,7434))},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>d,j2:()=>c});var i=s(3915),a=s(3004),n=s(5317),l=s(858);let r=(0,i.Dk)().length?(0,i.Sx)():(0,i.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,a.xI)(r),d=(0,n.aU)(r);(0,l.c7)(r)},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>a.a});var i=s(1469),a=s.n(i)},7434:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var i=s(5155),a=s(2115),n=s(6874),l=s.n(n),r=s(6766),c=s(3004),d=s(5317),o=s(6104),m=s(3592),u=s(4752),x=s.n(u);function h(){let[e,t]=(0,a.useState)(!1),[s,n]=(0,a.useState)(!1),u=async()=>{t(!0);try{let e="<EMAIL>",t=(await (0,c.eJ)(o.j2,e,"123456")).user,s={[m.FIELD_NAMES.name]:"MyTube Admin",[m.FIELD_NAMES.email]:e,[m.FIELD_NAMES.mobile]:"9999999999",[m.FIELD_NAMES.referralCode]:"ADMIN001",[m.FIELD_NAMES.referredBy]:"",[m.FIELD_NAMES.plan]:"Admin",[m.FIELD_NAMES.planExpiry]:null,[m.FIELD_NAMES.activeDays]:999999,[m.FIELD_NAMES.totalVideos]:0,[m.FIELD_NAMES.todayVideos]:0,[m.FIELD_NAMES.lastVideoDate]:null,[m.FIELD_NAMES.wallet]:0,[m.FIELD_NAMES.joinedDate]:d.Dc.now(),[m.FIELD_NAMES.status]:"active",[m.FIELD_NAMES.videoDuration]:300,role:"admin",isAdmin:!0,permissions:["all"]};await (0,d.BN)((0,d.H9)(o.db,m.COLLECTIONS.users,t.uid),s);let i={email:e,name:"MyTube Admin",role:"super_admin",permissions:["all"],createdAt:d.Dc.now(),isActive:!0};await (0,d.BN)((0,d.H9)(o.db,"admins",t.uid),i),await o.j2.signOut(),n(!0),x().fire({icon:"success",title:"Admin Account Created!",html:'\n          <div class="text-left">\n            <p><strong>Email:</strong> <EMAIL></p>\n            <p><strong>Password:</strong> 123456</p>\n            <br>\n            <p>The admin account has been successfully created. You can now login using these credentials.</p>\n          </div>\n        ',confirmButtonText:"Go to Admin Login"}).then(()=>{window.location.href="/admin/login"})}catch(t){console.error("Error creating admin account:",t);let e="Failed to create admin account";"auth/email-already-in-use"===t.code?(e="Admin account already exists! You can login with: <EMAIL> / 123456",x().fire({icon:"info",title:"Admin Account Exists",html:'\n            <div class="text-left">\n              <p><strong>Email:</strong> <EMAIL></p>\n              <p><strong>Password:</strong> 123456</p>\n              <br>\n              <p>The admin account already exists. Use these credentials to login.</p>\n            </div>\n          ',confirmButtonText:"Go to Admin Login"}).then(()=>{window.location.href="/admin/login"})):x().fire({icon:"error",title:"Setup Failed",text:e})}finally{t(!1)}};return(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,i.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,i.jsx)(r.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,i.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Admin Setup"}),(0,i.jsx)("p",{className:"text-white/80",children:"Create the admin account for MyTube"})]}),s?(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-green-400 text-6xl mb-4",children:(0,i.jsx)("i",{className:"fas fa-check-circle"})}),(0,i.jsx)("h2",{className:"text-xl font-bold text-white mb-2",children:"Setup Complete!"}),(0,i.jsx)("p",{className:"text-white/80 mb-6",children:"Admin account has been created successfully."}),(0,i.jsxs)(l(),{href:"/admin/login",className:"btn-primary inline-flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Go to Admin Login"]})]}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-4 border border-blue-500/30",children:[(0,i.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,i.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Admin Account Details"]}),(0,i.jsxs)("div",{className:"text-white/80 text-sm space-y-1",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Password:"})," 123456"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Role:"})," Super Administrator"]})]})]}),(0,i.jsx)("button",{onClick:u,disabled:e,className:"w-full btn-primary flex items-center justify-center",children:e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Admin Account..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-user-shield mr-2"}),"Create Admin Account"]})}),(0,i.jsx)("div",{className:"bg-yellow-500/20 rounded-lg p-4 border border-yellow-500/30",children:(0,i.jsxs)("div",{className:"flex items-start text-yellow-300",children:[(0,i.jsx)("i",{className:"fas fa-exclamation-triangle mr-2 mt-1"}),(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsx)("p",{className:"font-semibold mb-1",children:"Security Notice:"}),(0,i.jsx)("p",{children:"This will create an admin account with full system access. Make sure to change the password after first login for security."})]})]})})]}),(0,i.jsxs)("div",{className:"mt-8 text-center space-y-2",children:[(0,i.jsxs)(l(),{href:"/admin/login",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Admin Login"]}),(0,i.jsx)("br",{}),(0,i.jsxs)(l(),{href:"/",className:"text-white/60 hover:text-white/80 transition-colors inline-flex items-center text-sm",children:[(0,i.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,3592,8441,1684,7358],()=>t(3557)),_N_E=e.O()}]);