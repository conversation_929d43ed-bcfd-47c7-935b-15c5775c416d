"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-firebase-connectivity/page",{

/***/ "(app-pages-browser)/./src/app/test-firebase-connectivity/page.tsx":
/*!*****************************************************!*\
  !*** ./src/app/test-firebase-connectivity/page.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestFirebaseConnectivityPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TestFirebaseConnectivityPage() {\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const addToResult = (text)=>{\n        setResult((prev)=>prev + text + '\\n');\n    };\n    const testConnectivity = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        try {\n            addToResult('🌐 Testing Firebase Connectivity...\\n');\n            // Test 1: Check Firebase instances\n            addToResult('=== TEST 1: Firebase Instances ===');\n            addToResult(\"Auth instance: \".concat(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth ? '✅ Initialized' : '❌ Not initialized'));\n            addToResult(\"Firestore instance: \".concat(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db ? '✅ Initialized' : '❌ Not initialized'));\n            addToResult(\"Auth app: \".concat(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth.app ? '✅ Connected' : '❌ Not connected'));\n            addToResult(\"Firestore app: \".concat(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db.app ? '✅ Connected' : '❌ Not connected'));\n            // Test 2: Check environment variables\n            addToResult('\\n=== TEST 2: Environment Variables ===');\n            addToResult(\"API Key: \".concat( true ? '✅ Set' : 0));\n            addToResult(\"Auth Domain: \".concat( true ? '✅ Set' : 0));\n            addToResult(\"Project ID: \".concat( true ? '✅ Set' : 0));\n            addToResult(\"Project ID Value: \".concat(\"mytube-india\"));\n            // Test 3: Test anonymous authentication (simpler than email/password)\n            addToResult('\\n=== TEST 3: Anonymous Authentication ===');\n            try {\n                addToResult('Attempting anonymous sign-in...');\n                const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signInAnonymously)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n                addToResult(\"✅ Anonymous auth successful: \".concat(userCredential.user.uid));\n                // Test 4: Test Firestore write with anonymous user\n                addToResult('\\n=== TEST 4: Firestore Write Test ===');\n                const testDoc = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'connectivity_test', \"test_\".concat(Date.now()));\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)(testDoc, {\n                    test: true,\n                    timestamp: firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.Timestamp.now(),\n                    message: 'Connectivity test successful'\n                });\n                addToResult('✅ Firestore write successful');\n                // Test 5: Test Firestore read\n                addToResult('\\n=== TEST 5: Firestore Read Test ===');\n                const readDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(testDoc);\n                if (readDoc.exists()) {\n                    addToResult('✅ Firestore read successful');\n                    addToResult(\"   Data: \".concat(JSON.stringify(readDoc.data())));\n                } else {\n                    addToResult('❌ Firestore read failed - document not found');\n                }\n                // Sign out\n                await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n                addToResult('\\n✅ Signed out successfully');\n                addToResult('\\n🎉 ALL TESTS PASSED - Firebase connectivity is working!');\n                addToResult('The registration issue might be specific to email/password authentication.');\n            } catch (authError) {\n                addToResult(\"❌ Anonymous auth failed: \".concat(authError.message));\n                addToResult(\"   Error code: \".concat(authError.code));\n                if (authError.code === 'auth/network-request-failed') {\n                    addToResult('\\n🔧 NETWORK CONNECTIVITY ISSUE DETECTED:');\n                    addToResult('   1. Check your internet connection');\n                    addToResult('   2. Check if firewall/antivirus is blocking Firebase');\n                    addToResult('   3. Try using a different network (mobile hotspot)');\n                    addToResult('   4. Check if your ISP blocks Firebase services');\n                    addToResult('   5. Try using a VPN');\n                    addToResult('');\n                    addToResult('   Firebase domains that need to be accessible:');\n                    addToResult('   - firebase.google.com');\n                    addToResult('   - firestore.googleapis.com');\n                    addToResult('   - identitytoolkit.googleapis.com');\n                    addToResult('   - mytube-india.firebaseapp.com');\n                } else if (authError.code === 'auth/operation-not-allowed') {\n                    addToResult('\\n🔧 FIREBASE CONFIGURATION ISSUE:');\n                    addToResult('   1. Go to Firebase Console → Authentication → Sign-in method');\n                    addToResult('   2. Enable \"Anonymous\" authentication');\n                    addToResult('   3. Or enable \"Email/Password\" authentication');\n                }\n            }\n        } catch (error) {\n            addToResult(\"❌ Connectivity test failed: \".concat(error.message));\n            addToResult(\"   Error code: \".concat(error.code));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const testNetworkDiagnostics = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        try {\n            addToResult('🔍 Network Diagnostics...\\n');\n            // Test 1: Basic fetch to Firebase\n            addToResult('=== TEST 1: Firebase Domain Accessibility ===');\n            try {\n                const response = await fetch('https://firebase.google.com', {\n                    mode: 'no-cors'\n                });\n                addToResult('✅ Firebase.google.com is accessible');\n            } catch (fetchError) {\n                addToResult(\"❌ Firebase.google.com not accessible: \".concat(fetchError.message));\n            }\n            // Test 2: Project-specific domain\n            addToResult('\\n=== TEST 2: Project Domain Accessibility ===');\n            try {\n                const response = await fetch('https://mytube-india.firebaseapp.com', {\n                    mode: 'no-cors'\n                });\n                addToResult('✅ mytube-india.firebaseapp.com is accessible');\n            } catch (fetchError) {\n                addToResult(\"❌ mytube-india.firebaseapp.com not accessible: \".concat(fetchError.message));\n            }\n            // Test 3: Firestore API endpoint\n            addToResult('\\n=== TEST 3: Firestore API Accessibility ===');\n            try {\n                const response = await fetch('https://firestore.googleapis.com', {\n                    mode: 'no-cors'\n                });\n                addToResult('✅ firestore.googleapis.com is accessible');\n            } catch (fetchError) {\n                addToResult(\"❌ firestore.googleapis.com not accessible: \".concat(fetchError.message));\n            }\n            addToResult('\\n=== RECOMMENDATIONS ===');\n            addToResult('If any domains are not accessible:');\n            addToResult('1. Check your internet connection');\n            addToResult('2. Try disabling firewall/antivirus temporarily');\n            addToResult('3. Try using a different network (mobile hotspot)');\n            addToResult('4. Contact your ISP about Firebase access');\n            addToResult('5. Try using a VPN service');\n        } catch (error) {\n            addToResult(\"❌ Network diagnostics failed: \".concat(error.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const testSpecificUID = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        try {\n            addToResult('🔍 Testing Specific UID: b7690183-ab6b-4719-944d-c0a080a59e8c\\n');\n            // Test if this UID exists in Firestore\n            addToResult('=== Checking if UID exists in Firestore ===');\n            const specificUID = 'b7690183-ab6b-4719-944d-c0a080a59e8c';\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'users', specificUID);\n            try {\n                const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(userDocRef);\n                if (userDoc.exists()) {\n                    var _userData_joinedDate_toDate, _userData_joinedDate;\n                    const userData = userDoc.data();\n                    addToResult('✅ User document found!');\n                    addToResult(\"   Name: \".concat(userData.name || 'N/A'));\n                    addToResult(\"   Email: \".concat(userData.email || 'N/A'));\n                    addToResult(\"   Plan: \".concat(userData.plan || 'N/A'));\n                    addToResult(\"   Referral Code: \".concat(userData.referralCode || 'N/A'));\n                    addToResult(\"   Status: \".concat(userData.status || 'N/A'));\n                    addToResult(\"   Joined: \".concat(((_userData_joinedDate = userData.joinedDate) === null || _userData_joinedDate === void 0 ? void 0 : (_userData_joinedDate_toDate = _userData_joinedDate.toDate()) === null || _userData_joinedDate_toDate === void 0 ? void 0 : _userData_joinedDate_toDate.toLocaleString()) || 'N/A'));\n                    addToResult('\\n✅ This confirms Firestore is working!');\n                    addToResult('The registration issue might be specific to the registration flow.');\n                } else {\n                    addToResult('❌ User document not found');\n                    addToResult('This UID might be from Firebase Auth but Firestore document creation failed');\n                }\n            } catch (firestoreError) {\n                addToResult(\"❌ Firestore query failed: \".concat(firestoreError.message));\n                addToResult(\"   Error code: \".concat(firestoreError.code));\n                if (firestoreError.code === 'permission-denied') {\n                    addToResult('   This indicates Firestore security rules are blocking reads');\n                } else if (firestoreError.code === 'unavailable') {\n                    addToResult('   This indicates Firestore service is unavailable');\n                }\n            }\n        } catch (error) {\n            addToResult(\"❌ UID test failed: \".concat(error.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const testEmailPasswordAuth = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        let testUser = null;\n        try {\n            addToResult('📧 Testing Email/Password Authentication...\\n');\n            // Test email/password authentication (same as registration)\n            addToResult('=== Email/Password Authentication Test ===');\n            const testEmail = \"test\".concat(Date.now(), \"@example.com\");\n            const testPassword = 'test123456';\n            addToResult(\"Creating user with email: \".concat(testEmail));\n            try {\n                const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, testEmail, testPassword);\n                testUser = userCredential.user;\n                addToResult(\"✅ Email/Password auth successful: \".concat(testUser.uid));\n                addToResult(\"   Email: \".concat(testUser.email));\n                addToResult(\"   Email verified: \".concat(testUser.emailVerified));\n                // Test Firestore document creation\n                addToResult('\\n=== Testing Firestore Document Creation ===');\n                const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'users', testUser.uid);\n                const userData = {\n                    name: 'Test User',\n                    email: testEmail.toLowerCase(),\n                    mobile: '9876543210',\n                    referralCode: \"TEST\".concat(Date.now().toString().slice(-4)),\n                    plan: 'Trial',\n                    joinedDate: firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.Timestamp.now(),\n                    wallet: 0,\n                    status: 'active'\n                };\n                addToResult('Attempting Firestore document creation...');\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)(userDocRef, userData);\n                addToResult('✅ Firestore document created successfully');\n                // Verify document\n                const verifyDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(userDocRef);\n                if (verifyDoc.exists()) {\n                    addToResult('✅ Document verification successful');\n                    const docData = verifyDoc.data();\n                    addToResult(\"   Name: \".concat(docData.name));\n                    addToResult(\"   Email: \".concat(docData.email));\n                    addToResult(\"   Plan: \".concat(docData.plan));\n                    addToResult('\\n🎉 SUCCESS: Email/Password registration flow works!');\n                    addToResult('The registration issue might be in the form validation or error handling.');\n                } else {\n                    addToResult('❌ Document verification failed');\n                }\n            } catch (authError) {\n                addToResult(\"❌ Email/Password auth failed: \".concat(authError.message));\n                addToResult(\"   Error code: \".concat(authError.code));\n                if (authError.code === 'auth/operation-not-allowed') {\n                    addToResult('\\n🔧 EMAIL/PASSWORD AUTHENTICATION DISABLED:');\n                    addToResult('   1. Go to Firebase Console → Authentication → Sign-in method');\n                    addToResult('   2. Find \"Email/Password\" in the list');\n                    addToResult('   3. Click \"Enable\" and save');\n                    addToResult('   4. Make sure both \"Email/Password\" and \"Email link\" are enabled');\n                } else if (authError.code === 'auth/network-request-failed') {\n                    addToResult('\\n🔧 NETWORK ISSUE DETECTED:');\n                    addToResult('   The original network issue is still present');\n                    addToResult('   Try the network diagnostic solutions');\n                }\n            }\n        } catch (error) {\n            addToResult(\"❌ Email/Password test failed: \".concat(error.message));\n            addToResult(\"   Error code: \".concat(error.code));\n        } finally{\n            // Cleanup\n            if (testUser) {\n                try {\n                    addToResult('\\n=== Cleanup ===');\n                    await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.deleteUser)(testUser);\n                    addToResult('✅ Test user deleted');\n                } catch (deleteError) {\n                    addToResult(\"⚠️ User deletion failed: \".concat(deleteError.message));\n                }\n            }\n            try {\n                await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n                addToResult('✅ Signed out');\n            } catch (signOutError) {\n                addToResult(\"⚠️ Sign out failed: \".concat(signOutError.message));\n            }\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-white mb-6\",\n                    children: \"Firebase Connectivity Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testConnectivity,\n                                    disabled: isLoading,\n                                    className: \"btn-primary\",\n                                    children: isLoading ? 'Testing...' : 'Test Firebase Connectivity'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testNetworkDiagnostics,\n                                    disabled: isLoading,\n                                    className: \"btn-primary\",\n                                    children: isLoading ? 'Testing...' : 'Test Network Diagnostics'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testSpecificUID,\n                                    disabled: isLoading,\n                                    className: \"btn-primary\",\n                                    children: isLoading ? 'Testing...' : 'Test Specific UID'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testEmailPasswordAuth,\n                                    disabled: isLoading,\n                                    className: \"btn-primary\",\n                                    children: isLoading ? 'Testing...' : 'Test Email/Password Auth'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black/50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96\",\n                                children: result || 'Click a test button to start...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/register\",\n                        className: \"text-blue-400 hover:text-blue-300 underline\",\n                        children: \"← Back to Registration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connectivity\\\\page.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\n_s(TestFirebaseConnectivityPage, \"TA2EjS24NWK+5U65aZ0FJpR3Amc=\");\n_c = TestFirebaseConnectivityPage;\nvar _c;\n$RefreshReg$(_c, \"TestFirebaseConnectivityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-firebase-connectivity/page.tsx\n"));

/***/ })

});