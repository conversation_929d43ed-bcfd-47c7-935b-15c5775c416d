/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/register/page";
exports.ids = ["app/register/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(rsc)/./src/app/register/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'register',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\"],\n'error': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/register/page\",\n        pathname: \"/register\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(rsc)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(rsc)/./src/components/PWAInstaller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(rsc)/./src/app/register/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3JlZ2lzdGVyJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUErSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXHNyY1xcXFxhcHBcXFxccmVnaXN0ZXJcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fe166ce4cd0c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFTVVNcXE9uZURyaXZlXFxEZXNrdG9wXFxNWSBQUk9KRUNUU1xcTm9kZSBNeXR1YmVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZlMTY2Y2U0Y2QwY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PWAInstaller */ \"(rsc)/./src/components/PWAInstaller.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'MyTube - Watch Videos & Earn',\n    description: 'Watch videos and earn money. Complete daily video watching tasks to earn rewards.',\n    keywords: 'video watching, earn money, online earning, video tasks, rewards',\n    authors: [\n        {\n            name: 'MyTube Team'\n        }\n    ],\n    manifest: '/manifest.json',\n    icons: {\n        icon: '/img/mytube-favicon.svg',\n        apple: '/img/mytube-favicon.svg'\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1.0,\n    maximumScale: 1.0,\n    userScalable: false,\n    themeColor: '#FF0000'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"https://cdn.jsdelivr.net/npm/sweetalert2@11\",\n                        async: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animated-bg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-white/80\",\n                    children: \"Loading MyTube...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQVNVU1xcT25lRHJpdmVcXERlc2t0b3BcXE1ZIFBST0pFQ1RTXFxOb2RlIE15dHViZVxcc3JjXFxhcHBcXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGlubmVyIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MFwiPkxvYWRpbmcgTXlUdWJlLi4uPC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/img/mytube-logo.svg\",\n                            alt: \"MyTube Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-white mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-white mb-2\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"The page you're looking for doesn't exist or has been moved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need help finding what you're looking for?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-home mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/work\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-play-circle mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Watch Videos\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/register/page.tsx":
/*!***********************************!*\
  !*** ./src/app/register/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\register\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\PWAInstaller.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(ssr)/./src/components/PWAInstaller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(ssr)/./src/app/register/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3JlZ2lzdGVyJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUErSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXHNyY1xcXFxhcHBcXFxccmVnaXN0ZXJcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            console.error('Application error:', error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/img/mytube-logo.svg\",\n                            alt: \"MyTube Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-white mb-4\",\n                            children: \"Oops!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"We encountered an unexpected error. Please try again or contact support if the problem persists.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need immediate help?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-redo mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-home mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Go Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-8 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"text-white/60 cursor-pointer\",\n                            children: \"Error Details (Development)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-4 p-4 bg-red-900/20 rounded-lg text-red-300 text-sm overflow-auto\",\n                            children: [\n                                error.message,\n                                error.stack && '\\n\\n' + error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/register/page.tsx":
/*!***********************************!*\
  !*** ./src/app/register/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/dataService */ \"(ssr)/./src/lib/dataService.ts\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sweetalert2 */ \"(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction RegisterPage() {\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__.useAuthState)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        mobile: '',\n        password: '',\n        confirmPassword: '',\n        referralCode: ''\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            if (user && !loading) {\n                window.location.href = '/dashboard';\n            }\n        }\n    }[\"RegisterPage.useEffect\"], [\n        user,\n        loading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            // Get referral code from URL if present\n            const urlParams = new URLSearchParams(window.location.search);\n            const refCode = urlParams.get('ref');\n            if (refCode) {\n                setFormData({\n                    \"RegisterPage.useEffect\": (prev)=>({\n                            ...prev,\n                            referralCode: refCode\n                        })\n                }[\"RegisterPage.useEffect\"]);\n            }\n        }\n    }[\"RegisterPage.useEffect\"], []);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    // Referral code generation is handled by generateUniqueReferralCode function\n    const validateForm = ()=>{\n        const { name, email, mobile, password, confirmPassword } = formData;\n        if (!name || !email || !mobile || !password || !confirmPassword) {\n            throw new Error('Please fill in all required fields');\n        }\n        if (name.length < 2) {\n            throw new Error('Name must be at least 2 characters long');\n        }\n        if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n            throw new Error('Please enter a valid email address');\n        }\n        if (!/^[6-9]\\d{9}$/.test(mobile)) {\n            throw new Error('Please enter a valid 10-digit mobile number');\n        }\n        if (password.length < 6) {\n            throw new Error('Password must be at least 6 characters long');\n        }\n        if (password !== confirmPassword) {\n            throw new Error('Passwords do not match');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            validateForm();\n            setIsLoading(true);\n            // Create user account first - Firebase Auth will handle email uniqueness\n            // Mobile numbers are allowed to be duplicate (multiple users can have same mobile)\n            console.log('🚀 Starting registration process...');\n            console.log('📧 Email:', formData.email);\n            console.log('📱 Mobile:', formData.mobile);\n            console.log('👤 Name:', formData.name);\n            console.log('🔐 Creating user with email and password...');\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_4__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.auth, formData.email, formData.password);\n            const user = userCredential.user;\n            console.log('✅ Firebase Auth user created successfully!');\n            console.log('🆔 User UID:', user.uid);\n            console.log('📧 User Email:', user.email);\n            console.log('✅ Email Verified:', user.emailVerified);\n            // Wait for auth state to fully propagate\n            console.log('⏳ Waiting for auth state to propagate...');\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Verify auth state\n            console.log('🔍 Verifying auth state...');\n            console.log('Current auth user after wait:', _lib_firebase__WEBPACK_IMPORTED_MODULE_6__.auth.currentUser?.uid);\n            console.log('Auth user matches created user:', _lib_firebase__WEBPACK_IMPORTED_MODULE_6__.auth.currentUser?.uid === user.uid);\n            console.log('🎯 Generating sequential referral code...');\n            // Use sequential referral code generation based on registration count\n            const userReferralCode = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.generateUniqueReferralCode)();\n            console.log('✅ Generated sequential referral code:', userReferralCode);\n            // Note: Mobile numbers are allowed to be duplicate (business requirement)\n            // Firebase Auth already ensures email uniqueness\n            // Create user document in Firestore with all required fields\n            const userData = {\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.name]: formData.name.trim(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.email]: formData.email.toLowerCase(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.mobile]: formData.mobile,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referralCode]: userReferralCode,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referredBy]: formData.referralCode || '',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referralBonusCredited]: false,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.plan]: 'Trial',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.planExpiry]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.activeDays]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.joinedDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.Timestamp.now(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.wallet]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.totalVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.todayVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.lastVideoDate]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.videoDuration]: 30,\n                status: 'active'\n            };\n            console.log('Creating user document with data:', userData);\n            console.log('User UID:', user.uid);\n            console.log('Collection:', _lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users);\n            console.log('Document path:', `${_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users}/${user.uid}`);\n            // Create user document in Firestore\n            console.log('Creating user document in Firestore...');\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, _lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users, user.uid);\n            console.log('Document reference created:', userDocRef.path);\n            console.log('About to create document with data:', JSON.stringify(userData, null, 2));\n            try {\n                console.log('Attempting to create document...');\n                console.log('User UID:', user.uid);\n                console.log('Document path:', userDocRef.path);\n                console.log('Auth user email:', user.email);\n                console.log('Auth user verified:', user.emailVerified);\n                console.log('Current auth user UID:', _lib_firebase__WEBPACK_IMPORTED_MODULE_6__.auth.currentUser?.uid);\n                console.log('Auth state matches:', _lib_firebase__WEBPACK_IMPORTED_MODULE_6__.auth.currentUser?.uid === user.uid);\n                console.log('User data to be saved:', JSON.stringify(userData, null, 2));\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.setDoc)(userDocRef, userData);\n                console.log('✅ User document created successfully');\n                // Verify the document was created\n                const verifyDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.getDoc)(userDocRef);\n                if (verifyDoc.exists()) {\n                    console.log('✅ Document verification successful:', verifyDoc.data());\n                } else {\n                    console.error('❌ Document was not created properly');\n                    throw new Error('User document was not created properly');\n                }\n            } catch (firestoreError) {\n                console.error('❌ Firestore setDoc failed:', firestoreError);\n                console.error('❌ Firestore error code:', firestoreError.code);\n                console.error('❌ Firestore error message:', firestoreError.message);\n                console.error('❌ Full error object:', JSON.stringify(firestoreError, null, 2));\n                throw new Error(`Failed to create user profile: ${firestoreError.message}`);\n            }\n            // Note: Referral bonus will be credited when admin upgrades user from Trial to paid plan\n            console.log('User registered successfully. Referral bonus will be processed when upgraded to paid plan.');\n            // Show success message\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9__[\"default\"].fire({\n                icon: 'success',\n                title: 'Registration Successful!',\n                text: 'Your account has been created successfully. Welcome to MyTube!',\n                timer: 3000,\n                showConfirmButton: false\n            });\n        // Redirect will be handled by useEffect\n        } catch (error) {\n            console.error('Registration error:', error);\n            console.error('Error code:', error.code);\n            console.error('Error message:', error.message);\n            console.error('Full error object:', JSON.stringify(error, null, 2));\n            let message = 'An error occurred during registration';\n            if (error.message.includes('fill in all')) {\n                message = error.message;\n            } else if (error.message.includes('Name must be')) {\n                message = error.message;\n            } else if (error.message.includes('valid email')) {\n                message = error.message;\n            } else if (error.message.includes('valid 10-digit')) {\n                message = error.message;\n            } else if (error.message.includes('Password must be')) {\n                message = error.message;\n            } else if (error.message.includes('Passwords do not match')) {\n                message = error.message;\n            } else if (error.message.includes('email address is already registered')) {\n                message = error.message;\n            // Mobile number duplicates are allowed, so no error handling needed for mobile duplicates\n            } else {\n                switch(error.code){\n                    case 'auth/email-already-in-use':\n                        message = 'An account with this email already exists';\n                        break;\n                    case 'auth/invalid-email':\n                        message = 'Invalid email address';\n                        break;\n                    case 'auth/weak-password':\n                        message = 'Password is too weak';\n                        break;\n                    case 'permission-denied':\n                        message = `Permission denied: ${error.message}. Please check Firestore security rules.`;\n                        break;\n                    default:\n                        message = `Registration failed: ${error.message || 'Unknown error'} (Code: ${error.code || 'none'})`;\n                }\n            }\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9__[\"default\"].fire({\n                icon: 'error',\n                title: 'Registration Failed',\n                text: message\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass-card w-full max-w-md p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/img/mytube-logo.svg\",\n                                    alt: \"MyTube Logo\",\n                                    width: 50,\n                                    height: 50,\n                                    className: \"mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"MyTube\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-white mb-2\",\n                            children: \"Create Account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80\",\n                            children: \"Join MyTube and start earning today\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"name\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Full Name *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"name\",\n                                    name: \"name\",\n                                    value: formData.name,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter your full name\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"email\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Email Address *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    id: \"email\",\n                                    name: \"email\",\n                                    value: formData.email,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter your email\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"mobile\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Mobile Number *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"tel\",\n                                    id: \"mobile\",\n                                    name: \"mobile\",\n                                    value: formData.mobile,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter 10-digit mobile number\",\n                                    maxLength: 10,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"password\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Password *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            id: \"password\",\n                                            name: \"password\",\n                                            value: formData.password,\n                                            onChange: handleInputChange,\n                                            className: \"form-input pr-12\",\n                                            placeholder: \"Enter password (min 6 characters)\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"password-toggle-btn\",\n                                            \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: `fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmPassword\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Confirm Password *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            value: formData.confirmPassword,\n                                            onChange: handleInputChange,\n                                            className: \"form-input pr-12\",\n                                            placeholder: \"Confirm your password\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            className: \"password-toggle-btn\",\n                                            \"aria-label\": showConfirmPassword ? \"Hide confirm password\" : \"Show confirm password\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: `fas ${showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"referralCode\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Referral Code (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"referralCode\",\n                                    name: \"referralCode\",\n                                    value: formData.referralCode,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter referral code if you have one\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"w-full btn-primary flex items-center justify-center mt-6\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner mr-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Creating Account...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-user-plus mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Create Account\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/60\",\n                        children: [\n                            \"Already have an account?\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"text-white font-semibold hover:underline\",\n                                children: \"Sign in here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-white/80 hover:text-white transition-colors inline-flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-arrow-left mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Home\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 426,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 258,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/register/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-8 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"fas fa-exclamation-triangle text-red-400 text-4xl mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-4\",\n                            children: \"An error occurred while loading this page. Please refresh and try again.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-refresh mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                \"Refresh Page\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PWAInstaller)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction PWAInstaller() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInstallButton, setShowInstallButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PWAInstaller.useEffect\": ()=>{\n            // Register service worker\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"PWAInstaller.useEffect\": (registration)=>{\n                        console.log('SW registered: ', registration);\n                    }\n                }[\"PWAInstaller.useEffect\"]).catch({\n                    \"PWAInstaller.useEffect\": (registrationError)=>{\n                        console.log('SW registration failed: ', registrationError);\n                    }\n                }[\"PWAInstaller.useEffect\"]);\n            }\n            // Listen for beforeinstallprompt event\n            const handleBeforeInstallPrompt = {\n                \"PWAInstaller.useEffect.handleBeforeInstallPrompt\": (e)=>{\n                    e.preventDefault();\n                    setDeferredPrompt(e);\n                    setShowInstallButton(true);\n                }\n            }[\"PWAInstaller.useEffect.handleBeforeInstallPrompt\"];\n            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n            // Check if app is already installed\n            if (window.matchMedia('(display-mode: standalone)').matches) {\n                setShowInstallButton(false);\n            }\n            return ({\n                \"PWAInstaller.useEffect\": ()=>{\n                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n                }\n            })[\"PWAInstaller.useEffect\"];\n        }\n    }[\"PWAInstaller.useEffect\"], []);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        deferredPrompt.prompt();\n        const { outcome } = await deferredPrompt.userChoice;\n        if (outcome === 'accepted') {\n            console.log('User accepted the install prompt');\n        } else {\n            console.log('User dismissed the install prompt');\n        }\n        setDeferredPrompt(null);\n        setShowInstallButton(false);\n    };\n    if (!showInstallButton) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleInstallClick,\n            className: \"glass-button px-4 py-3 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-download mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                \"Install App\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PWAInstaller.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthState: () => (/* binding */ useAuthState),\n/* harmony export */   useRequireAdmin: () => (/* binding */ useRequireAdmin),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/authUtils */ \"(ssr)/./src/lib/authUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuthState,useRequireAuth,useRequireAdmin auto */ \n\n\n\nfunction useAuthState() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuthState.useEffect\": ()=>{\n            try {\n                const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, {\n                    \"useAuthState.useEffect.unsubscribe\": (user)=>{\n                        console.log('Auth state changed:', user ? 'User logged in' : 'No user');\n                        setUser(user);\n                        setLoading(false);\n                    }\n                }[\"useAuthState.useEffect.unsubscribe\"]);\n                return ({\n                    \"useAuthState.useEffect\": ()=>unsubscribe()\n                })[\"useAuthState.useEffect\"];\n            } catch (error) {\n                console.error('Error in auth state listener:', error);\n                setLoading(false);\n            }\n        }\n    }[\"useAuthState.useEffect\"], []);\n    const signOut = async ()=>{\n        try {\n            await (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_3__.quickLogout)(user?.uid, '/');\n        } catch (error) {\n            console.error('Error signing out:', error);\n            // Force redirect on error\n            window.location.href = '/';\n        }\n    };\n    return {\n        user,\n        loading,\n        signOut\n    };\n}\nfunction useRequireAuth() {\n    const { user, loading } = useAuthState();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRequireAuth.useEffect\": ()=>{\n            if (!loading && !user) {\n                window.location.href = '/login';\n            }\n        }\n    }[\"useRequireAuth.useEffect\"], [\n        user,\n        loading\n    ]);\n    return {\n        user,\n        loading\n    };\n}\nfunction useRequireAdmin() {\n    const { user, loading } = useAuthState();\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [adminLoading, setAdminLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRequireAdmin.useEffect\": ()=>{\n            if (!loading && !user) {\n                window.location.href = '/admin/login';\n                return;\n            }\n            if (user) {\n                // Check if user is admin\n                // This would typically involve checking a custom claim or database\n                // For now, we'll use a simple email check\n                const adminEmails = [\n                    '<EMAIL>',\n                    '<EMAIL>'\n                ];\n                const userIsAdmin = adminEmails.includes(user.email || '');\n                setIsAdmin(userIsAdmin);\n                setAdminLoading(false);\n                if (!userIsAdmin) {\n                    window.location.href = '/login';\n                }\n            }\n        }\n    }[\"useRequireAdmin.useEffect\"], [\n        user,\n        loading\n    ]);\n    return {\n        user,\n        loading: loading || adminLoading,\n        isAdmin\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/authUtils.ts":
/*!******************************!*\
  !*** ./src/lib/authUtils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearExpiredSessions: () => (/* binding */ clearExpiredSessions),\n/* harmony export */   clearUserLocalStorage: () => (/* binding */ clearUserLocalStorage),\n/* harmony export */   getUserSessionInfo: () => (/* binding */ getUserSessionInfo),\n/* harmony export */   handleUserLogout: () => (/* binding */ handleUserLogout),\n/* harmony export */   quickLogout: () => (/* binding */ quickLogout)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sweetalert2 */ \"(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js\");\n\n\n/**\n * Clear all user-specific data from localStorage\n */ function clearUserLocalStorage(userId) {\n    try {\n        // Get all localStorage keys\n        const keys = Object.keys(localStorage);\n        // Remove user-specific data\n        keys.forEach((key)=>{\n            if (key.includes(userId) || key.startsWith('video_session_') || key.startsWith('watch_times_') || key.startsWith('video_refresh_') || key.startsWith('video_change_notification_') || key.startsWith('leave_') || key.includes('mytube_') || key.includes('user_')) {\n                localStorage.removeItem(key);\n            }\n        });\n        // Also clear common app data\n        const commonKeys = [\n            'currentUser',\n            'authToken',\n            'userSession',\n            'appState',\n            'videoProgress',\n            'sessionData',\n            'workSession',\n            'walletCache',\n            'transactionCache'\n        ];\n        commonKeys.forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n        console.log('Local storage cleared for user:', userId);\n    } catch (error) {\n        console.error('Error clearing local storage:', error);\n    }\n}\n/**\n * Handle user logout with confirmation and cleanup\n */ async function handleUserLogout(userId, redirectPath = '/login') {\n    try {\n        const result = await sweetalert2__WEBPACK_IMPORTED_MODULE_1__[\"default\"].fire({\n            title: 'Logout Confirmation',\n            text: 'Are you sure you want to logout?',\n            icon: 'question',\n            showCancelButton: true,\n            confirmButtonColor: '#ef4444',\n            cancelButtonColor: '#6b7280',\n            confirmButtonText: 'Yes, Logout',\n            cancelButtonText: 'Cancel'\n        });\n        if (result.isConfirmed) {\n            // Clear user-specific local storage data\n            if (userId) {\n                clearUserLocalStorage(userId);\n            }\n            // Sign out from Firebase\n            await _firebase__WEBPACK_IMPORTED_MODULE_0__.auth.signOut();\n            // Show success message\n            sweetalert2__WEBPACK_IMPORTED_MODULE_1__[\"default\"].fire({\n                icon: 'success',\n                title: 'Logged Out Successfully',\n                text: 'You have been logged out. Redirecting...',\n                timer: 2000,\n                showConfirmButton: false\n            }).then(()=>{\n                // Redirect to specified path\n                window.location.href = redirectPath;\n            });\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error('Logout error:', error);\n        sweetalert2__WEBPACK_IMPORTED_MODULE_1__[\"default\"].fire({\n            icon: 'error',\n            title: 'Logout Failed',\n            text: 'There was an error logging out. Please try again.'\n        });\n        return false;\n    }\n}\n/**\n * Quick logout without confirmation (for emergency cases)\n */ async function quickLogout(userId, redirectPath = '/login') {\n    try {\n        // Clear user-specific local storage data\n        if (userId) {\n            clearUserLocalStorage(userId);\n        }\n        // Sign out from Firebase\n        await _firebase__WEBPACK_IMPORTED_MODULE_0__.auth.signOut();\n        // Redirect immediately\n        window.location.href = redirectPath;\n    } catch (error) {\n        console.error('Quick logout error:', error);\n        // Force redirect even if logout fails\n        window.location.href = redirectPath;\n    }\n}\n/**\n * Clear session data on app start (useful for cleanup)\n */ function clearExpiredSessions() {\n    try {\n        const keys = Object.keys(localStorage);\n        const today = new Date().toDateString();\n        keys.forEach((key)=>{\n            // Clear old session data (not from today)\n            if (key.startsWith('video_session_') || key.startsWith('watch_times_')) {\n                const storedData = localStorage.getItem(key);\n                if (storedData) {\n                    try {\n                        // Check if it's from today\n                        if (!key.includes(today)) {\n                            localStorage.removeItem(key);\n                            console.log('Cleared expired session:', key);\n                        }\n                    } catch (error) {\n                        // If we can't parse it, remove it\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Error clearing expired sessions:', error);\n    }\n}\n/**\n * Get user session info from localStorage\n */ function getUserSessionInfo(userId) {\n    try {\n        const today = new Date().toDateString();\n        const sessionKey = `video_session_${userId}_${today}`;\n        const watchTimesKey = `watch_times_${userId}_${today}`;\n        const sessionCount = localStorage.getItem(sessionKey);\n        const watchTimes = localStorage.getItem(watchTimesKey);\n        return {\n            videoCount: sessionCount ? parseInt(sessionCount) : 0,\n            watchTimes: watchTimes ? JSON.parse(watchTimes) : [],\n            hasActiveSession: !!(sessionCount || watchTimes)\n        };\n    } catch (error) {\n        console.error('Error getting session info:', error);\n        return {\n            videoCount: 0,\n            watchTimes: [],\n            hasActiveSession: false\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/authUtils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/dataService.ts":
/*!********************************!*\
  !*** ./src/lib/dataService.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   FIELD_NAMES: () => (/* binding */ FIELD_NAMES),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   addTransaction: () => (/* binding */ addTransaction),\n/* harmony export */   checkQuickVideoAdvantageActive: () => (/* binding */ checkQuickVideoAdvantageActive),\n/* harmony export */   checkReferralCodeExists: () => (/* binding */ checkReferralCodeExists),\n/* harmony export */   checkWithdrawalAllowed: () => (/* binding */ checkWithdrawalAllowed),\n/* harmony export */   createWithdrawalRequest: () => (/* binding */ createWithdrawalRequest),\n/* harmony export */   deleteNotification: () => (/* binding */ deleteNotification),\n/* harmony export */   generateSequentialReferralCode: () => (/* binding */ generateSequentialReferralCode),\n/* harmony export */   generateSimpleReferralCode: () => (/* binding */ generateSimpleReferralCode),\n/* harmony export */   generateUniqueReferralCode: () => (/* binding */ generateUniqueReferralCode),\n/* harmony export */   getAllNotifications: () => (/* binding */ getAllNotifications),\n/* harmony export */   getBankDetails: () => (/* binding */ getBankDetails),\n/* harmony export */   getCurrentReferralCounter: () => (/* binding */ getCurrentReferralCounter),\n/* harmony export */   getPlanEarning: () => (/* binding */ getPlanEarning),\n/* harmony export */   getPlanValidityDays: () => (/* binding */ getPlanValidityDays),\n/* harmony export */   getPlanVideoDuration: () => (/* binding */ getPlanVideoDuration),\n/* harmony export */   getReferralBonus: () => (/* binding */ getReferralBonus),\n/* harmony export */   getReferrals: () => (/* binding */ getReferrals),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUnreadNotificationCount: () => (/* binding */ getUnreadNotificationCount),\n/* harmony export */   getUnreadNotifications: () => (/* binding */ getUnreadNotifications),\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   getUserNotifications: () => (/* binding */ getUserNotifications),\n/* harmony export */   getUserVideoSettings: () => (/* binding */ getUserVideoSettings),\n/* harmony export */   getUserWithdrawals: () => (/* binding */ getUserWithdrawals),\n/* harmony export */   getVideoCountData: () => (/* binding */ getVideoCountData),\n/* harmony export */   getWalletData: () => (/* binding */ getWalletData),\n/* harmony export */   grantQuickVideoAdvantage: () => (/* binding */ grantQuickVideoAdvantage),\n/* harmony export */   hasPendingWithdrawals: () => (/* binding */ hasPendingWithdrawals),\n/* harmony export */   hasUnreadNotifications: () => (/* binding */ hasUnreadNotifications),\n/* harmony export */   isNotificationRead: () => (/* binding */ isNotificationRead),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead),\n/* harmony export */   processReferralBonus: () => (/* binding */ processReferralBonus),\n/* harmony export */   removeQuickVideoAdvantage: () => (/* binding */ removeQuickVideoAdvantage),\n/* harmony export */   saveBankDetails: () => (/* binding */ saveBankDetails),\n/* harmony export */   updateUserData: () => (/* binding */ updateUserData),\n/* harmony export */   updateUserPlanExpiry: () => (/* binding */ updateUserPlanExpiry),\n/* harmony export */   updateUserVideoDuration: () => (/* binding */ updateUserVideoDuration),\n/* harmony export */   updateVideoCount: () => (/* binding */ updateVideoCount),\n/* harmony export */   updateWalletBalance: () => (/* binding */ updateWalletBalance)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\n// Field names for Firestore collections\nconst FIELD_NAMES = {\n    // User fields\n    name: 'name',\n    email: 'email',\n    mobile: 'mobile',\n    referralCode: 'referralCode',\n    referredBy: 'referredBy',\n    referralBonusCredited: 'referralBonusCredited',\n    plan: 'plan',\n    planExpiry: 'planExpiry',\n    activeDays: 'activeDays',\n    joinedDate: 'joinedDate',\n    // Wallet fields\n    wallet: 'wallet',\n    // Bank details fields\n    bankAccountHolderName: 'bankAccountHolderName',\n    bankAccountNumber: 'bankAccountNumber',\n    bankIfscCode: 'bankIfscCode',\n    bankName: 'bankName',\n    bankDetailsUpdated: 'bankDetailsUpdated',\n    // Video fields\n    totalVideos: 'totalVideos',\n    todayVideos: 'todayVideos',\n    lastVideoDate: 'lastVideoDate',\n    videoDuration: 'videoDuration',\n    // Quick Video Advantage fields\n    quickVideoAdvantage: 'quickVideoAdvantage',\n    quickVideoAdvantageExpiry: 'quickVideoAdvantageExpiry',\n    quickVideoAdvantageDays: 'quickVideoAdvantageDays',\n    quickVideoAdvantageSeconds: 'quickVideoAdvantageSeconds',\n    quickVideoAdvantageGrantedBy: 'quickVideoAdvantageGrantedBy',\n    quickVideoAdvantageGrantedAt: 'quickVideoAdvantageGrantedAt',\n    // Transaction fields\n    type: 'type',\n    amount: 'amount',\n    date: 'date',\n    status: 'status',\n    description: 'description',\n    userId: 'userId'\n};\n// Collection names\nconst COLLECTIONS = {\n    users: 'users',\n    transactions: 'transactions',\n    withdrawals: 'withdrawals',\n    plans: 'plans',\n    settings: 'settings',\n    notifications: 'notifications',\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Get user data\nasync function getUserData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserData:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Ensure all values are properly typed\n            const result = {\n                name: String(data[FIELD_NAMES.name] || ''),\n                email: String(data[FIELD_NAMES.email] || ''),\n                mobile: String(data[FIELD_NAMES.mobile] || ''),\n                referralCode: String(data[FIELD_NAMES.referralCode] || ''),\n                referredBy: String(data[FIELD_NAMES.referredBy] || ''),\n                plan: String(data[FIELD_NAMES.plan] || 'Trial'),\n                planExpiry: data[FIELD_NAMES.planExpiry]?.toDate() || null,\n                activeDays: Number(data[FIELD_NAMES.activeDays] || 0),\n                joinedDate: data[FIELD_NAMES.joinedDate]?.toDate() || new Date(),\n                videoDuration: Number(data[FIELD_NAMES.videoDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)),\n                // Quick Video Advantage fields\n                quickVideoAdvantage: Boolean(data[FIELD_NAMES.quickVideoAdvantage] || false),\n                quickVideoAdvantageExpiry: data[FIELD_NAMES.quickVideoAdvantageExpiry]?.toDate() || null,\n                quickVideoAdvantageDays: Number(data[FIELD_NAMES.quickVideoAdvantageDays] || 0),\n                quickVideoAdvantageSeconds: Number(data[FIELD_NAMES.quickVideoAdvantageSeconds] || 30),\n                quickVideoAdvantageGrantedBy: String(data[FIELD_NAMES.quickVideoAdvantageGrantedBy] || ''),\n                quickVideoAdvantageGrantedAt: data[FIELD_NAMES.quickVideoAdvantageGrantedAt]?.toDate() || null\n            };\n            console.log('getUserData result:', result);\n            return result;\n        }\n        return null;\n    } catch (error) {\n        console.error('Error getting user data:', error);\n        return null // Return null instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get wallet data\nasync function getWalletData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getWalletData:', userId);\n            return {\n                wallet: 0\n            };\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const result = {\n                wallet: Number(data[FIELD_NAMES.wallet] || 0)\n            };\n            console.log('getWalletData result:', result);\n            return result;\n        }\n        return {\n            wallet: 0\n        };\n    } catch (error) {\n        console.error('Error getting wallet data:', error);\n        return {\n            wallet: 0\n        } // Return default instead of throwing\n        ;\n    }\n}\n// Get video count data\nasync function getVideoCountData(userId) {\n    try {\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const totalVideos = data[FIELD_NAMES.totalVideos] || 0;\n            const todayVideos = data[FIELD_NAMES.todayVideos] || 0;\n            const lastVideoDate = data[FIELD_NAMES.lastVideoDate]?.toDate();\n            // Check if it's a new day\n            const today = new Date();\n            const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n            return {\n                totalVideos,\n                todayVideos: isNewDay ? 0 : todayVideos,\n                remainingVideos: Math.max(0, 50 - (isNewDay ? 0 : todayVideos))\n            };\n        }\n        return {\n            totalVideos: 0,\n            todayVideos: 0,\n            remainingVideos: 50\n        };\n    } catch (error) {\n        console.error('Error getting video count data:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUserData(userId, data) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), data);\n    } catch (error) {\n        console.error('Error updating user data:', error);\n        throw error;\n    }\n}\n// Add transaction\nasync function addTransaction(userId, transactionData) {\n    try {\n        const transaction = {\n            [FIELD_NAMES.userId]: userId,\n            [FIELD_NAMES.type]: transactionData.type,\n            [FIELD_NAMES.amount]: transactionData.amount,\n            [FIELD_NAMES.description]: transactionData.description,\n            [FIELD_NAMES.status]: transactionData.status || 'completed',\n            [FIELD_NAMES.date]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), transaction);\n    } catch (error) {\n        console.error('Error adding transaction:', error);\n        throw error;\n    }\n}\n// Get transactions\nasync function getTransactions(userId, limitCount = 10) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getTransactions:', userId);\n            return [];\n        }\n        // Temporary fix: Use only where clause without orderBy to avoid index requirement\n        // TODO: Create composite index in Firebase console for better performance\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.userId, '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data()[FIELD_NAMES.date]?.toDate()\n            }));\n        // Sort in memory since we can't use orderBy without index\n        transactions.sort((a, b)=>{\n            const dateA = a.date || new Date(0);\n            const dateB = b.date || new Date(0);\n            return dateB.getTime() - dateA.getTime() // Descending order\n            ;\n        });\n        return transactions;\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        return [] // Return empty array instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get referrals\nasync function getReferrals(referralCode) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referredBy, '==', referralCode));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting referrals:', error);\n        throw error;\n    }\n}\n// Update video count\nasync function updateVideoCount(userId) {\n    try {\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.todayVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n        });\n    } catch (error) {\n        console.error('Error updating video count:', error);\n        throw error;\n    }\n}\n// Update wallet balance\nasync function updateWalletBalance(userId, amount) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.wallet]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(amount)\n        });\n    } catch (error) {\n        console.error('Error updating wallet balance:', error);\n        throw error;\n    }\n}\n// Save bank details\nasync function saveBankDetails(userId, bankDetails) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            throw new Error('Invalid userId provided');\n        }\n        // Validate bank details\n        validateBankDetails(bankDetails);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),\n            [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),\n            [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),\n            [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),\n            [FIELD_NAMES.bankDetailsUpdated]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        });\n        console.log('Bank details saved successfully for user:', userId);\n    } catch (error) {\n        console.error('Error saving bank details:', error);\n        throw error;\n    }\n}\n// Get bank details\nasync function getBankDetails(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getBankDetails:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Check if bank details exist\n            if (data[FIELD_NAMES.bankAccountNumber]) {\n                const result = {\n                    accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),\n                    accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),\n                    ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),\n                    bankName: String(data[FIELD_NAMES.bankName] || '')\n                };\n                console.log('getBankDetails result found');\n                return result;\n            }\n        }\n        console.log('No bank details found for user');\n        return null;\n    } catch (error) {\n        console.error('Error getting bank details:', error);\n        return null;\n    }\n}\n// Get plan-based earning amount (per batch of 50 videos)\nfunction getPlanEarning(plan) {\n    const planEarnings = {\n        'Trial': 10,\n        'Starter': 25,\n        'Basic': 75,\n        'Premium': 150,\n        'Gold': 200,\n        'Platinum': 250,\n        'Diamond': 400\n    };\n    return planEarnings[plan] || 10 // Default to trial earning (per batch of 50 videos)\n    ;\n}\n// Get plan-based video duration (in seconds)\nfunction getPlanVideoDuration(plan) {\n    const planDurations = {\n        'Trial': 30,\n        'Starter': 300,\n        'Basic': 300,\n        'Premium': 300,\n        'Gold': 180,\n        'Platinum': 120,\n        'Diamond': 60 // 1 minute (Rs 9999 plan)\n    };\n    return planDurations[plan] || 30 // Default to trial duration (30 seconds)\n    ;\n}\n// Get plan validity duration in days\nfunction getPlanValidityDays(plan) {\n    const planValidityDays = {\n        'Trial': 2,\n        'Starter': 30,\n        'Basic': 30,\n        'Premium': 30,\n        'Gold': 30,\n        'Platinum': 30,\n        'Diamond': 30,\n        '499': 30,\n        '1499': 30,\n        '2999': 30,\n        '3999': 30,\n        '5999': 30,\n        '9999': 30 // Legacy plan mapping\n    };\n    return planValidityDays[plan] || 2 // Default to trial duration (2 days)\n    ;\n}\n// Check if user's plan is expired based on active days and plan validity\nasync function isUserPlanExpired(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                expired: true,\n                reason: 'User data not found'\n            };\n        }\n        // If user is on Trial plan, check based on joined date\n        if (userData.plan === 'Trial') {\n            const joinedDate = userData.joinedDate || new Date();\n            const today = new Date();\n            const daysSinceJoined = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n            const trialDaysLeft = Math.max(0, 2 - daysSinceJoined);\n            return {\n                expired: trialDaysLeft <= 0,\n                reason: trialDaysLeft <= 0 ? 'Trial period expired' : undefined,\n                daysLeft: trialDaysLeft,\n                activeDays: daysSinceJoined\n            };\n        }\n        // For paid plans, check if planExpiry is set\n        if (userData.planExpiry) {\n            const today = new Date();\n            const expired = today > userData.planExpiry;\n            const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                expired,\n                reason: expired ? 'Plan subscription expired' : undefined,\n                daysLeft,\n                activeDays: userData.activeDays || 0\n            };\n        }\n        // If planExpiry is not set, calculate based on active days and plan validity\n        const planValidityDays = getPlanValidityDays(userData.plan);\n        const currentActiveDays = userData.activeDays || 0;\n        const daysLeft = Math.max(0, planValidityDays - currentActiveDays);\n        const expired = daysLeft <= 0;\n        return {\n            expired,\n            reason: expired ? `Plan validity period (${planValidityDays} days) exceeded based on active days` : undefined,\n            daysLeft,\n            activeDays: currentActiveDays\n        };\n    } catch (error) {\n        console.error('Error checking plan expiry:', error);\n        return {\n            expired: true,\n            reason: 'Error checking plan status'\n        };\n    }\n}\n// Update user's plan expiry when admin changes plan\nasync function updateUserPlanExpiry(userId, newPlan, customExpiryDate) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        if (newPlan === 'Trial') {\n            // Trial plan doesn't have expiry, it's based on joined date\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: null\n            });\n        } else {\n            // Set expiry date for paid plans\n            let expiryDate;\n            if (customExpiryDate) {\n                expiryDate = customExpiryDate;\n            } else {\n                // Calculate expiry based on plan validity\n                const validityDays = getPlanValidityDays(newPlan);\n                const today = new Date();\n                expiryDate = new Date(today.getTime() + validityDays * 24 * 60 * 60 * 1000);\n            }\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiryDate)\n            });\n            console.log(`Updated plan expiry for user ${userId} to ${expiryDate.toDateString()}`);\n        }\n    } catch (error) {\n        console.error('Error updating plan expiry:', error);\n        throw error;\n    }\n}\n// Get referral bonus based on plan\nfunction getReferralBonus(plan) {\n    const referralBonuses = {\n        'Trial': 0,\n        '499': 50,\n        '1499': 150,\n        '2999': 300,\n        '3999': 400,\n        '5999': 700,\n        '9999': 1200,\n        'Starter': 50,\n        'Basic': 150,\n        'Premium': 300,\n        'Gold': 400,\n        'Platinum': 700,\n        'Diamond': 1200\n    };\n    return referralBonuses[plan] || 0;\n}\n// Process referral bonus when admin upgrades user from Trial to paid plan\nasync function processReferralBonus(userId, oldPlan, newPlan) {\n    try {\n        // Only process bonus when upgrading FROM Trial TO a paid plan\n        if (oldPlan !== 'Trial' || newPlan === 'Trial') {\n            console.log('Referral bonus only applies when upgrading from Trial to paid plan');\n            return;\n        }\n        console.log(`Processing referral bonus for user ${userId} upgrading from ${oldPlan} to ${newPlan}`);\n        // Get the user's data to find their referral info\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (!userDoc.exists()) {\n            console.log('User not found');\n            return;\n        }\n        const userData = userDoc.data();\n        const referredBy = userData[FIELD_NAMES.referredBy];\n        const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited];\n        if (!referredBy) {\n            console.log('User was not referred by anyone, skipping bonus processing');\n            return;\n        }\n        if (alreadyCredited) {\n            console.log('Referral bonus already credited for this user, skipping');\n            return;\n        }\n        console.log('Finding referrer with code:', referredBy);\n        // Find the referrer by referral code\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', referredBy), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        if (querySnapshot.empty) {\n            console.log('Referral code not found:', referredBy);\n            return;\n        }\n        const referrerDoc = querySnapshot.docs[0];\n        const referrerId = referrerDoc.id;\n        const bonusAmount = getReferralBonus(newPlan);\n        console.log(`Found referrer: ${referrerId}, bonus amount: ₹${bonusAmount}`);\n        if (bonusAmount > 0) {\n            // Add bonus to referrer's wallet\n            await updateWalletBalance(referrerId, bonusAmount);\n            // Add 50 videos to referrer's total video count\n            const referrerRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, referrerId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(referrerRef, {\n                [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(50)\n            });\n            // Mark referral bonus as credited for this user\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.referralBonusCredited]: true\n            });\n            // Add transaction record for referral bonus\n            await addTransaction(referrerId, {\n                type: 'referral_bonus',\n                amount: bonusAmount,\n                description: `Referral bonus for ${newPlan} plan upgrade + 50 bonus videos (User: ${userData[FIELD_NAMES.name]})`\n            });\n            console.log(`✅ Referral bonus processed: ₹${bonusAmount} + 50 videos for referrer ${referrerId}`);\n        } else {\n            console.log('No bonus amount calculated, skipping');\n        }\n    } catch (error) {\n        console.error('❌ Error processing referral bonus:', error);\n    // Don't throw error to avoid breaking plan update\n    }\n}\n// Get user video settings (duration and earning per batch)\nasync function getUserVideoSettings(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                videoDuration: 30,\n                earningPerBatch: 10,\n                plan: 'Trial',\n                hasQuickAdvantage: false\n            };\n        }\n        // Check if user has active quick video advantage\n        const hasActiveQuickAdvantage = checkQuickVideoAdvantageActive(userData);\n        let videoDuration = userData.videoDuration;\n        // If user has active quick video advantage, use custom seconds or default to 30\n        if (hasActiveQuickAdvantage) {\n            videoDuration = userData.quickVideoAdvantageSeconds || 30 // Use custom duration or default to 30 seconds\n            ;\n        } else {\n            // Use plan-based video duration, but allow admin overrides for non-trial users\n            if (!videoDuration || userData.plan === 'Trial') {\n                videoDuration = getPlanVideoDuration(userData.plan);\n            }\n        }\n        return {\n            videoDuration: videoDuration,\n            earningPerBatch: getPlanEarning(userData.plan),\n            plan: userData.plan,\n            hasQuickAdvantage: hasActiveQuickAdvantage,\n            quickAdvantageExpiry: userData.quickVideoAdvantageExpiry\n        };\n    } catch (error) {\n        console.error('Error getting user video settings:', error);\n        return {\n            videoDuration: 30,\n            earningPerBatch: 10,\n            plan: 'Trial',\n            hasQuickAdvantage: false\n        };\n    }\n}\n// Check if user has active quick video advantage\nfunction checkQuickVideoAdvantageActive(userData) {\n    if (!userData.quickVideoAdvantage || !userData.quickVideoAdvantageExpiry) {\n        return false;\n    }\n    const now = new Date();\n    const expiry = userData.quickVideoAdvantageExpiry;\n    return now < expiry;\n}\n// Grant quick video advantage to user (admin function)\nasync function grantQuickVideoAdvantage(userId, days, grantedBy, seconds = 30) {\n    try {\n        if (days <= 0 || days > 365) {\n            throw new Error('Days must be between 1 and 365');\n        }\n        if (seconds < 1 || seconds > 420) {\n            throw new Error('Seconds must be between 1 and 420 (7 minutes)');\n        }\n        const now = new Date();\n        const expiry = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: true,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiry),\n            [FIELD_NAMES.quickVideoAdvantageDays]: days,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: seconds,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: grantedBy,\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(now)\n        });\n        console.log(`Granted quick video advantage to user ${userId} for ${days} days until ${expiry.toDateString()}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_granted',\n            amount: 0,\n            description: `Quick video advantage granted for ${days} days by ${grantedBy}`\n        });\n        return {\n            success: true,\n            expiry\n        };\n    } catch (error) {\n        console.error('Error granting quick video advantage:', error);\n        throw error;\n    }\n}\n// Remove quick video advantage from user (admin function)\nasync function removeQuickVideoAdvantage(userId, removedBy) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: false,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: null,\n            [FIELD_NAMES.quickVideoAdvantageDays]: 0,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: 30,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: '',\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: null\n        });\n        console.log(`Removed quick video advantage from user ${userId}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_removed',\n            amount: 0,\n            description: `Quick video advantage removed by ${removedBy}`\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error removing quick video advantage:', error);\n        throw error;\n    }\n}\n// Update user video duration (admin function)\nasync function updateUserVideoDuration(userId, durationInSeconds) {\n    try {\n        // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)\n        const isQuickDuration = [\n            1,\n            10,\n            30\n        ].includes(durationInSeconds);\n        const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420;\n        if (!isQuickDuration && !isStandardDuration) {\n            throw new Error('Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration');\n        }\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.videoDuration]: durationInSeconds\n        });\n        console.log(`Updated video duration for user ${userId} to ${durationInSeconds} seconds`);\n    } catch (error) {\n        console.error('Error updating user video duration:', error);\n        throw error;\n    }\n}\n// Validate bank details\nfunction validateBankDetails(bankDetails) {\n    const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails;\n    if (!accountHolderName || accountHolderName.trim().length < 2) {\n        throw new Error('Account holder name must be at least 2 characters long');\n    }\n    if (!accountNumber || !/^\\d{9,18}$/.test(accountNumber.trim())) {\n        throw new Error('Account number must be 9-18 digits');\n    }\n    if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {\n        throw new Error('Invalid IFSC code format (e.g., SBIN0001234)');\n    }\n    if (!bankName || bankName.trim().length < 2) {\n        throw new Error('Bank name must be at least 2 characters long');\n    }\n}\n// Add notification (admin function) - All notifications are now blocking\nasync function addNotification(notification) {\n    try {\n        const notificationData = {\n            title: notification.title,\n            message: notification.message,\n            type: notification.type,\n            targetUsers: notification.targetUsers,\n            userIds: notification.userIds || [],\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdBy: notification.createdBy\n        };\n        console.log('Adding notification to Firestore:', notificationData);\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), notificationData);\n        console.log('Notification added successfully with ID:', docRef.id);\n        // Verify the notification was added\n        const addedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n        if (addedDoc.exists()) {\n            console.log('Notification verified in database:', addedDoc.data());\n        } else {\n            console.warn('Notification not found after adding');\n        }\n        return docRef.id;\n    } catch (error) {\n        console.error('Error adding notification:', error);\n        throw error;\n    }\n}\n// Get notifications for a user\nasync function getUserNotifications(userId, limitCount = 20) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading notifications for user: ${userId}`);\n        // Try to get notifications with fallback for indexing issues\n        let allUsersSnapshot, specificUserSnapshot;\n        try {\n            // Get notifications targeted to all users\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n            console.log(`Found ${allUsersSnapshot.docs.length} notifications for all users`);\n        } catch (error) {\n            console.warn('Error querying all users notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n        }\n        try {\n            // Get notifications targeted to specific user\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n            console.log(`Found ${specificUserSnapshot.docs.length} notifications for specific user`);\n        } catch (error) {\n            console.warn('Error querying specific user notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n        }\n        const notifications = [];\n        // Process all users notifications\n        allUsersSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Process specific user notifications\n        specificUserSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Sort by creation date (newest first)\n        notifications.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        const finalNotifications = notifications.slice(0, limitCount);\n        console.log(`Returning ${finalNotifications.length} total notifications for user`);\n        return finalNotifications;\n    } catch (error) {\n        console.error('Error getting user notifications:', error);\n        return [];\n    }\n}\n// Get all notifications (admin function)\nasync function getAllNotifications(limitCount = 50) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const notifications = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            }));\n        return notifications;\n    } catch (error) {\n        console.error('Error getting all notifications:', error);\n        return [];\n    }\n}\n// Delete notification (admin function)\nasync function deleteNotification(notificationId) {\n    try {\n        if (!notificationId || typeof notificationId !== 'string') {\n            throw new Error('Invalid notification ID provided');\n        }\n        console.log('Deleting notification:', notificationId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications, notificationId));\n        console.log('Notification deleted successfully');\n    } catch (error) {\n        console.error('Error deleting notification:', error);\n        throw error;\n    }\n}\n// Mark notification as read\nasync function markNotificationAsRead(notificationId, userId) {\n    try {\n        // For now, we'll store read status in localStorage since it's user-specific\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        if (!readNotifications.includes(notificationId)) {\n            readNotifications.push(notificationId);\n            localStorage.setItem(`read_notifications_${userId}`, JSON.stringify(readNotifications));\n        }\n    } catch (error) {\n        console.error('Error marking notification as read:', error);\n    }\n}\n// Check if notification is read\nfunction isNotificationRead(notificationId, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return readNotifications.includes(notificationId);\n    } catch (error) {\n        console.error('Error checking notification read status:', error);\n        return false;\n    }\n}\n// Get unread notification count\nfunction getUnreadNotificationCount(notifications, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return notifications.filter((notification)=>!readNotifications.includes(notification.id)).length;\n    } catch (error) {\n        console.error('Error getting unread notification count:', error);\n        return 0;\n    }\n}\n// Get unread notifications - All notifications are now blocking\nasync function getUnreadNotifications(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUnreadNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading unread notifications for user: ${userId}`);\n        // Get all notifications for the user\n        const allNotifications = await getUserNotifications(userId, 50);\n        // Filter for unread notifications\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        const unreadNotifications = allNotifications.filter((notification)=>notification.id && !readNotifications.includes(notification.id));\n        console.log(`Found ${unreadNotifications.length} unread notifications`);\n        return unreadNotifications;\n    } catch (error) {\n        console.error('Error getting unread notifications:', error);\n        return [];\n    }\n}\n// Check if user has unread notifications\nasync function hasUnreadNotifications(userId) {\n    try {\n        const unreadNotifications = await getUnreadNotifications(userId);\n        return unreadNotifications.length > 0;\n    } catch (error) {\n        console.error('Error checking for unread notifications:', error);\n        return false;\n    }\n}\n// Check if user has pending withdrawals\nasync function hasPendingWithdrawals(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking pending withdrawals:', error);\n        return false;\n    }\n}\n// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)\nasync function checkWithdrawalAllowed(userId) {\n    try {\n        // Check user plan first\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                allowed: false,\n                reason: 'Unable to verify user information. Please try again.'\n            };\n        }\n        // Check if user is on trial plan\n        if (userData.plan === 'Trial') {\n            return {\n                allowed: false,\n                reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'\n            };\n        }\n        // Check if user has pending withdrawals\n        const hasPending = await hasPendingWithdrawals(userId);\n        if (hasPending) {\n            return {\n                allowed: false,\n                reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'\n            };\n        }\n        const now = new Date();\n        const currentHour = now.getHours();\n        // Check time restrictions (10 AM to 6 PM)\n        if (currentHour < 10 || currentHour >= 18) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'\n            };\n        }\n        // Check admin leave day\n        const { isAdminLeaveDay } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(now);\n        if (isAdminLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on admin leave/holiday days'\n            };\n        }\n        // Check user leave day\n        const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isUserLeave = await isUserOnLeave(userId, now);\n        if (isUserLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on your leave days'\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking withdrawal allowed:', error);\n        return {\n            allowed: false,\n            reason: 'Unable to verify withdrawal eligibility. Please try again.'\n        };\n    }\n}\n// Create withdrawal request\nasync function createWithdrawalRequest(userId, amount, bankDetails) {\n    try {\n        // Check minimum withdrawal amount\n        if (amount < 50) {\n            throw new Error('Minimum withdrawal amount is ₹50');\n        }\n        // Check if withdrawal is allowed\n        const withdrawalCheck = await checkWithdrawalAllowed(userId);\n        if (!withdrawalCheck.allowed) {\n            throw new Error(withdrawalCheck.reason);\n        }\n        // Check if user has sufficient balance\n        const walletData = await getWalletData(userId);\n        if (walletData.wallet < amount) {\n            throw new Error('Insufficient wallet balance');\n        }\n        // Debit the amount from user's wallet immediately\n        await updateWalletBalance(userId, -amount);\n        // Add transaction record for withdrawal debit\n        await addTransaction(userId, {\n            type: 'withdrawal_request',\n            amount: -amount,\n            description: `Withdrawal request submitted - ₹${amount} debited from wallet`\n        });\n        const withdrawalData = {\n            userId,\n            amount,\n            bankDetails,\n            status: 'pending',\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), withdrawalData);\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating withdrawal request:', error);\n        throw error;\n    }\n}\n// Get user withdrawals\nasync function getUserWithdrawals(userId, limitCount = 20) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting user withdrawals:', error);\n        return [];\n    }\n}\n// Generate sequential referral code - using highest existing number + 1 approach\nasync function generateSequentialReferralCode() {\n    try {\n        console.log('Generating sequential referral code...');\n        // Primary Method: Find the highest existing MYN code and increment\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            // Get all MYN codes and find the highest number\n            const mynCodesQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '>=', 'MYN0000'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '<=', 'MYN9999'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(FIELD_NAMES.referralCode, 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n            const mynCodesSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(mynCodesQuery);\n            let nextNumber = 1;\n            if (!mynCodesSnapshot.empty) {\n                const highestCode = mynCodesSnapshot.docs[0].data()[FIELD_NAMES.referralCode];\n                console.log('Highest existing MYN code:', highestCode);\n                if (highestCode && highestCode.startsWith('MYN')) {\n                    const numberPart = highestCode.substring(3);\n                    const currentNumber = parseInt(numberPart);\n                    if (!isNaN(currentNumber)) {\n                        nextNumber = currentNumber + 1;\n                        console.log(`Next sequential number: ${nextNumber}`);\n                    }\n                }\n            } else {\n                console.log('No existing MYN codes found, starting from MYN0001');\n            }\n            const code = `MYN${String(nextNumber).padStart(4, '0')}`;\n            console.log(`Generated sequential referral code: ${code}`);\n            return code;\n        } catch (sequentialError) {\n            console.log('Sequential method failed, trying fallback:', sequentialError);\n        }\n        // Fallback Method: Use user count + 1\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getCountFromServer)(usersCollection);\n            const count = snapshot.data().count + 1;\n            const fallbackCode = `MYN${String(count).padStart(4, '0')}`;\n            console.log(`Using count-based fallback code: ${fallbackCode}`);\n            return fallbackCode;\n        } catch (countError) {\n            console.log('Count method also failed, using random fallback:', countError);\n        }\n        // Final Fallback: Random code\n        const randomCode = `MYN${Math.floor(1000 + Math.random() * 9000)}`;\n        console.log('Using random fallback referral code:', randomCode);\n        return randomCode;\n    } catch (error) {\n        console.error('Error generating referral code:', error);\n        // Ultimate fallback\n        const emergencyCode = `MYN${Math.floor(1000 + Math.random() * 9000)}`;\n        console.log('Using emergency fallback referral code:', emergencyCode);\n        return emergencyCode;\n    }\n}\n// Check if referral code exists\nasync function checkReferralCodeExists(code) {\n    try {\n        const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n        const codeQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', code), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(codeQuery);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking referral code:', error);\n        return false;\n    }\n}\n// Generate unique referral code with retry logic\nasync function generateUniqueReferralCode(maxRetries = 5) {\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            const code = await generateSequentialReferralCode();\n            const exists = await checkReferralCodeExists(code);\n            if (!exists) {\n                console.log(`Generated unique referral code: ${code} (attempt ${attempt})`);\n                return code;\n            } else {\n                console.log(`Code ${code} already exists, retrying... (attempt ${attempt})`);\n            }\n        } catch (error) {\n            console.error(`Error in attempt ${attempt}:`, error);\n        }\n    }\n    // Final fallback with timestamp to ensure uniqueness\n    const timestamp = Date.now().toString().slice(-4);\n    const fallbackCode = `MYN${timestamp}`;\n    console.log(`Using timestamp-based fallback code: ${fallbackCode}`);\n    return fallbackCode;\n}\n// Get current referral counter (for admin purposes)\nasync function getCurrentReferralCounter() {\n    try {\n        const counterRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'system', 'referralCounter');\n        const counterDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(counterRef);\n        if (counterDoc.exists()) {\n            return counterDoc.data().lastNumber || 0;\n        }\n        return 0;\n    } catch (error) {\n        console.error('Error getting referral counter:', error);\n        return 0;\n    }\n}\n// Simple referral code generation for registration (no database queries to avoid conflicts)\nfunction generateSimpleReferralCode() {\n    // Use current timestamp + random string for better uniqueness\n    const timestamp = Date.now().toString();\n    const randomPart = Math.random().toString(36).substring(2, 8).toUpperCase();\n    // Create a more unique code using full timestamp with MYN prefix\n    const uniqueCode = `MYN${timestamp.slice(-6)}${randomPart.substring(0, 1)}`;\n    console.log('Generated simple referral code:', uniqueCode);\n    return uniqueCode;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/dataService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ\",\n    authDomain: \"mytube-india.firebaseapp.com\",\n    projectId: \"mytube-india\",\n    storageBucket: \"mytube-india.firebasestorage.app\",\n    messagingSenderId: \"************\",\n    appId: \"1:************:web:ebedaec6a492926af2056a\",\n    measurementId: \"G-R24C6N7CWJ\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)() : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/@opentelemetry","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/@swc","vendor-chunks/sweetalert2"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();