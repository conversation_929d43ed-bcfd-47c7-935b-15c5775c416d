/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/register/page";
exports.ids = ["app/register/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(rsc)/./src/app/register/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'register',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\"],\n'error': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/register/page\",\n        pathname: \"/register\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQVNVUyU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q01ZJTIwUFJPSkVDVFMlNUMlNUNOb2RlJTIwTXl0dWJlJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQVNVUyU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q01ZJTIwUFJPSkVDVFMlNUMlNUNOb2RlJTIwTXl0dWJlJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQStKO0FBQy9KO0FBQ0EsME9BQWtLO0FBQ2xLO0FBQ0EsME9BQWtLO0FBQ2xLO0FBQ0Esb1JBQXdMO0FBQ3hMO0FBQ0Esd09BQWlLO0FBQ2pLO0FBQ0EsNFBBQTRLO0FBQzVLO0FBQ0Esa1FBQStLO0FBQy9LO0FBQ0Esc1FBQWdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFTVVNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxNWSBQUk9KRUNUU1xcXFxOb2RlIE15dHViZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFTVVNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxNWSBQUk9KRUNUU1xcXFxOb2RlIE15dHViZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(rsc)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(rsc)/./src/components/PWAInstaller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(rsc)/./src/app/register/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3JlZ2lzdGVyJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUErSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXHNyY1xcXFxhcHBcXFxccmVnaXN0ZXJcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fe166ce4cd0c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFTVVNcXE9uZURyaXZlXFxEZXNrdG9wXFxNWSBQUk9KRUNUU1xcTm9kZSBNeXR1YmVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZlMTY2Y2U0Y2QwY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PWAInstaller */ \"(rsc)/./src/components/PWAInstaller.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'MyTube - Watch Videos & Earn',\n    description: 'Watch videos and earn money. Complete daily video watching tasks to earn rewards.',\n    keywords: 'video watching, earn money, online earning, video tasks, rewards',\n    authors: [\n        {\n            name: 'MyTube Team'\n        }\n    ],\n    manifest: '/manifest.json',\n    icons: {\n        icon: '/img/mytube-favicon.svg',\n        apple: '/img/mytube-favicon.svg'\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1.0,\n    maximumScale: 1.0,\n    userScalable: false,\n    themeColor: '#FF0000'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"https://cdn.jsdelivr.net/npm/sweetalert2@11\",\n                        async: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animated-bg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-white/80\",\n                    children: \"Loading MyTube...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQVNVU1xcT25lRHJpdmVcXERlc2t0b3BcXE1ZIFBST0pFQ1RTXFxOb2RlIE15dHViZVxcc3JjXFxhcHBcXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGlubmVyIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MFwiPkxvYWRpbmcgTXlUdWJlLi4uPC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/img/mytube-logo.svg\",\n                            alt: \"MyTube Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-white mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-white mb-2\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"The page you're looking for doesn't exist or has been moved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need help finding what you're looking for?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-home mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/work\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-play-circle mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Watch Videos\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/register/page.tsx":
/*!***********************************!*\
  !*** ./src/app/register/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\register\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\PWAInstaller.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBNeXR1YmUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(ssr)/./src/components/PWAInstaller.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBNeXR1YmVcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(ssr)/./src/app/register/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyME15dHViZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3JlZ2lzdGVyJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUErSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgTXl0dWJlXFxcXHNyY1xcXFxhcHBcXFxccmVnaXN0ZXJcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Mytube%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            console.error('Application error:', error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/img/mytube-logo.svg\",\n                            alt: \"MyTube Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-white mb-4\",\n                            children: \"Oops!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"We encountered an unexpected error. Please try again or contact support if the problem persists.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need immediate help?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-redo mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-home mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Go Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-8 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"text-white/60 cursor-pointer\",\n                            children: \"Error Details (Development)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-4 p-4 bg-red-900/20 rounded-lg text-red-300 text-sm overflow-auto\",\n                            children: [\n                                error.message,\n                                error.stack && '\\n\\n' + error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/register/page.tsx":
/*!***********************************!*\
  !*** ./src/app/register/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/dataService */ \"(ssr)/./src/lib/dataService.ts\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sweetalert2 */ \"(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction RegisterPage() {\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__.useAuthState)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        mobile: '',\n        password: '',\n        confirmPassword: '',\n        referralCode: ''\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            if (user && !loading) {\n                window.location.href = '/dashboard';\n            }\n        }\n    }[\"RegisterPage.useEffect\"], [\n        user,\n        loading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            // Get referral code from URL if present\n            const urlParams = new URLSearchParams(window.location.search);\n            const refCode = urlParams.get('ref');\n            if (refCode) {\n                setFormData({\n                    \"RegisterPage.useEffect\": (prev)=>({\n                            ...prev,\n                            referralCode: refCode\n                        })\n                }[\"RegisterPage.useEffect\"]);\n            }\n        }\n    }[\"RegisterPage.useEffect\"], []);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    // Referral code generation is handled by generateUniqueReferralCode function\n    const validateForm = ()=>{\n        const { name, email, mobile, password, confirmPassword } = formData;\n        if (!name || !email || !mobile || !password || !confirmPassword) {\n            throw new Error('Please fill in all required fields');\n        }\n        if (name.length < 2) {\n            throw new Error('Name must be at least 2 characters long');\n        }\n        if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n            throw new Error('Please enter a valid email address');\n        }\n        if (!/^[6-9]\\d{9}$/.test(mobile)) {\n            throw new Error('Please enter a valid 10-digit mobile number');\n        }\n        if (password.length < 6) {\n            throw new Error('Password must be at least 6 characters long');\n        }\n        if (password !== confirmPassword) {\n            throw new Error('Passwords do not match');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            validateForm();\n            setIsLoading(true);\n            // Create user account first - Firebase Auth will handle email uniqueness\n            // Mobile number uniqueness will be checked after user creation\n            console.log('Creating user with email and password...');\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_4__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.auth, formData.email, formData.password);\n            const user = userCredential.user;\n            console.log('Firebase Auth user created successfully:', user.uid);\n            console.log('Generating referral code...');\n            // Use a simple, reliable referral code generation for registration\n            // Complex sequential generation can be done later by admin if needed\n            const timestamp = Date.now().toString().slice(-4);\n            const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase();\n            const userReferralCode = `MY${timestamp}${randomPart}`;\n            console.log('Generated referral code:', userReferralCode);\n            // Note: Mobile number uniqueness is handled by the admin panel\n            // Firebase Auth already ensures email uniqueness\n            // Create user document in Firestore with all required fields\n            const userData = {\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.name]: formData.name.trim(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.email]: formData.email.toLowerCase(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.mobile]: formData.mobile,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referralCode]: userReferralCode,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referredBy]: formData.referralCode || '',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referralBonusCredited]: false,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.plan]: 'Trial',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.planExpiry]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.activeDays]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.joinedDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.Timestamp.now(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.wallet]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.totalVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.todayVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.lastVideoDate]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.videoDuration]: 30,\n                status: 'active'\n            };\n            console.log('Creating user document with data:', userData);\n            console.log('User UID:', user.uid);\n            console.log('Collection:', _lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users);\n            console.log('Document path:', `${_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users}/${user.uid}`);\n            // Create user document in Firestore\n            console.log('Creating user document in Firestore...');\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, _lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users, user.uid);\n            console.log('Document reference created:', userDocRef.path);\n            console.log('About to create document with data:', JSON.stringify(userData, null, 2));\n            try {\n                console.log('Attempting to create document...');\n                console.log('User UID:', user.uid);\n                console.log('Document path:', userDocRef.path);\n                console.log('Auth user email:', user.email);\n                console.log('Auth user verified:', user.emailVerified);\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.setDoc)(userDocRef, userData);\n                console.log('✅ User document created successfully');\n                // Verify the document was created\n                const verifyDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.getDoc)(userDocRef);\n                if (verifyDoc.exists()) {\n                    console.log('✅ Document verification successful:', verifyDoc.data());\n                } else {\n                    console.error('❌ Document was not created properly');\n                    throw new Error('User document was not created properly');\n                }\n            } catch (firestoreError) {\n                console.error('❌ Firestore setDoc failed:', firestoreError);\n                console.error('❌ Firestore error code:', firestoreError.code);\n                console.error('❌ Firestore error message:', firestoreError.message);\n                console.error('❌ Full error object:', JSON.stringify(firestoreError, null, 2));\n                throw new Error(`Failed to create user profile: ${firestoreError.message}`);\n            }\n            // Note: Referral bonus will be credited when admin upgrades user from Trial to paid plan\n            console.log('User registered successfully. Referral bonus will be processed when upgraded to paid plan.');\n            // Show success message\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9__[\"default\"].fire({\n                icon: 'success',\n                title: 'Registration Successful!',\n                text: 'Your account has been created successfully. Welcome to MyTube!',\n                timer: 3000,\n                showConfirmButton: false\n            });\n        // Redirect will be handled by useEffect\n        } catch (error) {\n            console.error('Registration error:', error);\n            console.error('Error code:', error.code);\n            console.error('Error message:', error.message);\n            console.error('Full error object:', JSON.stringify(error, null, 2));\n            let message = 'An error occurred during registration';\n            if (error.message.includes('fill in all')) {\n                message = error.message;\n            } else if (error.message.includes('Name must be')) {\n                message = error.message;\n            } else if (error.message.includes('valid email')) {\n                message = error.message;\n            } else if (error.message.includes('valid 10-digit')) {\n                message = error.message;\n            } else if (error.message.includes('Password must be')) {\n                message = error.message;\n            } else if (error.message.includes('Passwords do not match')) {\n                message = error.message;\n            } else if (error.message.includes('email address is already registered')) {\n                message = error.message;\n            } else if (error.message.includes('mobile number is already registered')) {\n                message = error.message;\n            } else {\n                switch(error.code){\n                    case 'auth/email-already-in-use':\n                        message = 'An account with this email already exists';\n                        break;\n                    case 'auth/invalid-email':\n                        message = 'Invalid email address';\n                        break;\n                    case 'auth/weak-password':\n                        message = 'Password is too weak';\n                        break;\n                    default:\n                        message = error.message || 'Registration failed';\n                }\n            }\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9__[\"default\"].fire({\n                icon: 'error',\n                title: 'Registration Failed',\n                text: message\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass-card w-full max-w-md p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/img/mytube-logo.svg\",\n                                    alt: \"MyTube Logo\",\n                                    width: 50,\n                                    height: 50,\n                                    className: \"mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"MyTube\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-white mb-2\",\n                            children: \"Create Account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80\",\n                            children: \"Join MyTube and start earning today\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"name\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Full Name *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"name\",\n                                    name: \"name\",\n                                    value: formData.name,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter your full name\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"email\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Email Address *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    id: \"email\",\n                                    name: \"email\",\n                                    value: formData.email,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter your email\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"mobile\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Mobile Number *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"tel\",\n                                    id: \"mobile\",\n                                    name: \"mobile\",\n                                    value: formData.mobile,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter 10-digit mobile number\",\n                                    maxLength: 10,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"password\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Password *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            id: \"password\",\n                                            name: \"password\",\n                                            value: formData.password,\n                                            onChange: handleInputChange,\n                                            className: \"form-input pr-12\",\n                                            placeholder: \"Enter password (min 6 characters)\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"password-toggle-btn\",\n                                            \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: `fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmPassword\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Confirm Password *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            value: formData.confirmPassword,\n                                            onChange: handleInputChange,\n                                            className: \"form-input pr-12\",\n                                            placeholder: \"Confirm your password\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            className: \"password-toggle-btn\",\n                                            \"aria-label\": showConfirmPassword ? \"Hide confirm password\" : \"Show confirm password\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: `fas ${showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"referralCode\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Referral Code (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"referralCode\",\n                                    name: \"referralCode\",\n                                    value: formData.referralCode,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter referral code if you have one\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"w-full btn-primary flex items-center justify-center mt-6\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner mr-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Creating Account...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-user-plus mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Create Account\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/60\",\n                        children: [\n                            \"Already have an account?\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"text-white font-semibold hover:underline\",\n                                children: \"Sign in here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-white/80 hover:text-white transition-colors inline-flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-arrow-left mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Home\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/register/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-8 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"fas fa-exclamation-triangle text-red-400 text-4xl mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-4\",\n                            children: \"An error occurred while loading this page. Please refresh and try again.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-refresh mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                \"Refresh Page\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PWAInstaller)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction PWAInstaller() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInstallButton, setShowInstallButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PWAInstaller.useEffect\": ()=>{\n            // Register service worker\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"PWAInstaller.useEffect\": (registration)=>{\n                        console.log('SW registered: ', registration);\n                    }\n                }[\"PWAInstaller.useEffect\"]).catch({\n                    \"PWAInstaller.useEffect\": (registrationError)=>{\n                        console.log('SW registration failed: ', registrationError);\n                    }\n                }[\"PWAInstaller.useEffect\"]);\n            }\n            // Listen for beforeinstallprompt event\n            const handleBeforeInstallPrompt = {\n                \"PWAInstaller.useEffect.handleBeforeInstallPrompt\": (e)=>{\n                    e.preventDefault();\n                    setDeferredPrompt(e);\n                    setShowInstallButton(true);\n                }\n            }[\"PWAInstaller.useEffect.handleBeforeInstallPrompt\"];\n            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n            // Check if app is already installed\n            if (window.matchMedia('(display-mode: standalone)').matches) {\n                setShowInstallButton(false);\n            }\n            return ({\n                \"PWAInstaller.useEffect\": ()=>{\n                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n                }\n            })[\"PWAInstaller.useEffect\"];\n        }\n    }[\"PWAInstaller.useEffect\"], []);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        deferredPrompt.prompt();\n        const { outcome } = await deferredPrompt.userChoice;\n        if (outcome === 'accepted') {\n            console.log('User accepted the install prompt');\n        } else {\n            console.log('User dismissed the install prompt');\n        }\n        setDeferredPrompt(null);\n        setShowInstallButton(false);\n    };\n    if (!showInstallButton) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleInstallClick,\n            className: \"glass-button px-4 py-3 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-download mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                \"Install App\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PWAInstaller.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthState: () => (/* binding */ useAuthState),\n/* harmony export */   useRequireAdmin: () => (/* binding */ useRequireAdmin),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/authUtils */ \"(ssr)/./src/lib/authUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuthState,useRequireAuth,useRequireAdmin auto */ \n\n\n\nfunction useAuthState() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuthState.useEffect\": ()=>{\n            try {\n                const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, {\n                    \"useAuthState.useEffect.unsubscribe\": (user)=>{\n                        console.log('Auth state changed:', user ? 'User logged in' : 'No user');\n                        setUser(user);\n                        setLoading(false);\n                    }\n                }[\"useAuthState.useEffect.unsubscribe\"]);\n                return ({\n                    \"useAuthState.useEffect\": ()=>unsubscribe()\n                })[\"useAuthState.useEffect\"];\n            } catch (error) {\n                console.error('Error in auth state listener:', error);\n                setLoading(false);\n            }\n        }\n    }[\"useAuthState.useEffect\"], []);\n    const signOut = async ()=>{\n        try {\n            await (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_3__.quickLogout)(user?.uid, '/');\n        } catch (error) {\n            console.error('Error signing out:', error);\n            // Force redirect on error\n            window.location.href = '/';\n        }\n    };\n    return {\n        user,\n        loading,\n        signOut\n    };\n}\nfunction useRequireAuth() {\n    const { user, loading } = useAuthState();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRequireAuth.useEffect\": ()=>{\n            if (!loading && !user) {\n                window.location.href = '/login';\n            }\n        }\n    }[\"useRequireAuth.useEffect\"], [\n        user,\n        loading\n    ]);\n    return {\n        user,\n        loading\n    };\n}\nfunction useRequireAdmin() {\n    const { user, loading } = useAuthState();\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [adminLoading, setAdminLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRequireAdmin.useEffect\": ()=>{\n            if (!loading && !user) {\n                window.location.href = '/admin/login';\n                return;\n            }\n            if (user) {\n                // Check if user is admin\n                // This would typically involve checking a custom claim or database\n                // For now, we'll use a simple email check\n                const adminEmails = [\n                    '<EMAIL>',\n                    '<EMAIL>'\n                ];\n                const userIsAdmin = adminEmails.includes(user.email || '');\n                setIsAdmin(userIsAdmin);\n                setAdminLoading(false);\n                if (!userIsAdmin) {\n                    window.location.href = '/login';\n                }\n            }\n        }\n    }[\"useRequireAdmin.useEffect\"], [\n        user,\n        loading\n    ]);\n    return {\n        user,\n        loading: loading || adminLoading,\n        isAdmin\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/authUtils.ts":
/*!******************************!*\
  !*** ./src/lib/authUtils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearExpiredSessions: () => (/* binding */ clearExpiredSessions),\n/* harmony export */   clearUserLocalStorage: () => (/* binding */ clearUserLocalStorage),\n/* harmony export */   getUserSessionInfo: () => (/* binding */ getUserSessionInfo),\n/* harmony export */   handleUserLogout: () => (/* binding */ handleUserLogout),\n/* harmony export */   quickLogout: () => (/* binding */ quickLogout)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sweetalert2 */ \"(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js\");\n\n\n/**\n * Clear all user-specific data from localStorage\n */ function clearUserLocalStorage(userId) {\n    try {\n        // Get all localStorage keys\n        const keys = Object.keys(localStorage);\n        // Remove user-specific data\n        keys.forEach((key)=>{\n            if (key.includes(userId) || key.startsWith('video_session_') || key.startsWith('watch_times_') || key.startsWith('video_refresh_') || key.startsWith('video_change_notification_') || key.startsWith('leave_') || key.includes('mytube_') || key.includes('user_')) {\n                localStorage.removeItem(key);\n            }\n        });\n        // Also clear common app data\n        const commonKeys = [\n            'currentUser',\n            'authToken',\n            'userSession',\n            'appState',\n            'videoProgress',\n            'sessionData',\n            'workSession',\n            'walletCache',\n            'transactionCache'\n        ];\n        commonKeys.forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n        console.log('Local storage cleared for user:', userId);\n    } catch (error) {\n        console.error('Error clearing local storage:', error);\n    }\n}\n/**\n * Handle user logout with confirmation and cleanup\n */ async function handleUserLogout(userId, redirectPath = '/login') {\n    try {\n        const result = await sweetalert2__WEBPACK_IMPORTED_MODULE_1__[\"default\"].fire({\n            title: 'Logout Confirmation',\n            text: 'Are you sure you want to logout?',\n            icon: 'question',\n            showCancelButton: true,\n            confirmButtonColor: '#ef4444',\n            cancelButtonColor: '#6b7280',\n            confirmButtonText: 'Yes, Logout',\n            cancelButtonText: 'Cancel'\n        });\n        if (result.isConfirmed) {\n            // Clear user-specific local storage data\n            if (userId) {\n                clearUserLocalStorage(userId);\n            }\n            // Sign out from Firebase\n            await _firebase__WEBPACK_IMPORTED_MODULE_0__.auth.signOut();\n            // Show success message\n            sweetalert2__WEBPACK_IMPORTED_MODULE_1__[\"default\"].fire({\n                icon: 'success',\n                title: 'Logged Out Successfully',\n                text: 'You have been logged out. Redirecting...',\n                timer: 2000,\n                showConfirmButton: false\n            }).then(()=>{\n                // Redirect to specified path\n                window.location.href = redirectPath;\n            });\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error('Logout error:', error);\n        sweetalert2__WEBPACK_IMPORTED_MODULE_1__[\"default\"].fire({\n            icon: 'error',\n            title: 'Logout Failed',\n            text: 'There was an error logging out. Please try again.'\n        });\n        return false;\n    }\n}\n/**\n * Quick logout without confirmation (for emergency cases)\n */ async function quickLogout(userId, redirectPath = '/login') {\n    try {\n        // Clear user-specific local storage data\n        if (userId) {\n            clearUserLocalStorage(userId);\n        }\n        // Sign out from Firebase\n        await _firebase__WEBPACK_IMPORTED_MODULE_0__.auth.signOut();\n        // Redirect immediately\n        window.location.href = redirectPath;\n    } catch (error) {\n        console.error('Quick logout error:', error);\n        // Force redirect even if logout fails\n        window.location.href = redirectPath;\n    }\n}\n/**\n * Clear session data on app start (useful for cleanup)\n */ function clearExpiredSessions() {\n    try {\n        const keys = Object.keys(localStorage);\n        const today = new Date().toDateString();\n        keys.forEach((key)=>{\n            // Clear old session data (not from today)\n            if (key.startsWith('video_session_') || key.startsWith('watch_times_')) {\n                const storedData = localStorage.getItem(key);\n                if (storedData) {\n                    try {\n                        // Check if it's from today\n                        if (!key.includes(today)) {\n                            localStorage.removeItem(key);\n                            console.log('Cleared expired session:', key);\n                        }\n                    } catch (error) {\n                        // If we can't parse it, remove it\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Error clearing expired sessions:', error);\n    }\n}\n/**\n * Get user session info from localStorage\n */ function getUserSessionInfo(userId) {\n    try {\n        const today = new Date().toDateString();\n        const sessionKey = `video_session_${userId}_${today}`;\n        const watchTimesKey = `watch_times_${userId}_${today}`;\n        const sessionCount = localStorage.getItem(sessionKey);\n        const watchTimes = localStorage.getItem(watchTimesKey);\n        return {\n            videoCount: sessionCount ? parseInt(sessionCount) : 0,\n            watchTimes: watchTimes ? JSON.parse(watchTimes) : [],\n            hasActiveSession: !!(sessionCount || watchTimes)\n        };\n    } catch (error) {\n        console.error('Error getting session info:', error);\n        return {\n            videoCount: 0,\n            watchTimes: [],\n            hasActiveSession: false\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/authUtils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/dataService.ts":
/*!********************************!*\
  !*** ./src/lib/dataService.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   FIELD_NAMES: () => (/* binding */ FIELD_NAMES),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   addTransaction: () => (/* binding */ addTransaction),\n/* harmony export */   checkQuickVideoAdvantageActive: () => (/* binding */ checkQuickVideoAdvantageActive),\n/* harmony export */   checkWithdrawalAllowed: () => (/* binding */ checkWithdrawalAllowed),\n/* harmony export */   createWithdrawalRequest: () => (/* binding */ createWithdrawalRequest),\n/* harmony export */   deleteNotification: () => (/* binding */ deleteNotification),\n/* harmony export */   getAllNotifications: () => (/* binding */ getAllNotifications),\n/* harmony export */   getBankDetails: () => (/* binding */ getBankDetails),\n/* harmony export */   getPlanEarning: () => (/* binding */ getPlanEarning),\n/* harmony export */   getPlanValidityDays: () => (/* binding */ getPlanValidityDays),\n/* harmony export */   getPlanVideoDuration: () => (/* binding */ getPlanVideoDuration),\n/* harmony export */   getReferralBonus: () => (/* binding */ getReferralBonus),\n/* harmony export */   getReferrals: () => (/* binding */ getReferrals),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUnreadNotificationCount: () => (/* binding */ getUnreadNotificationCount),\n/* harmony export */   getUnreadNotifications: () => (/* binding */ getUnreadNotifications),\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   getUserNotifications: () => (/* binding */ getUserNotifications),\n/* harmony export */   getUserVideoSettings: () => (/* binding */ getUserVideoSettings),\n/* harmony export */   getUserWithdrawals: () => (/* binding */ getUserWithdrawals),\n/* harmony export */   getVideoCountData: () => (/* binding */ getVideoCountData),\n/* harmony export */   getWalletData: () => (/* binding */ getWalletData),\n/* harmony export */   grantQuickVideoAdvantage: () => (/* binding */ grantQuickVideoAdvantage),\n/* harmony export */   hasPendingWithdrawals: () => (/* binding */ hasPendingWithdrawals),\n/* harmony export */   hasUnreadNotifications: () => (/* binding */ hasUnreadNotifications),\n/* harmony export */   isNotificationRead: () => (/* binding */ isNotificationRead),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead),\n/* harmony export */   processReferralBonus: () => (/* binding */ processReferralBonus),\n/* harmony export */   removeQuickVideoAdvantage: () => (/* binding */ removeQuickVideoAdvantage),\n/* harmony export */   saveBankDetails: () => (/* binding */ saveBankDetails),\n/* harmony export */   updateUserData: () => (/* binding */ updateUserData),\n/* harmony export */   updateUserPlanExpiry: () => (/* binding */ updateUserPlanExpiry),\n/* harmony export */   updateUserVideoDuration: () => (/* binding */ updateUserVideoDuration),\n/* harmony export */   updateVideoCount: () => (/* binding */ updateVideoCount),\n/* harmony export */   updateWalletBalance: () => (/* binding */ updateWalletBalance)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\n// Field names for Firestore collections\nconst FIELD_NAMES = {\n    // User fields\n    name: 'name',\n    email: 'email',\n    mobile: 'mobile',\n    referralCode: 'referralCode',\n    referredBy: 'referredBy',\n    referralBonusCredited: 'referralBonusCredited',\n    plan: 'plan',\n    planExpiry: 'planExpiry',\n    activeDays: 'activeDays',\n    joinedDate: 'joinedDate',\n    // Wallet fields\n    wallet: 'wallet',\n    // Bank details fields\n    bankAccountHolderName: 'bankAccountHolderName',\n    bankAccountNumber: 'bankAccountNumber',\n    bankIfscCode: 'bankIfscCode',\n    bankName: 'bankName',\n    bankDetailsUpdated: 'bankDetailsUpdated',\n    // Video fields\n    totalVideos: 'totalVideos',\n    todayVideos: 'todayVideos',\n    lastVideoDate: 'lastVideoDate',\n    videoDuration: 'videoDuration',\n    // Quick Video Advantage fields\n    quickVideoAdvantage: 'quickVideoAdvantage',\n    quickVideoAdvantageExpiry: 'quickVideoAdvantageExpiry',\n    quickVideoAdvantageDays: 'quickVideoAdvantageDays',\n    quickVideoAdvantageSeconds: 'quickVideoAdvantageSeconds',\n    quickVideoAdvantageGrantedBy: 'quickVideoAdvantageGrantedBy',\n    quickVideoAdvantageGrantedAt: 'quickVideoAdvantageGrantedAt',\n    // Transaction fields\n    type: 'type',\n    amount: 'amount',\n    date: 'date',\n    status: 'status',\n    description: 'description',\n    userId: 'userId'\n};\n// Collection names\nconst COLLECTIONS = {\n    users: 'users',\n    transactions: 'transactions',\n    withdrawals: 'withdrawals',\n    plans: 'plans',\n    settings: 'settings',\n    notifications: 'notifications',\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Get user data\nasync function getUserData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserData:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Ensure all values are properly typed\n            const result = {\n                name: String(data[FIELD_NAMES.name] || ''),\n                email: String(data[FIELD_NAMES.email] || ''),\n                mobile: String(data[FIELD_NAMES.mobile] || ''),\n                referralCode: String(data[FIELD_NAMES.referralCode] || ''),\n                referredBy: String(data[FIELD_NAMES.referredBy] || ''),\n                plan: String(data[FIELD_NAMES.plan] || 'Trial'),\n                planExpiry: data[FIELD_NAMES.planExpiry]?.toDate() || null,\n                activeDays: Number(data[FIELD_NAMES.activeDays] || 0),\n                joinedDate: data[FIELD_NAMES.joinedDate]?.toDate() || new Date(),\n                videoDuration: Number(data[FIELD_NAMES.videoDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)),\n                // Quick Video Advantage fields\n                quickVideoAdvantage: Boolean(data[FIELD_NAMES.quickVideoAdvantage] || false),\n                quickVideoAdvantageExpiry: data[FIELD_NAMES.quickVideoAdvantageExpiry]?.toDate() || null,\n                quickVideoAdvantageDays: Number(data[FIELD_NAMES.quickVideoAdvantageDays] || 0),\n                quickVideoAdvantageSeconds: Number(data[FIELD_NAMES.quickVideoAdvantageSeconds] || 30),\n                quickVideoAdvantageGrantedBy: String(data[FIELD_NAMES.quickVideoAdvantageGrantedBy] || ''),\n                quickVideoAdvantageGrantedAt: data[FIELD_NAMES.quickVideoAdvantageGrantedAt]?.toDate() || null\n            };\n            console.log('getUserData result:', result);\n            return result;\n        }\n        return null;\n    } catch (error) {\n        console.error('Error getting user data:', error);\n        return null // Return null instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get wallet data\nasync function getWalletData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getWalletData:', userId);\n            return {\n                wallet: 0\n            };\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const result = {\n                wallet: Number(data[FIELD_NAMES.wallet] || 0)\n            };\n            console.log('getWalletData result:', result);\n            return result;\n        }\n        return {\n            wallet: 0\n        };\n    } catch (error) {\n        console.error('Error getting wallet data:', error);\n        return {\n            wallet: 0\n        } // Return default instead of throwing\n        ;\n    }\n}\n// Get video count data\nasync function getVideoCountData(userId) {\n    try {\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const totalVideos = data[FIELD_NAMES.totalVideos] || 0;\n            const todayVideos = data[FIELD_NAMES.todayVideos] || 0;\n            const lastVideoDate = data[FIELD_NAMES.lastVideoDate]?.toDate();\n            // Check if it's a new day\n            const today = new Date();\n            const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== today.toDateString();\n            return {\n                totalVideos,\n                todayVideos: isNewDay ? 0 : todayVideos,\n                remainingVideos: Math.max(0, 50 - (isNewDay ? 0 : todayVideos))\n            };\n        }\n        return {\n            totalVideos: 0,\n            todayVideos: 0,\n            remainingVideos: 50\n        };\n    } catch (error) {\n        console.error('Error getting video count data:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUserData(userId, data) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), data);\n    } catch (error) {\n        console.error('Error updating user data:', error);\n        throw error;\n    }\n}\n// Add transaction\nasync function addTransaction(userId, transactionData) {\n    try {\n        const transaction = {\n            [FIELD_NAMES.userId]: userId,\n            [FIELD_NAMES.type]: transactionData.type,\n            [FIELD_NAMES.amount]: transactionData.amount,\n            [FIELD_NAMES.description]: transactionData.description,\n            [FIELD_NAMES.status]: transactionData.status || 'completed',\n            [FIELD_NAMES.date]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), transaction);\n    } catch (error) {\n        console.error('Error adding transaction:', error);\n        throw error;\n    }\n}\n// Get transactions\nasync function getTransactions(userId, limitCount = 10) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getTransactions:', userId);\n            return [];\n        }\n        // Temporary fix: Use only where clause without orderBy to avoid index requirement\n        // TODO: Create composite index in Firebase console for better performance\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.userId, '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data()[FIELD_NAMES.date]?.toDate()\n            }));\n        // Sort in memory since we can't use orderBy without index\n        transactions.sort((a, b)=>{\n            const dateA = a.date || new Date(0);\n            const dateB = b.date || new Date(0);\n            return dateB.getTime() - dateA.getTime() // Descending order\n            ;\n        });\n        return transactions;\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        return [] // Return empty array instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get referrals\nasync function getReferrals(referralCode) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referredBy, '==', referralCode));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting referrals:', error);\n        throw error;\n    }\n}\n// Update video count\nasync function updateVideoCount(userId) {\n    try {\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.todayVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n            [FIELD_NAMES.lastVideoDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n        });\n    } catch (error) {\n        console.error('Error updating video count:', error);\n        throw error;\n    }\n}\n// Update wallet balance\nasync function updateWalletBalance(userId, amount) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.wallet]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(amount)\n        });\n    } catch (error) {\n        console.error('Error updating wallet balance:', error);\n        throw error;\n    }\n}\n// Save bank details\nasync function saveBankDetails(userId, bankDetails) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            throw new Error('Invalid userId provided');\n        }\n        // Validate bank details\n        validateBankDetails(bankDetails);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),\n            [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),\n            [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),\n            [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),\n            [FIELD_NAMES.bankDetailsUpdated]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        });\n        console.log('Bank details saved successfully for user:', userId);\n    } catch (error) {\n        console.error('Error saving bank details:', error);\n        throw error;\n    }\n}\n// Get bank details\nasync function getBankDetails(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getBankDetails:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Check if bank details exist\n            if (data[FIELD_NAMES.bankAccountNumber]) {\n                const result = {\n                    accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),\n                    accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),\n                    ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),\n                    bankName: String(data[FIELD_NAMES.bankName] || '')\n                };\n                console.log('getBankDetails result found');\n                return result;\n            }\n        }\n        console.log('No bank details found for user');\n        return null;\n    } catch (error) {\n        console.error('Error getting bank details:', error);\n        return null;\n    }\n}\n// Get plan-based earning amount (per batch of 50 videos)\nfunction getPlanEarning(plan) {\n    const planEarnings = {\n        'Trial': 10,\n        'Starter': 25,\n        'Basic': 75,\n        'Premium': 150,\n        'Gold': 200,\n        'Platinum': 250,\n        'Diamond': 400\n    };\n    return planEarnings[plan] || 10 // Default to trial earning (per batch of 50 videos)\n    ;\n}\n// Get plan-based video duration (in seconds)\nfunction getPlanVideoDuration(plan) {\n    const planDurations = {\n        'Trial': 30,\n        'Starter': 300,\n        'Basic': 300,\n        'Premium': 300,\n        'Gold': 180,\n        'Platinum': 120,\n        'Diamond': 60 // 1 minute (Rs 9999 plan)\n    };\n    return planDurations[plan] || 30 // Default to trial duration (30 seconds)\n    ;\n}\n// Get plan validity duration in days\nfunction getPlanValidityDays(plan) {\n    const planValidityDays = {\n        'Trial': 2,\n        'Starter': 30,\n        'Basic': 30,\n        'Premium': 30,\n        'Gold': 30,\n        'Platinum': 30,\n        'Diamond': 30,\n        '499': 30,\n        '1499': 30,\n        '2999': 30,\n        '3999': 30,\n        '5999': 30,\n        '9999': 30 // Legacy plan mapping\n    };\n    return planValidityDays[plan] || 2 // Default to trial duration (2 days)\n    ;\n}\n// Check if user's plan is expired based on active days and plan validity\nasync function isUserPlanExpired(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                expired: true,\n                reason: 'User data not found'\n            };\n        }\n        // If user is on Trial plan, check based on joined date\n        if (userData.plan === 'Trial') {\n            const joinedDate = userData.joinedDate || new Date();\n            const today = new Date();\n            const daysSinceJoined = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n            const trialDaysLeft = Math.max(0, 2 - daysSinceJoined);\n            return {\n                expired: trialDaysLeft <= 0,\n                reason: trialDaysLeft <= 0 ? 'Trial period expired' : undefined,\n                daysLeft: trialDaysLeft,\n                activeDays: daysSinceJoined\n            };\n        }\n        // For paid plans, check if planExpiry is set\n        if (userData.planExpiry) {\n            const today = new Date();\n            const expired = today > userData.planExpiry;\n            const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                expired,\n                reason: expired ? 'Plan subscription expired' : undefined,\n                daysLeft,\n                activeDays: userData.activeDays || 0\n            };\n        }\n        // If planExpiry is not set, calculate based on active days and plan validity\n        const planValidityDays = getPlanValidityDays(userData.plan);\n        const currentActiveDays = userData.activeDays || 0;\n        const daysLeft = Math.max(0, planValidityDays - currentActiveDays);\n        const expired = daysLeft <= 0;\n        return {\n            expired,\n            reason: expired ? `Plan validity period (${planValidityDays} days) exceeded based on active days` : undefined,\n            daysLeft,\n            activeDays: currentActiveDays\n        };\n    } catch (error) {\n        console.error('Error checking plan expiry:', error);\n        return {\n            expired: true,\n            reason: 'Error checking plan status'\n        };\n    }\n}\n// Update user's plan expiry when admin changes plan\nasync function updateUserPlanExpiry(userId, newPlan, customExpiryDate) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        if (newPlan === 'Trial') {\n            // Trial plan doesn't have expiry, it's based on joined date\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: null\n            });\n        } else {\n            // Set expiry date for paid plans\n            let expiryDate;\n            if (customExpiryDate) {\n                expiryDate = customExpiryDate;\n            } else {\n                // Calculate expiry based on plan validity\n                const validityDays = getPlanValidityDays(newPlan);\n                const today = new Date();\n                expiryDate = new Date(today.getTime() + validityDays * 24 * 60 * 60 * 1000);\n            }\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiryDate)\n            });\n            console.log(`Updated plan expiry for user ${userId} to ${expiryDate.toDateString()}`);\n        }\n    } catch (error) {\n        console.error('Error updating plan expiry:', error);\n        throw error;\n    }\n}\n// Get referral bonus based on plan\nfunction getReferralBonus(plan) {\n    const referralBonuses = {\n        'Trial': 0,\n        '499': 50,\n        '1499': 150,\n        '2999': 300,\n        '3999': 400,\n        '5999': 700,\n        '9999': 1200,\n        'Starter': 50,\n        'Basic': 150,\n        'Premium': 300,\n        'Gold': 400,\n        'Platinum': 700,\n        'Diamond': 1200\n    };\n    return referralBonuses[plan] || 0;\n}\n// Process referral bonus when admin upgrades user from Trial to paid plan\nasync function processReferralBonus(userId, oldPlan, newPlan) {\n    try {\n        // Only process bonus when upgrading FROM Trial TO a paid plan\n        if (oldPlan !== 'Trial' || newPlan === 'Trial') {\n            console.log('Referral bonus only applies when upgrading from Trial to paid plan');\n            return;\n        }\n        console.log(`Processing referral bonus for user ${userId} upgrading from ${oldPlan} to ${newPlan}`);\n        // Get the user's data to find their referral info\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (!userDoc.exists()) {\n            console.log('User not found');\n            return;\n        }\n        const userData = userDoc.data();\n        const referredBy = userData[FIELD_NAMES.referredBy];\n        const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited];\n        if (!referredBy) {\n            console.log('User was not referred by anyone, skipping bonus processing');\n            return;\n        }\n        if (alreadyCredited) {\n            console.log('Referral bonus already credited for this user, skipping');\n            return;\n        }\n        console.log('Finding referrer with code:', referredBy);\n        // Find the referrer by referral code\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', referredBy), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        if (querySnapshot.empty) {\n            console.log('Referral code not found:', referredBy);\n            return;\n        }\n        const referrerDoc = querySnapshot.docs[0];\n        const referrerId = referrerDoc.id;\n        const bonusAmount = getReferralBonus(newPlan);\n        console.log(`Found referrer: ${referrerId}, bonus amount: ₹${bonusAmount}`);\n        if (bonusAmount > 0) {\n            // Add bonus to referrer's wallet\n            await updateWalletBalance(referrerId, bonusAmount);\n            // Add 50 videos to referrer's total video count\n            const referrerRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, referrerId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(referrerRef, {\n                [FIELD_NAMES.totalVideos]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(50)\n            });\n            // Mark referral bonus as credited for this user\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.referralBonusCredited]: true\n            });\n            // Add transaction record for referral bonus\n            await addTransaction(referrerId, {\n                type: 'referral_bonus',\n                amount: bonusAmount,\n                description: `Referral bonus for ${newPlan} plan upgrade + 50 bonus videos (User: ${userData[FIELD_NAMES.name]})`\n            });\n            console.log(`✅ Referral bonus processed: ₹${bonusAmount} + 50 videos for referrer ${referrerId}`);\n        } else {\n            console.log('No bonus amount calculated, skipping');\n        }\n    } catch (error) {\n        console.error('❌ Error processing referral bonus:', error);\n    // Don't throw error to avoid breaking plan update\n    }\n}\n// Get user video settings (duration and earning per batch)\nasync function getUserVideoSettings(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                videoDuration: 30,\n                earningPerBatch: 10,\n                plan: 'Trial',\n                hasQuickAdvantage: false\n            };\n        }\n        // Check if user has active quick video advantage\n        const hasActiveQuickAdvantage = checkQuickVideoAdvantageActive(userData);\n        let videoDuration = userData.videoDuration;\n        // If user has active quick video advantage, use custom seconds or default to 30\n        if (hasActiveQuickAdvantage) {\n            videoDuration = userData.quickVideoAdvantageSeconds || 30 // Use custom duration or default to 30 seconds\n            ;\n        } else {\n            // Use plan-based video duration, but allow admin overrides for non-trial users\n            if (!videoDuration || userData.plan === 'Trial') {\n                videoDuration = getPlanVideoDuration(userData.plan);\n            }\n        }\n        return {\n            videoDuration: videoDuration,\n            earningPerBatch: getPlanEarning(userData.plan),\n            plan: userData.plan,\n            hasQuickAdvantage: hasActiveQuickAdvantage,\n            quickAdvantageExpiry: userData.quickVideoAdvantageExpiry\n        };\n    } catch (error) {\n        console.error('Error getting user video settings:', error);\n        return {\n            videoDuration: 30,\n            earningPerBatch: 10,\n            plan: 'Trial',\n            hasQuickAdvantage: false\n        };\n    }\n}\n// Check if user has active quick video advantage\nfunction checkQuickVideoAdvantageActive(userData) {\n    if (!userData.quickVideoAdvantage || !userData.quickVideoAdvantageExpiry) {\n        return false;\n    }\n    const now = new Date();\n    const expiry = userData.quickVideoAdvantageExpiry;\n    return now < expiry;\n}\n// Grant quick video advantage to user (admin function)\nasync function grantQuickVideoAdvantage(userId, days, grantedBy, seconds = 30) {\n    try {\n        if (days <= 0 || days > 365) {\n            throw new Error('Days must be between 1 and 365');\n        }\n        if (seconds < 1 || seconds > 420) {\n            throw new Error('Seconds must be between 1 and 420 (7 minutes)');\n        }\n        const now = new Date();\n        const expiry = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: true,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiry),\n            [FIELD_NAMES.quickVideoAdvantageDays]: days,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: seconds,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: grantedBy,\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(now)\n        });\n        console.log(`Granted quick video advantage to user ${userId} for ${days} days until ${expiry.toDateString()}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_granted',\n            amount: 0,\n            description: `Quick video advantage granted for ${days} days by ${grantedBy}`\n        });\n        return {\n            success: true,\n            expiry\n        };\n    } catch (error) {\n        console.error('Error granting quick video advantage:', error);\n        throw error;\n    }\n}\n// Remove quick video advantage from user (admin function)\nasync function removeQuickVideoAdvantage(userId, removedBy) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.quickVideoAdvantage]: false,\n            [FIELD_NAMES.quickVideoAdvantageExpiry]: null,\n            [FIELD_NAMES.quickVideoAdvantageDays]: 0,\n            [FIELD_NAMES.quickVideoAdvantageSeconds]: 30,\n            [FIELD_NAMES.quickVideoAdvantageGrantedBy]: '',\n            [FIELD_NAMES.quickVideoAdvantageGrantedAt]: null\n        });\n        console.log(`Removed quick video advantage from user ${userId}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_removed',\n            amount: 0,\n            description: `Quick video advantage removed by ${removedBy}`\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error removing quick video advantage:', error);\n        throw error;\n    }\n}\n// Update user video duration (admin function)\nasync function updateUserVideoDuration(userId, durationInSeconds) {\n    try {\n        // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)\n        const isQuickDuration = [\n            1,\n            10,\n            30\n        ].includes(durationInSeconds);\n        const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420;\n        if (!isQuickDuration && !isStandardDuration) {\n            throw new Error('Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration');\n        }\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.videoDuration]: durationInSeconds\n        });\n        console.log(`Updated video duration for user ${userId} to ${durationInSeconds} seconds`);\n    } catch (error) {\n        console.error('Error updating user video duration:', error);\n        throw error;\n    }\n}\n// Validate bank details\nfunction validateBankDetails(bankDetails) {\n    const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails;\n    if (!accountHolderName || accountHolderName.trim().length < 2) {\n        throw new Error('Account holder name must be at least 2 characters long');\n    }\n    if (!accountNumber || !/^\\d{9,18}$/.test(accountNumber.trim())) {\n        throw new Error('Account number must be 9-18 digits');\n    }\n    if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {\n        throw new Error('Invalid IFSC code format (e.g., SBIN0001234)');\n    }\n    if (!bankName || bankName.trim().length < 2) {\n        throw new Error('Bank name must be at least 2 characters long');\n    }\n}\n// Add notification (admin function) - All notifications are now blocking\nasync function addNotification(notification) {\n    try {\n        const notificationData = {\n            title: notification.title,\n            message: notification.message,\n            type: notification.type,\n            targetUsers: notification.targetUsers,\n            userIds: notification.userIds || [],\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdBy: notification.createdBy\n        };\n        console.log('Adding notification to Firestore:', notificationData);\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), notificationData);\n        console.log('Notification added successfully with ID:', docRef.id);\n        // Verify the notification was added\n        const addedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n        if (addedDoc.exists()) {\n            console.log('Notification verified in database:', addedDoc.data());\n        } else {\n            console.warn('Notification not found after adding');\n        }\n        return docRef.id;\n    } catch (error) {\n        console.error('Error adding notification:', error);\n        throw error;\n    }\n}\n// Get notifications for a user\nasync function getUserNotifications(userId, limitCount = 20) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading notifications for user: ${userId}`);\n        // Try to get notifications with fallback for indexing issues\n        let allUsersSnapshot, specificUserSnapshot;\n        try {\n            // Get notifications targeted to all users\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n            console.log(`Found ${allUsersSnapshot.docs.length} notifications for all users`);\n        } catch (error) {\n            console.warn('Error querying all users notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n        }\n        try {\n            // Get notifications targeted to specific user\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n            console.log(`Found ${specificUserSnapshot.docs.length} notifications for specific user`);\n        } catch (error) {\n            console.warn('Error querying specific user notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n        }\n        const notifications = [];\n        // Process all users notifications\n        allUsersSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Process specific user notifications\n        specificUserSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Sort by creation date (newest first)\n        notifications.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        const finalNotifications = notifications.slice(0, limitCount);\n        console.log(`Returning ${finalNotifications.length} total notifications for user`);\n        return finalNotifications;\n    } catch (error) {\n        console.error('Error getting user notifications:', error);\n        return [];\n    }\n}\n// Get all notifications (admin function)\nasync function getAllNotifications(limitCount = 50) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const notifications = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            }));\n        return notifications;\n    } catch (error) {\n        console.error('Error getting all notifications:', error);\n        return [];\n    }\n}\n// Delete notification (admin function)\nasync function deleteNotification(notificationId) {\n    try {\n        if (!notificationId || typeof notificationId !== 'string') {\n            throw new Error('Invalid notification ID provided');\n        }\n        console.log('Deleting notification:', notificationId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications, notificationId));\n        console.log('Notification deleted successfully');\n    } catch (error) {\n        console.error('Error deleting notification:', error);\n        throw error;\n    }\n}\n// Mark notification as read\nasync function markNotificationAsRead(notificationId, userId) {\n    try {\n        // For now, we'll store read status in localStorage since it's user-specific\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        if (!readNotifications.includes(notificationId)) {\n            readNotifications.push(notificationId);\n            localStorage.setItem(`read_notifications_${userId}`, JSON.stringify(readNotifications));\n        }\n    } catch (error) {\n        console.error('Error marking notification as read:', error);\n    }\n}\n// Check if notification is read\nfunction isNotificationRead(notificationId, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return readNotifications.includes(notificationId);\n    } catch (error) {\n        console.error('Error checking notification read status:', error);\n        return false;\n    }\n}\n// Get unread notification count\nfunction getUnreadNotificationCount(notifications, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return notifications.filter((notification)=>!readNotifications.includes(notification.id)).length;\n    } catch (error) {\n        console.error('Error getting unread notification count:', error);\n        return 0;\n    }\n}\n// Get unread notifications - All notifications are now blocking\nasync function getUnreadNotifications(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUnreadNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading unread notifications for user: ${userId}`);\n        // Get all notifications for the user\n        const allNotifications = await getUserNotifications(userId, 50);\n        // Filter for unread notifications\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        const unreadNotifications = allNotifications.filter((notification)=>notification.id && !readNotifications.includes(notification.id));\n        console.log(`Found ${unreadNotifications.length} unread notifications`);\n        return unreadNotifications;\n    } catch (error) {\n        console.error('Error getting unread notifications:', error);\n        return [];\n    }\n}\n// Check if user has unread notifications\nasync function hasUnreadNotifications(userId) {\n    try {\n        const unreadNotifications = await getUnreadNotifications(userId);\n        return unreadNotifications.length > 0;\n    } catch (error) {\n        console.error('Error checking for unread notifications:', error);\n        return false;\n    }\n}\n// Check if user has pending withdrawals\nasync function hasPendingWithdrawals(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking pending withdrawals:', error);\n        return false;\n    }\n}\n// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)\nasync function checkWithdrawalAllowed(userId) {\n    try {\n        // Check user plan first\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                allowed: false,\n                reason: 'Unable to verify user information. Please try again.'\n            };\n        }\n        // Check if user is on trial plan\n        if (userData.plan === 'Trial') {\n            return {\n                allowed: false,\n                reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'\n            };\n        }\n        // Check if user has pending withdrawals\n        const hasPending = await hasPendingWithdrawals(userId);\n        if (hasPending) {\n            return {\n                allowed: false,\n                reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'\n            };\n        }\n        const now = new Date();\n        const currentHour = now.getHours();\n        // Check time restrictions (10 AM to 6 PM)\n        if (currentHour < 10 || currentHour >= 18) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'\n            };\n        }\n        // Check admin leave day\n        const { isAdminLeaveDay } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(now);\n        if (isAdminLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on admin leave/holiday days'\n            };\n        }\n        // Check user leave day\n        const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isUserLeave = await isUserOnLeave(userId, now);\n        if (isUserLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on your leave days'\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking withdrawal allowed:', error);\n        return {\n            allowed: false,\n            reason: 'Unable to verify withdrawal eligibility. Please try again.'\n        };\n    }\n}\n// Create withdrawal request\nasync function createWithdrawalRequest(userId, amount, bankDetails) {\n    try {\n        // Check minimum withdrawal amount\n        if (amount < 50) {\n            throw new Error('Minimum withdrawal amount is ₹50');\n        }\n        // Check if withdrawal is allowed\n        const withdrawalCheck = await checkWithdrawalAllowed(userId);\n        if (!withdrawalCheck.allowed) {\n            throw new Error(withdrawalCheck.reason);\n        }\n        // Check if user has sufficient balance\n        const walletData = await getWalletData(userId);\n        if (walletData.wallet < amount) {\n            throw new Error('Insufficient wallet balance');\n        }\n        // Debit the amount from user's wallet immediately\n        await updateWalletBalance(userId, -amount);\n        // Add transaction record for withdrawal debit\n        await addTransaction(userId, {\n            type: 'withdrawal_request',\n            amount: -amount,\n            description: `Withdrawal request submitted - ₹${amount} debited from wallet`\n        });\n        const withdrawalData = {\n            userId,\n            amount,\n            bankDetails,\n            status: 'pending',\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), withdrawalData);\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating withdrawal request:', error);\n        throw error;\n    }\n}\n// Get user withdrawals\nasync function getUserWithdrawals(userId, limitCount = 20) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting user withdrawals:', error);\n        return [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/dataService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ\",\n    authDomain: \"mytube-india.firebaseapp.com\",\n    projectId: \"mytube-india\",\n    storageBucket: \"mytube-india.firebasestorage.app\",\n    messagingSenderId: \"************\",\n    appId: \"1:************:web:ebedaec6a492926af2056a\",\n    measurementId: \"G-R24C6N7CWJ\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)() : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/sweetalert2","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();