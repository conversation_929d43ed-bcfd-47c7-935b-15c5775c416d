"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/register/page",{

/***/ "(app-pages-browser)/./src/app/register/page.tsx":
/*!***********************************!*\
  !*** ./src/app/register/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sweetalert2 */ \"(app-pages-browser)/./node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction RegisterPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__.useAuthState)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        mobile: '',\n        password: '',\n        confirmPassword: '',\n        referralCode: ''\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            if (user && !loading) {\n                window.location.href = '/dashboard';\n            }\n        }\n    }[\"RegisterPage.useEffect\"], [\n        user,\n        loading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            // Get referral code from URL if present\n            const urlParams = new URLSearchParams(window.location.search);\n            const refCode = urlParams.get('ref');\n            if (refCode) {\n                setFormData({\n                    \"RegisterPage.useEffect\": (prev)=>({\n                            ...prev,\n                            referralCode: refCode\n                        })\n                }[\"RegisterPage.useEffect\"]);\n            }\n        }\n    }[\"RegisterPage.useEffect\"], []);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    // Referral code generation is handled by generateUniqueReferralCode function\n    const validateForm = ()=>{\n        const { name, email, mobile, password, confirmPassword } = formData;\n        if (!name || !email || !mobile || !password || !confirmPassword) {\n            throw new Error('Please fill in all required fields');\n        }\n        if (name.length < 2) {\n            throw new Error('Name must be at least 2 characters long');\n        }\n        if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n            throw new Error('Please enter a valid email address');\n        }\n        if (!/^[6-9]\\d{9}$/.test(mobile)) {\n            throw new Error('Please enter a valid 10-digit mobile number');\n        }\n        if (password.length < 6) {\n            throw new Error('Password must be at least 6 characters long');\n        }\n        if (password !== confirmPassword) {\n            throw new Error('Passwords do not match');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            validateForm();\n            setIsLoading(true);\n            // Create user account first - Firebase Auth will handle email uniqueness\n            // Mobile number uniqueness will be checked after user creation\n            console.log('Creating user with email and password...');\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_4__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.auth, formData.email, formData.password);\n            const user = userCredential.user;\n            console.log('Firebase Auth user created successfully:', user.uid);\n            console.log('Generating referral code...');\n            // Use improved referral code generation with better uniqueness\n            const userReferralCode = generateSimpleReferralCode();\n            console.log('Generated referral code:', userReferralCode);\n            // Note: Mobile number uniqueness is handled by the admin panel\n            // Firebase Auth already ensures email uniqueness\n            // Create user document in Firestore with all required fields\n            const userData = {\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.name]: formData.name.trim(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.email]: formData.email.toLowerCase(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.mobile]: formData.mobile,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referralCode]: userReferralCode,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referredBy]: formData.referralCode || '',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referralBonusCredited]: false,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.plan]: 'Trial',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.planExpiry]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.activeDays]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.joinedDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.Timestamp.now(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.wallet]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.totalVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.todayVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.lastVideoDate]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.videoDuration]: 30,\n                status: 'active'\n            };\n            console.log('Creating user document with data:', userData);\n            console.log('User UID:', user.uid);\n            console.log('Collection:', _lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users);\n            console.log('Document path:', \"\".concat(_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users, \"/\").concat(user.uid));\n            // Create user document in Firestore\n            console.log('Creating user document in Firestore...');\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, _lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users, user.uid);\n            console.log('Document reference created:', userDocRef.path);\n            console.log('About to create document with data:', JSON.stringify(userData, null, 2));\n            try {\n                console.log('Attempting to create document...');\n                console.log('User UID:', user.uid);\n                console.log('Document path:', userDocRef.path);\n                console.log('Auth user email:', user.email);\n                console.log('Auth user verified:', user.emailVerified);\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.setDoc)(userDocRef, userData);\n                console.log('✅ User document created successfully');\n                // Verify the document was created\n                const verifyDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.getDoc)(userDocRef);\n                if (verifyDoc.exists()) {\n                    console.log('✅ Document verification successful:', verifyDoc.data());\n                } else {\n                    console.error('❌ Document was not created properly');\n                    throw new Error('User document was not created properly');\n                }\n            } catch (firestoreError) {\n                console.error('❌ Firestore setDoc failed:', firestoreError);\n                console.error('❌ Firestore error code:', firestoreError.code);\n                console.error('❌ Firestore error message:', firestoreError.message);\n                console.error('❌ Full error object:', JSON.stringify(firestoreError, null, 2));\n                throw new Error(\"Failed to create user profile: \".concat(firestoreError.message));\n            }\n            // Note: Referral bonus will be credited when admin upgrades user from Trial to paid plan\n            console.log('User registered successfully. Referral bonus will be processed when upgraded to paid plan.');\n            // Show success message\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'success',\n                title: 'Registration Successful!',\n                text: 'Your account has been created successfully. Welcome to MyTube!',\n                timer: 3000,\n                showConfirmButton: false\n            });\n        // Redirect will be handled by useEffect\n        } catch (error) {\n            console.error('Registration error:', error);\n            console.error('Error code:', error.code);\n            console.error('Error message:', error.message);\n            console.error('Full error object:', JSON.stringify(error, null, 2));\n            let message = 'An error occurred during registration';\n            if (error.message.includes('fill in all')) {\n                message = error.message;\n            } else if (error.message.includes('Name must be')) {\n                message = error.message;\n            } else if (error.message.includes('valid email')) {\n                message = error.message;\n            } else if (error.message.includes('valid 10-digit')) {\n                message = error.message;\n            } else if (error.message.includes('Password must be')) {\n                message = error.message;\n            } else if (error.message.includes('Passwords do not match')) {\n                message = error.message;\n            } else if (error.message.includes('email address is already registered')) {\n                message = error.message;\n            } else if (error.message.includes('mobile number is already registered')) {\n                message = error.message;\n            } else {\n                switch(error.code){\n                    case 'auth/email-already-in-use':\n                        message = 'An account with this email already exists';\n                        break;\n                    case 'auth/invalid-email':\n                        message = 'Invalid email address';\n                        break;\n                    case 'auth/weak-password':\n                        message = 'Password is too weak';\n                        break;\n                    default:\n                        message = error.message || 'Registration failed';\n                }\n            }\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Registration Failed',\n                text: message\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass-card w-full max-w-md p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/img/mytube-logo.svg\",\n                                    alt: \"MyTube Logo\",\n                                    width: 50,\n                                    height: 50,\n                                    className: \"mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"MyTube\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-white mb-2\",\n                            children: \"Create Account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80\",\n                            children: \"Join MyTube and start earning today\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"name\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Full Name *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"name\",\n                                    name: \"name\",\n                                    value: formData.name,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter your full name\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"email\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Email Address *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    id: \"email\",\n                                    name: \"email\",\n                                    value: formData.email,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter your email\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"mobile\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Mobile Number *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"tel\",\n                                    id: \"mobile\",\n                                    name: \"mobile\",\n                                    value: formData.mobile,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter 10-digit mobile number\",\n                                    maxLength: 10,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"password\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Password *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            id: \"password\",\n                                            name: \"password\",\n                                            value: formData.password,\n                                            onChange: handleInputChange,\n                                            className: \"form-input pr-12\",\n                                            placeholder: \"Enter password (min 6 characters)\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"password-toggle-btn\",\n                                            \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas \".concat(showPassword ? 'fa-eye-slash' : 'fa-eye')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmPassword\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Confirm Password *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            value: formData.confirmPassword,\n                                            onChange: handleInputChange,\n                                            className: \"form-input pr-12\",\n                                            placeholder: \"Confirm your password\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            className: \"password-toggle-btn\",\n                                            \"aria-label\": showConfirmPassword ? \"Hide confirm password\" : \"Show confirm password\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas \".concat(showConfirmPassword ? 'fa-eye-slash' : 'fa-eye')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"referralCode\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Referral Code (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"referralCode\",\n                                    name: \"referralCode\",\n                                    value: formData.referralCode,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter referral code if you have one\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"w-full btn-primary flex items-center justify-center mt-6\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner mr-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Creating Account...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-user-plus mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Create Account\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/60\",\n                        children: [\n                            \"Already have an account?\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"text-white font-semibold hover:underline\",\n                                children: \"Sign in here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-white/80 hover:text-white transition-colors inline-flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-arrow-left mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Home\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n_s(RegisterPage, \"dJ1wC6bb5H+msBVAe0YvL0R8f8k=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__.useAuthState\n    ];\n});\n_c = RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/register/page.tsx\n"));

/***/ })

});