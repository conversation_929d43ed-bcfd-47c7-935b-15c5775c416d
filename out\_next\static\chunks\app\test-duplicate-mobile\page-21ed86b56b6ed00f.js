(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1844],{2562:(e,a,t)=>{Promise.resolve().then(t.bind(t,4886))},4886:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>c});var i=t(5155),s=t(2115),l=t(5317),n=t(6104),r=t(3592);function c(){let[e,a]=(0,s.useState)(""),[t,c]=(0,s.useState)(!1),[o,u]=(0,s.useState)("9876543210"),m=e=>{a(a=>a+e+"\n")},b=async()=>{a(""),c(!0);try{m("\uD83D\uDD0D Testing Duplicate Mobile Number Policy...\n"),m("=== TEST 1: Checking Existing Duplicate Mobile Numbers ===");let e=(0,l.rJ)(n.db,r.COLLECTIONS.users),a=(0,l.P)(e),t=await (0,l.GG)(a),i=new Map,s=new Set;t.docs.forEach(e=>{let a=e.data()[r.FIELD_NAMES.mobile];if(a&&"N/A"!==a){let e=i.get(a)||0;i.set(a,e+1),e>0&&s.add(a)}}),m("Total users: ".concat(t.docs.length)),m("Unique mobile numbers: ".concat(i.size)),m("Duplicate mobile numbers found: ".concat(s.size)),s.size>0?(m("✅ DUPLICATE MOBILES DETECTED (This is ALLOWED):"),Array.from(s).slice(0,5).forEach(e=>{let a=i.get(e);m("   - ".concat(e," (").concat(a," users)"))})):m("ℹ️ No duplicate mobile numbers found yet"),m("\n=== TEST 2: Checking Specific Mobile Number ==="),m("Testing mobile: ".concat(o));let c=(0,l.P)(e,(0,l._M)(r.FIELD_NAMES.mobile,"==",o)),u=await (0,l.GG)(c);m("Users with mobile ".concat(o,": ").concat(u.docs.length)),u.docs.length>0?(m("✅ Mobile number ".concat(o," is used by ").concat(u.docs.length," user(s)")),u.docs.forEach((e,a)=>{let t=e.data();m("   ".concat(a+1,". ").concat(t[r.FIELD_NAMES.name]," (").concat(t[r.FIELD_NAMES.email],")"))})):m("ℹ️ Mobile number ".concat(o," is not used by any user")),m("\n=== TEST 3: Registration Policy Summary ==="),m("\uD83D\uDCCB Current Registration Rules:"),m("   ✅ Email: MUST be unique (enforced by Firebase Auth)"),m("   ✅ Mobile: CAN be duplicate (multiple users allowed)"),m("   ✅ Referral Code: MUST be unique (enforced by generation logic)"),m(""),m("\uD83D\uDCDD Business Logic:"),m("   - Family members can share mobile numbers"),m("   - Multiple accounts per mobile are allowed"),m("   - Email remains the primary unique identifier"),m("   - Each user gets unique referral code (MYN format)"),m("\n=== TEST 4: Registration Validation Check ==="),m("✅ Mobile number format validation: 10-digit Indian mobile"),m("✅ Mobile number duplicate check: DISABLED (allows duplicates)"),m("✅ Email duplicate check: ENABLED (Firebase Auth handles this)"),m("✅ Referral code generation: UNIQUE sequential (MYN0001, MYN0002...)"),m("\n\uD83C\uDFAF CONCLUSION:"),m("The registration system correctly allows duplicate mobile numbers"),m("while maintaining email uniqueness. This supports the business"),m("requirement for family members to share mobile numbers.")}catch(e){m("❌ Test failed: ".concat(e.message)),m("Error code: ".concat(e.code))}finally{c(!1)}};return(0,i.jsx)("div",{className:"min-h-screen p-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Duplicate Mobile Number Test"}),(0,i.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("label",{className:"block text-white font-medium mb-2",children:"Test Mobile Number"}),(0,i.jsx)("input",{type:"tel",value:o,onChange:e=>u(e.target.value),placeholder:"Enter mobile number to test",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",maxLength:10})]}),(0,i.jsx)("button",{onClick:b,disabled:t,className:"btn-primary mb-4",children:t?"Testing Duplicate Mobile Policy...":"Test Duplicate Mobile Numbers"}),(0,i.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,i.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||'Click "Test Duplicate Mobile Numbers" to start...'})})]}),(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},6104:(e,a,t)=>{"use strict";t.d(a,{db:()=>o,j2:()=>c});var i=t(3915),s=t(3004),l=t(5317),n=t(858);let r=(0,i.Dk)().length?(0,i.Sx)():(0,i.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,s.xI)(r),o=(0,l.aU)(r);(0,n.c7)(r)}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,5181,3592,8441,1684,7358],()=>a(2562)),_N_E=e.O()}]);