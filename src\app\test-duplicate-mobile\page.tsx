'use client'

import { useState } from 'react'
import { collection, query, where, getDocs } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS } from '@/lib/dataService'

export default function TestDuplicateMobilePage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [testMobile, setTestMobile] = useState('9876543210')

  const addToResult = (text: string) => {
    setResult(prev => prev + text + '\n')
  }

  const testDuplicateMobileNumbers = async () => {
    setResult('')
    setIsLoading(true)
    
    try {
      addToResult('🔍 Testing Duplicate Mobile Number Policy...\n')
      
      // Test 1: Check for existing duplicate mobile numbers
      addToResult('=== TEST 1: Checking Existing Duplicate Mobile Numbers ===')
      const usersRef = collection(db, COLLECTIONS.users)
      const allUsersQuery = query(usersRef)
      const allUsersSnapshot = await getDocs(allUsersQuery)
      
      const mobileNumbers = new Map<string, number>()
      const duplicateMobiles = new Set<string>()
      
      allUsersSnapshot.docs.forEach(doc => {
        const data = doc.data()
        const mobile = data[FIELD_NAMES.mobile]
        if (mobile && mobile !== 'N/A') {
          const count = mobileNumbers.get(mobile) || 0
          mobileNumbers.set(mobile, count + 1)
          if (count > 0) {
            duplicateMobiles.add(mobile)
          }
        }
      })
      
      addToResult(`Total users: ${allUsersSnapshot.docs.length}`)
      addToResult(`Unique mobile numbers: ${mobileNumbers.size}`)
      addToResult(`Duplicate mobile numbers found: ${duplicateMobiles.size}`)
      
      if (duplicateMobiles.size > 0) {
        addToResult(`✅ DUPLICATE MOBILES DETECTED (This is ALLOWED):`)
        Array.from(duplicateMobiles).slice(0, 5).forEach(mobile => {
          const count = mobileNumbers.get(mobile)
          addToResult(`   - ${mobile} (${count} users)`)
        })
      } else {
        addToResult(`ℹ️ No duplicate mobile numbers found yet`)
      }
      
      // Test 2: Check specific mobile number
      addToResult(`\n=== TEST 2: Checking Specific Mobile Number ===`)
      addToResult(`Testing mobile: ${testMobile}`)
      
      const mobileQuery = query(
        usersRef,
        where(FIELD_NAMES.mobile, '==', testMobile)
      )
      const mobileSnapshot = await getDocs(mobileQuery)
      
      addToResult(`Users with mobile ${testMobile}: ${mobileSnapshot.docs.length}`)
      
      if (mobileSnapshot.docs.length > 0) {
        addToResult(`✅ Mobile number ${testMobile} is used by ${mobileSnapshot.docs.length} user(s)`)
        mobileSnapshot.docs.forEach((doc, index) => {
          const userData = doc.data()
          addToResult(`   ${index + 1}. ${userData[FIELD_NAMES.name]} (${userData[FIELD_NAMES.email]})`)
        })
      } else {
        addToResult(`ℹ️ Mobile number ${testMobile} is not used by any user`)
      }
      
      // Test 3: Registration Policy Summary
      addToResult('\n=== TEST 3: Registration Policy Summary ===')
      addToResult('📋 Current Registration Rules:')
      addToResult('   ✅ Email: MUST be unique (enforced by Firebase Auth)')
      addToResult('   ✅ Mobile: CAN be duplicate (multiple users allowed)')
      addToResult('   ✅ Referral Code: MUST be unique (enforced by generation logic)')
      addToResult('')
      addToResult('📝 Business Logic:')
      addToResult('   - Family members can share mobile numbers')
      addToResult('   - Multiple accounts per mobile are allowed')
      addToResult('   - Email remains the primary unique identifier')
      addToResult('   - Each user gets unique referral code (MYN format)')
      
      // Test 4: Validation Check
      addToResult('\n=== TEST 4: Registration Validation Check ===')
      addToResult('✅ Mobile number format validation: 10-digit Indian mobile')
      addToResult('✅ Mobile number duplicate check: DISABLED (allows duplicates)')
      addToResult('✅ Email duplicate check: ENABLED (Firebase Auth handles this)')
      addToResult('✅ Referral code generation: UNIQUE sequential (MYN0001, MYN0002...)')
      
      addToResult('\n🎯 CONCLUSION:')
      addToResult('The registration system correctly allows duplicate mobile numbers')
      addToResult('while maintaining email uniqueness. This supports the business')
      addToResult('requirement for family members to share mobile numbers.')
      
    } catch (error: any) {
      addToResult(`❌ Test failed: ${error.message}`)
      addToResult(`Error code: ${error.code}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Duplicate Mobile Number Test</h1>
        
        <div className="glass-card p-6 mb-6">
          <div className="mb-4">
            <label className="block text-white font-medium mb-2">Test Mobile Number</label>
            <input
              type="tel"
              value={testMobile}
              onChange={(e) => setTestMobile(e.target.value)}
              placeholder="Enter mobile number to test"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              maxLength={10}
            />
          </div>
          
          <button
            onClick={testDuplicateMobileNumbers}
            disabled={isLoading}
            className="btn-primary mb-4"
          >
            {isLoading ? 'Testing Duplicate Mobile Policy...' : 'Test Duplicate Mobile Numbers'}
          </button>
          
          <div className="bg-black/50 p-4 rounded-lg">
            <pre className="text-white text-sm whitespace-pre-wrap overflow-auto max-h-96">
              {result || 'Click "Test Duplicate Mobile Numbers" to start...'}
            </pre>
          </div>
        </div>
        
        <div className="text-center">
          <a href="/register" className="text-blue-400 hover:text-blue-300 underline">
            ← Back to Registration
          </a>
        </div>
      </div>
    </div>
  )
}
