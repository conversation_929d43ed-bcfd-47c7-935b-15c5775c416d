(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2],{6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>d,j2:()=>o});var r=a(3915),n=a(3004),s=a(5317),c=a(858);let i=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,n.xI)(i),d=(0,s.aU)(i);(0,c.c7)(i)},6136:(e,t,a)=>{Promise.resolve().then(a.bind(a,7132))},7132:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(5155),n=a(2115),s=a(5317),c=a(6104),i=a(3592);function o(){let[e,t]=(0,n.useState)(""),[a,o]=(0,n.useState)(!1),d=e=>{t(t=>t+e+"\n")},l=async()=>{t(""),o(!0);try{d("\uD83D\uDD0D Testing Sequential Referral Code Generation...\n"),d("=== TEST 1: Current Database State ===");let e=(0,s.rJ)(c.db,i.COLLECTIONS.users),t=(await (0,s.d_)(e)).data().count;d("Total users in database: ".concat(t));let a=(0,s.P)(e,(0,s.My)(i.FIELD_NAMES.referralCode,"desc"),(0,s.AB)(5)),r=await (0,s.GG)(a);d("\nTop 5 existing referral codes:"),r.docs.forEach((e,t)=>{let a=e.data()[i.FIELD_NAMES.referralCode];d("".concat(t+1,". ").concat(a))});let n=0;r.docs.forEach(e=>{let t=e.data()[i.FIELD_NAMES.referralCode];if(t&&t.startsWith("MYN")){let e=parseInt(t.substring(3));!isNaN(e)&&e>n&&(n=e)}}),d("\nHighest MYN number found: ".concat(n)),d("Expected next MYN code: MYN".concat(String(n+1).padStart(4,"0"))),d("\n=== TEST 2: Generate Sequential Codes ===");for(let e=1;e<=5;e++){try{d("\nGenerating code ".concat(e,"..."));let t=await (0,i.x4)();if(d("✅ Generated: ".concat(t)),t.startsWith("MYN")){let e=t.substring(3);/^\d{4}$/.test(e)?(d("   ✅ Format correct: MYN + 4 digits"),d("   ✅ Number: ".concat(parseInt(e)))):d('   ❌ Format incorrect: Expected 4 digits, got "'.concat(e,'"'))}else d('   ❌ Prefix incorrect: Expected "MYN", got "'.concat(t.substring(0,3),'"'))}catch(t){d("❌ Generation ".concat(e," failed: ").concat(t.message))}await new Promise(e=>setTimeout(e,100))}d("\n=== TEST 3: Verify Sequential Order ===");let o=(0,s.P)(e,(0,s.My)(i.FIELD_NAMES.referralCode,"desc"),(0,s.AB)(10)),l=await (0,s.GG)(o),m=[];l.docs.forEach(e=>{let t=e.data()[i.FIELD_NAMES.referralCode];if(t&&t.startsWith("MYN")){let e=parseInt(t.substring(3));isNaN(e)||m.push(e)}}),m.sort((e,t)=>t-e),d("Recent MYN codes (descending order):"),m.slice(0,10).forEach((e,t)=>{d("".concat(t+1,". MYN").concat(String(e).padStart(4,"0")))});let u=!0;for(let e=1;e<Math.min(m.length,5);e++)if(m[e-1]-m[e]!=1){u=!1;break}d("\nSequential order check: ".concat(u?"✅ PASS":"❌ FAIL")),d("\n=== TEST 4: Performance Test ===");let f=Date.now();try{let e=await (0,i.x4)(),t=Date.now()-f;d("✅ Performance test passed"),d("   Generated: ".concat(e)),d("   Time taken: ".concat(t,"ms")),t<1e3?d("   ✅ Performance: GOOD (< 1 second)"):t<3e3?d("   ⚠️ Performance: ACCEPTABLE (1-3 seconds)"):d("   ❌ Performance: SLOW (> 3 seconds)")}catch(e){d("❌ Performance test failed: ".concat(e.message))}d("\n\uD83C\uDFAF CONCLUSION:"),d("Sequential referral code generation test completed."),d("Check the results above to verify proper MYN sequential numbering.")}catch(e){d("❌ Test failed: ".concat(e.message)),d("Error code: ".concat(e.code))}finally{o(!1)}};return(0,r.jsx)("div",{className:"min-h-screen p-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Sequential Referral Code Test"}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsx)("button",{onClick:l,disabled:a,className:"btn-primary mb-4",children:a?"Testing Sequential Generation...":"Test Sequential Code Generation"}),(0,r.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,r.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||'Click "Test Sequential Code Generation" to start...'})})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,3592,8441,1684,7358],()=>t(6136)),_N_E=e.O()}]);