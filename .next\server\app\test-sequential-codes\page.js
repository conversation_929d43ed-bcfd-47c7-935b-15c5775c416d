(()=>{var e={};e.id=2,e.ids=[2],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29458:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),i=r(43210),o=r(75535),n=r(33784),a=r(3582);function d(){let[e,t]=(0,i.useState)(""),[r,d]=(0,i.useState)(!1),u=e=>{t(t=>t+e+"\n")},c=async()=>{t(""),d(!0);try{u("\uD83D\uDD0D Testing Sequential Referral Code Generation...\n"),u("=== TEST 1: Current Database State ===");let e=(0,o.rJ)(n.db,a.COLLECTIONS.users),t=(await (0,o.d_)(e)).data().count;u(`Total users in database: ${t}`);let r=(0,o.P)(e,(0,o.My)(a.FIELD_NAMES.referralCode,"desc"),(0,o.AB)(5)),s=await (0,o.GG)(r);u(`
Top 5 existing referral codes:`),s.docs.forEach((e,t)=>{let r=e.data()[a.FIELD_NAMES.referralCode];u(`${t+1}. ${r}`)});let i=0;s.docs.forEach(e=>{let t=e.data()[a.FIELD_NAMES.referralCode];if(t&&t.startsWith("MYN")){let e=parseInt(t.substring(3));!isNaN(e)&&e>i&&(i=e)}}),u(`
Highest MYN number found: ${i}`),u(`Expected next MYN code: MYN${String(i+1).padStart(4,"0")}`),u("\n=== TEST 2: Generate Sequential Codes ===");for(let e=1;e<=5;e++){try{u(`
Generating code ${e}...`);let t=await (0,a.x4)();if(u(`✅ Generated: ${t}`),t.startsWith("MYN")){let e=t.substring(3);/^\d{4}$/.test(e)?(u(`   ✅ Format correct: MYN + 4 digits`),u(`   ✅ Number: ${parseInt(e)}`)):u(`   ❌ Format incorrect: Expected 4 digits, got "${e}"`)}else u(`   ❌ Prefix incorrect: Expected "MYN", got "${t.substring(0,3)}"`)}catch(t){u(`❌ Generation ${e} failed: ${t.message}`)}await new Promise(e=>setTimeout(e,100))}u("\n=== TEST 3: Verify Sequential Order ===");let d=(0,o.P)(e,(0,o.My)(a.FIELD_NAMES.referralCode,"desc"),(0,o.AB)(10)),c=await (0,o.GG)(d),l=[];c.docs.forEach(e=>{let t=e.data()[a.FIELD_NAMES.referralCode];if(t&&t.startsWith("MYN")){let e=parseInt(t.substring(3));isNaN(e)||l.push(e)}}),l.sort((e,t)=>t-e),u("Recent MYN codes (descending order):"),l.slice(0,10).forEach((e,t)=>{u(`${t+1}. MYN${String(e).padStart(4,"0")}`)});let p=!0;for(let e=1;e<Math.min(l.length,5);e++)if(l[e-1]-l[e]!=1){p=!1;break}u(`
Sequential order check: ${p?"✅ PASS":"❌ FAIL"}`),u("\n=== TEST 4: Performance Test ===");let x=Date.now();try{let e=await (0,a.x4)(),t=Date.now()-x;u(`✅ Performance test passed`),u(`   Generated: ${e}`),u(`   Time taken: ${t}ms`),t<1e3?u(`   ✅ Performance: GOOD (< 1 second)`):t<3e3?u(`   ⚠️ Performance: ACCEPTABLE (1-3 seconds)`):u(`   ❌ Performance: SLOW (> 3 seconds)`)}catch(e){u(`❌ Performance test failed: ${e.message}`)}u("\n\uD83C\uDFAF CONCLUSION:"),u("Sequential referral code generation test completed."),u("Check the results above to verify proper MYN sequential numbering.")}catch(e){u(`❌ Test failed: ${e.message}`),u(`Error code: ${e.code}`)}finally{d(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Sequential Referral Code Test"}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("button",{onClick:c,disabled:r,className:"btn-primary mb-4",children:r?"Testing Sequential Generation...":"Test Sequential Code Generation"}),(0,s.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,s.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||'Click "Test Sequential Code Generation" to start...'})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>u,j2:()=>d});var s=r(67989),i=r(63385),o=r(75535),n=r(70146);let a=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),d=(0,i.xI)(a),u=(0,o.aU)(a);(0,n.c7)(a)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},40477:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>c,routeModule:()=>p,tree:()=>u});var s=r(65239),i=r(48088),o=r(88170),n=r.n(o),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let u={children:["",{children:["test-sequential-codes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58372)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-sequential-codes\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-sequential-codes\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-sequential-codes/page",pathname:"/test-sequential-codes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58372:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-sequential-codes\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-sequential-codes\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82512:(e,t,r)=>{Promise.resolve().then(r.bind(r,58372))},91645:e=>{"use strict";e.exports=require("net")},92240:(e,t,r)=>{Promise.resolve().then(r.bind(r,29458))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[204,756,441,582],()=>r(40477));module.exports=s})();