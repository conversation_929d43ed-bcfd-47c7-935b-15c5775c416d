'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRequireAdmin } from '@/hooks/useAuth'
import { collection, query, orderBy, limit, getDocs, where, Timestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { COLLECTIONS } from '@/lib/dataService'
import { downloadCSV, formatTransactionsForExport } from '@/lib/csvExport'
import Swal from 'sweetalert2'

interface Transaction {
  id: string
  userId: string
  userName: string
  userEmail: string
  type: string
  amount: number
  description: string
  date: Date
  status: string
}

export default function AdminTransactionsPage() {
  const { user, loading, isAdmin } = useRequireAdmin()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [dataLoading, setDataLoading] = useState(true)
  const [filterType, setFilterType] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 20

  useEffect(() => {
    if (isAdmin) {
      loadTransactions()
    }
  }, [isAdmin])

  const loadTransactions = async () => {
    try {
      setDataLoading(true)
      
      // Get transactions from Firestore
      const transactionsQuery = query(
        collection(db, COLLECTIONS.transactions),
        orderBy('date', 'desc'),
        limit(500) // Limit to recent 500 transactions
      )
      
      const snapshot = await getDocs(transactionsQuery)
      const transactionsList: Transaction[] = []
      
      for (const doc of snapshot.docs) {
        const data = doc.data()
        
        // Get user details
        let userName = 'Unknown User'
        let userEmail = '<EMAIL>'

        try {
          const userDoc = await getDocs(query(
            collection(db, COLLECTIONS.users),
            where('__name__', '==', data.userId)
          ))

          if (!userDoc.empty) {
            const userData = userDoc.docs[0].data()
            userName = userData.name || 'Unknown User'
            userEmail = userData.email || '<EMAIL>'
          }
        } catch (error) {
          console.error('Error fetching user data:', error)
        }
        
        transactionsList.push({
          id: doc.id,
          userId: data.userId,
          userName,
          userEmail,
          type: data.type,
          amount: data.amount,
          description: data.description,
          date: data.date?.toDate() || new Date(),
          status: data.status || 'completed'
        })
      }
      
      setTransactions(transactionsList)
    } catch (error) {
      console.error('Error loading transactions:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to load transactions. Please try again.',
      })
    } finally {
      setDataLoading(false)
    }
  }

  const filteredTransactions = transactions.filter(transaction => {
    const matchesType = !filterType || transaction.type === filterType
    const matchesStatus = !filterStatus || transaction.status === filterStatus
    const matchesSearch = !searchTerm ||
      transaction.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase())

    return matchesType && matchesStatus && matchesSearch
  })

  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const currentTransactions = filteredTransactions.slice(startIndex, startIndex + itemsPerPage)

  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return '₹0.00'
    }
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'video_earning':
        return 'Video Earning'
      case 'withdrawal':
        return 'Withdrawal'
      case 'bonus':
        return 'Bonus'
      case 'referral':
        return 'Referral'
      default:
        return type.charAt(0).toUpperCase() + type.slice(1)
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'video_earning':
        return 'fas fa-play-circle text-green-500'
      case 'withdrawal':
        return 'fas fa-download text-red-500'
      case 'bonus':
        return 'fas fa-gift text-yellow-500'
      case 'referral':
        return 'fas fa-users text-blue-500'
      default:
        return 'fas fa-exchange-alt text-gray-500'
    }
  }

  const handleExportTransactions = () => {
    if (filteredTransactions.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data',
        text: 'No transactions to export.',
      })
      return
    }

    const exportData = formatTransactionsForExport(filteredTransactions)
    downloadCSV(exportData, 'transactions')

    Swal.fire({
      icon: 'success',
      title: 'Export Complete',
      text: `Exported ${filteredTransactions.length} transactions to CSV file.`,
      timer: 2000,
      showConfirmButton: false
    })
  }

  if (loading || dataLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading transactions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Link
              href="/admin"
              className="text-gray-500 hover:text-gray-700"
            >
              <i className="fas fa-arrow-left text-xl"></i>
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">Transactions</h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-gray-700">Total: {filteredTransactions.length}</span>
            <button
              onClick={handleExportTransactions}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
            >
              <i className="fas fa-download mr-2"></i>
              Export CSV
            </button>
            <button
              onClick={loadTransactions}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
            >
              <i className="fas fa-sync-alt mr-2"></i>
              Refresh
            </button>
          </div>
        </div>
      </header>

      {/* Filters */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search user, email, or description..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              <option value="video_earning">Video Earning</option>
              <option value="withdrawal">Withdrawal</option>
              <option value="bonus">Bonus</option>
              <option value="referral">Referral</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          
          <div className="flex items-end">
            <button
              onClick={() => {
                setFilterType('')
                setFilterStatus('')
                setSearchTerm('')
                setCurrentPage(1)
              }}
              className="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="p-6">
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {transaction.userName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {transaction.userEmail}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <i className={`${getTransactionIcon(transaction.type)} mr-2`}></i>
                        <span className="text-sm text-gray-900">
                          {getTransactionTypeLabel(transaction.type)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">
                        {transaction.description}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm font-medium ${
                        transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transaction.date.toLocaleDateString()} {transaction.date.toLocaleTimeString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        transaction.status === 'completed' ? 'bg-green-100 text-green-800' :
                        transaction.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        transaction.status === 'failed' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredTransactions.length)} of {filteredTransactions.length} results
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <span className="px-3 py-1 text-sm bg-blue-500 text-white rounded">
                    {currentPage} of {totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded disabled:opacity-50"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
