"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/register/page",{

/***/ "(app-pages-browser)/./src/app/register/page.tsx":
/*!***********************************!*\
  !*** ./src/app/register/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sweetalert2 */ \"(app-pages-browser)/./node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction RegisterPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__.useAuthState)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        mobile: '',\n        password: '',\n        confirmPassword: '',\n        referralCode: ''\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            if (user && !loading) {\n                window.location.href = '/dashboard';\n            }\n        }\n    }[\"RegisterPage.useEffect\"], [\n        user,\n        loading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RegisterPage.useEffect\": ()=>{\n            // Get referral code from URL if present\n            const urlParams = new URLSearchParams(window.location.search);\n            const refCode = urlParams.get('ref');\n            if (refCode) {\n                setFormData({\n                    \"RegisterPage.useEffect\": (prev)=>({\n                            ...prev,\n                            referralCode: refCode\n                        })\n                }[\"RegisterPage.useEffect\"]);\n            }\n        }\n    }[\"RegisterPage.useEffect\"], []);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    // Referral code generation is handled by generateUniqueReferralCode function\n    const validateForm = ()=>{\n        const { name, email, mobile, password, confirmPassword } = formData;\n        if (!name || !email || !mobile || !password || !confirmPassword) {\n            throw new Error('Please fill in all required fields');\n        }\n        if (name.length < 2) {\n            throw new Error('Name must be at least 2 characters long');\n        }\n        if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n            throw new Error('Please enter a valid email address');\n        }\n        if (!/^[6-9]\\d{9}$/.test(mobile)) {\n            throw new Error('Please enter a valid 10-digit mobile number');\n        }\n        if (password.length < 6) {\n            throw new Error('Password must be at least 6 characters long');\n        }\n        if (password !== confirmPassword) {\n            throw new Error('Passwords do not match');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            validateForm();\n            setIsLoading(true);\n            // Create user account first - Firebase Auth will handle email uniqueness\n            // Mobile number uniqueness will be checked after user creation\n            console.log('Creating user with email and password...');\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_4__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.auth, formData.email, formData.password);\n            const user = userCredential.user;\n            console.log('Firebase Auth user created successfully:', user.uid);\n            console.log('Generating referral code...');\n            // Use a simple, reliable referral code generation for registration\n            // Complex sequential generation can be done later by admin if needed\n            const timestamp = Date.now().toString().slice(-4);\n            const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase();\n            const userReferralCode = \"MY\".concat(timestamp).concat(randomPart);\n            console.log('Generated referral code:', userReferralCode);\n            // Note: Mobile number uniqueness is handled by the admin panel\n            // Firebase Auth already ensures email uniqueness\n            // Create user document in Firestore with all required fields\n            const userData = {\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.name]: formData.name.trim(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.email]: formData.email.toLowerCase(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.mobile]: formData.mobile,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referralCode]: userReferralCode,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referredBy]: formData.referralCode || '',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.referralBonusCredited]: false,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.plan]: 'Trial',\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.planExpiry]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.activeDays]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.joinedDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.Timestamp.now(),\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.wallet]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.totalVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.todayVideos]: 0,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.lastVideoDate]: null,\n                [_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.FIELD_NAMES.videoDuration]: 30,\n                status: 'active'\n            };\n            console.log('Creating user document with data:', userData);\n            console.log('User UID:', user.uid);\n            console.log('Collection:', _lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users);\n            console.log('Document path:', \"\".concat(_lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users, \"/\").concat(user.uid));\n            // Create user document in Firestore\n            console.log('Creating user document in Firestore...');\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, _lib_dataService__WEBPACK_IMPORTED_MODULE_8__.COLLECTIONS.users, user.uid);\n            console.log('Document reference created:', userDocRef.path);\n            console.log('About to create document with data:', JSON.stringify(userData, null, 2));\n            try {\n                console.log('Attempting to create document...');\n                console.log('User UID:', user.uid);\n                console.log('Document path:', userDocRef.path);\n                console.log('Auth user email:', user.email);\n                console.log('Auth user verified:', user.emailVerified);\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.setDoc)(userDocRef, userData);\n                console.log('✅ User document created successfully');\n                // Verify the document was created\n                const verifyDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.getDoc)(userDocRef);\n                if (verifyDoc.exists()) {\n                    console.log('✅ Document verification successful:', verifyDoc.data());\n                } else {\n                    console.error('❌ Document was not created properly');\n                    throw new Error('User document was not created properly');\n                }\n            } catch (firestoreError) {\n                console.error('❌ Firestore setDoc failed:', firestoreError);\n                console.error('❌ Firestore error code:', firestoreError.code);\n                console.error('❌ Firestore error message:', firestoreError.message);\n                console.error('❌ Full error object:', JSON.stringify(firestoreError, null, 2));\n                throw new Error(\"Failed to create user profile: \".concat(firestoreError.message));\n            }\n            // Note: Referral bonus will be credited when admin upgrades user from Trial to paid plan\n            console.log('User registered successfully. Referral bonus will be processed when upgraded to paid plan.');\n            // Show success message and redirect\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'success',\n                title: 'Registration Successful!',\n                text: 'Your account has been created successfully. Welcome to MyTube!',\n                timer: 2000,\n                showConfirmButton: false\n            }).then(()=>{\n                // Force redirect after success message\n                console.log('Redirecting to dashboard...');\n                window.location.href = '/dashboard';\n            });\n        } catch (error) {\n            console.error('Registration error:', error);\n            console.error('Error code:', error.code);\n            console.error('Error message:', error.message);\n            console.error('Full error object:', JSON.stringify(error, null, 2));\n            let message = 'An error occurred during registration';\n            if (error.message.includes('fill in all')) {\n                message = error.message;\n            } else if (error.message.includes('Name must be')) {\n                message = error.message;\n            } else if (error.message.includes('valid email')) {\n                message = error.message;\n            } else if (error.message.includes('valid 10-digit')) {\n                message = error.message;\n            } else if (error.message.includes('Password must be')) {\n                message = error.message;\n            } else if (error.message.includes('Passwords do not match')) {\n                message = error.message;\n            } else if (error.message.includes('email address is already registered')) {\n                message = error.message;\n            } else if (error.message.includes('mobile number is already registered')) {\n                message = error.message;\n            } else {\n                switch(error.code){\n                    case 'auth/email-already-in-use':\n                        message = 'An account with this email already exists';\n                        break;\n                    case 'auth/invalid-email':\n                        message = 'Invalid email address';\n                        break;\n                    case 'auth/weak-password':\n                        message = 'Password is too weak';\n                        break;\n                    default:\n                        message = error.message || 'Registration failed';\n                }\n            }\n            sweetalert2__WEBPACK_IMPORTED_MODULE_9___default().fire({\n                icon: 'error',\n                title: 'Registration Failed',\n                text: message\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 233,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass-card w-full max-w-md p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/img/mytube-logo.svg\",\n                                    alt: \"MyTube Logo\",\n                                    width: 50,\n                                    height: 50,\n                                    className: \"mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"MyTube\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-white mb-2\",\n                            children: \"Create Account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80\",\n                            children: \"Join MyTube and start earning today\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"name\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Full Name *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"name\",\n                                    name: \"name\",\n                                    value: formData.name,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter your full name\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"email\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Email Address *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    id: \"email\",\n                                    name: \"email\",\n                                    value: formData.email,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter your email\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"mobile\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Mobile Number *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"tel\",\n                                    id: \"mobile\",\n                                    name: \"mobile\",\n                                    value: formData.mobile,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter 10-digit mobile number\",\n                                    maxLength: 10,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"password\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Password *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            id: \"password\",\n                                            name: \"password\",\n                                            value: formData.password,\n                                            onChange: handleInputChange,\n                                            className: \"form-input pr-12\",\n                                            placeholder: \"Enter password (min 6 characters)\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"password-toggle-btn\",\n                                            \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas \".concat(showPassword ? 'fa-eye-slash' : 'fa-eye')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmPassword\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Confirm Password *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            value: formData.confirmPassword,\n                                            onChange: handleInputChange,\n                                            className: \"form-input pr-12\",\n                                            placeholder: \"Confirm your password\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            className: \"password-toggle-btn\",\n                                            \"aria-label\": showConfirmPassword ? \"Hide confirm password\" : \"Show confirm password\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas \".concat(showConfirmPassword ? 'fa-eye-slash' : 'fa-eye')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"referralCode\",\n                                    className: \"block text-white font-medium mb-2\",\n                                    children: \"Referral Code (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"referralCode\",\n                                    name: \"referralCode\",\n                                    value: formData.referralCode,\n                                    onChange: handleInputChange,\n                                    className: \"form-input\",\n                                    placeholder: \"Enter referral code if you have one\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"w-full btn-primary flex items-center justify-center mt-6\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner mr-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Creating Account...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-user-plus mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Create Account\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/60\",\n                        children: [\n                            \"Already have an account?\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"text-white font-semibold hover:underline\",\n                                children: \"Sign in here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-white/80 hover:text-white transition-colors inline-flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-arrow-left mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Home\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\register\\\\page.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(RegisterPage, \"dJ1wC6bb5H+msBVAe0YvL0R8f8k=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__.useAuthState\n    ];\n});\n_c = RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/register/page.tsx\n"));

/***/ })

});