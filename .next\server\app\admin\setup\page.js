(()=>{var e={};e.id=912,e.ids=[912],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},5936:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(60687),i=s(43210),n=s(85814),a=s.n(n),o=s(30474),c=s(63385),l=s(75535),d=s(33784),u=s(3582),m=s(77567);function p(){let[e,t]=(0,i.useState)(!1),[s,n]=(0,i.useState)(!1),p=async()=>{t(!0);try{let e="<EMAIL>",t=(await (0,c.eJ)(d.j2,e,"123456")).user,s={[u.FIELD_NAMES.name]:"MyTube Admin",[u.FIELD_NAMES.email]:e,[u.FIELD_NAMES.mobile]:"9999999999",[u.FIELD_NAMES.referralCode]:"ADMIN001",[u.FIELD_NAMES.referredBy]:"",[u.FIELD_NAMES.plan]:"Admin",[u.FIELD_NAMES.planExpiry]:null,[u.FIELD_NAMES.activeDays]:999999,[u.FIELD_NAMES.totalVideos]:0,[u.FIELD_NAMES.todayVideos]:0,[u.FIELD_NAMES.lastVideoDate]:null,[u.FIELD_NAMES.wallet]:0,[u.FIELD_NAMES.joinedDate]:l.Dc.now(),[u.FIELD_NAMES.status]:"active",[u.FIELD_NAMES.videoDuration]:300,role:"admin",isAdmin:!0,permissions:["all"]};await (0,l.BN)((0,l.H9)(d.db,u.COLLECTIONS.users,t.uid),s);let r={email:e,name:"MyTube Admin",role:"super_admin",permissions:["all"],createdAt:l.Dc.now(),isActive:!0};await (0,l.BN)((0,l.H9)(d.db,"admins",t.uid),r),await d.j2.signOut(),n(!0),m.A.fire({icon:"success",title:"Admin Account Created!",html:`
          <div class="text-left">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> 123456</p>
            <br>
            <p>The admin account has been successfully created. You can now login using these credentials.</p>
          </div>
        `,confirmButtonText:"Go to Admin Login"}).then(()=>{window.location.href="/admin/login"})}catch(t){console.error("Error creating admin account:",t);let e="Failed to create admin account";"auth/email-already-in-use"===t.code?(e="Admin account already exists! You can login with: <EMAIL> / 123456",m.A.fire({icon:"info",title:"Admin Account Exists",html:`
            <div class="text-left">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> 123456</p>
              <br>
              <p>The admin account already exists. Use these credentials to login.</p>
            </div>
          `,confirmButtonText:"Go to Admin Login"}).then(()=>{window.location.href="/admin/login"})):m.A.fire({icon:"error",title:"Setup Failed",text:e})}finally{t(!1)}};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,r.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,r.jsx)(o.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,r.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Admin Setup"}),(0,r.jsx)("p",{className:"text-white/80",children:"Create the admin account for MyTube"})]}),s?(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-green-400 text-6xl mb-4",children:(0,r.jsx)("i",{className:"fas fa-check-circle"})}),(0,r.jsx)("h2",{className:"text-xl font-bold text-white mb-2",children:"Setup Complete!"}),(0,r.jsx)("p",{className:"text-white/80 mb-6",children:"Admin account has been created successfully."}),(0,r.jsxs)(a(),{href:"/admin/login",className:"btn-primary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Go to Admin Login"]})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-4 border border-blue-500/30",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Admin Account Details"]}),(0,r.jsxs)("div",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Password:"})," 123456"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Role:"})," Super Administrator"]})]})]}),(0,r.jsx)("button",{onClick:p,disabled:e,className:"w-full btn-primary flex items-center justify-center",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Admin Account..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-user-shield mr-2"}),"Create Admin Account"]})}),(0,r.jsx)("div",{className:"bg-yellow-500/20 rounded-lg p-4 border border-yellow-500/30",children:(0,r.jsxs)("div",{className:"flex items-start text-yellow-300",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle mr-2 mt-1"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("p",{className:"font-semibold mb-1",children:"Security Notice:"}),(0,r.jsx)("p",{children:"This will create an admin account with full system access. Make sure to change the password after first login for security."})]})]})})]}),(0,r.jsxs)("div",{className:"mt-8 text-center space-y-2",children:[(0,r.jsxs)(a(),{href:"/admin/login",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Admin Login"]}),(0,r.jsx)("br",{}),(0,r.jsxs)(a(),{href:"/",className:"text-white/60 hover:text-white/80 transition-colors inline-flex items-center text-sm",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,s)=>{"use strict";s.d(t,{db:()=>l,j2:()=>c});var r=s(67989),i=s(63385),n=s(75535),a=s(70146);let o=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,i.xI)(o),l=(0,n.aU)(o);(0,a.c7)(o)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},47291:(e,t,s)=>{Promise.resolve().then(s.bind(s,5936))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81635:(e,t,s)=>{Promise.resolve().then(s.bind(s,94606))},86517:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var r=s(65239),i=s(48088),n=s(88170),a=s.n(n),o=s(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);s.d(t,c);let l={children:["",{children:["admin",{children:["setup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94606)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\setup\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\setup\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/setup/page",pathname:"/admin/setup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91645:e=>{"use strict";e.exports=require("net")},94606:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\setup\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[204,756,567,441,582],()=>s(86517));module.exports=r})();