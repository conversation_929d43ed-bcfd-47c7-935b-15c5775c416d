'use client'

import { useState } from 'react'
import { collection, query, orderBy, limit, getDocs, getCountFromServer } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS, generateUniqueReferralCode } from '@/lib/dataService'

export default function TestSequentialCodesPage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const addToResult = (text: string) => {
    setResult(prev => prev + text + '\n')
  }

  const testSequentialGeneration = async () => {
    setResult('')
    setIsLoading(true)
    
    try {
      addToResult('🔍 Testing Sequential Referral Code Generation...\n')
      
      // Test 1: Check current database state
      addToResult('=== TEST 1: Current Database State ===')
      const usersRef = collection(db, COLLECTIONS.users)
      const countSnapshot = await getCountFromServer(usersRef)
      const totalUsers = countSnapshot.data().count
      
      addToResult(`Total users in database: ${totalUsers}`)
      
      // Get the highest existing referral code
      const codesQuery = query(
        usersRef,
        orderBy(FIELD_NAMES.referralCode, 'desc'),
        limit(5)
      )
      const codesSnapshot = await getDocs(codesQuery)
      
      addToResult(`\nTop 5 existing referral codes:`)
      codesSnapshot.docs.forEach((doc, index) => {
        const code = doc.data()[FIELD_NAMES.referralCode]
        addToResult(`${index + 1}. ${code}`)
      })
      
      // Find highest MYN code
      let highestMYNNumber = 0
      codesSnapshot.docs.forEach(doc => {
        const code = doc.data()[FIELD_NAMES.referralCode]
        if (code && code.startsWith('MYN')) {
          const numberPart = parseInt(code.substring(3))
          if (!isNaN(numberPart) && numberPart > highestMYNNumber) {
            highestMYNNumber = numberPart
          }
        }
      })
      
      addToResult(`\nHighest MYN number found: ${highestMYNNumber}`)
      addToResult(`Expected next MYN code: MYN${String(highestMYNNumber + 1).padStart(4, '0')}`)
      
      // Test 2: Generate sequential codes
      addToResult('\n=== TEST 2: Generate Sequential Codes ===')
      
      for (let i = 1; i <= 5; i++) {
        try {
          addToResult(`\nGenerating code ${i}...`)
          const generatedCode = await generateUniqueReferralCode()
          addToResult(`✅ Generated: ${generatedCode}`)
          
          // Verify it follows MYN format
          if (generatedCode.startsWith('MYN')) {
            const numberPart = generatedCode.substring(3)
            if (/^\d{4}$/.test(numberPart)) {
              addToResult(`   ✅ Format correct: MYN + 4 digits`)
              addToResult(`   ✅ Number: ${parseInt(numberPart)}`)
            } else {
              addToResult(`   ❌ Format incorrect: Expected 4 digits, got "${numberPart}"`)
            }
          } else {
            addToResult(`   ❌ Prefix incorrect: Expected "MYN", got "${generatedCode.substring(0, 3)}"`)
          }
          
        } catch (error: any) {
          addToResult(`❌ Generation ${i} failed: ${error.message}`)
        }
        
        // Small delay between generations
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      
      // Test 3: Verify sequential order
      addToResult('\n=== TEST 3: Verify Sequential Order ===')
      
      const newCodesQuery = query(
        usersRef,
        orderBy(FIELD_NAMES.referralCode, 'desc'),
        limit(10)
      )
      const newCodesSnapshot = await getDocs(newCodesQuery)
      
      const mynCodes: number[] = []
      newCodesSnapshot.docs.forEach(doc => {
        const code = doc.data()[FIELD_NAMES.referralCode]
        if (code && code.startsWith('MYN')) {
          const numberPart = parseInt(code.substring(3))
          if (!isNaN(numberPart)) {
            mynCodes.push(numberPart)
          }
        }
      })
      
      mynCodes.sort((a, b) => b - a) // Sort descending
      
      addToResult(`Recent MYN codes (descending order):`)
      mynCodes.slice(0, 10).forEach((num, index) => {
        addToResult(`${index + 1}. MYN${String(num).padStart(4, '0')}`)
      })
      
      // Check if they are sequential
      let isSequential = true
      for (let i = 1; i < Math.min(mynCodes.length, 5); i++) {
        if (mynCodes[i-1] - mynCodes[i] !== 1) {
          isSequential = false
          break
        }
      }
      
      addToResult(`\nSequential order check: ${isSequential ? '✅ PASS' : '❌ FAIL'}`)
      
      // Test 4: Performance test
      addToResult('\n=== TEST 4: Performance Test ===')
      const startTime = Date.now()
      
      try {
        const perfTestCode = await generateUniqueReferralCode()
        const endTime = Date.now()
        const duration = endTime - startTime
        
        addToResult(`✅ Performance test passed`)
        addToResult(`   Generated: ${perfTestCode}`)
        addToResult(`   Time taken: ${duration}ms`)
        
        if (duration < 1000) {
          addToResult(`   ✅ Performance: GOOD (< 1 second)`)
        } else if (duration < 3000) {
          addToResult(`   ⚠️ Performance: ACCEPTABLE (1-3 seconds)`)
        } else {
          addToResult(`   ❌ Performance: SLOW (> 3 seconds)`)
        }
        
      } catch (perfError: any) {
        addToResult(`❌ Performance test failed: ${perfError.message}`)
      }
      
      addToResult('\n🎯 CONCLUSION:')
      addToResult('Sequential referral code generation test completed.')
      addToResult('Check the results above to verify proper MYN sequential numbering.')
      
    } catch (error: any) {
      addToResult(`❌ Test failed: ${error.message}`)
      addToResult(`Error code: ${error.code}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Sequential Referral Code Test</h1>
        
        <div className="glass-card p-6 mb-6">
          <button
            onClick={testSequentialGeneration}
            disabled={isLoading}
            className="btn-primary mb-4"
          >
            {isLoading ? 'Testing Sequential Generation...' : 'Test Sequential Code Generation'}
          </button>
          
          <div className="bg-black/50 p-4 rounded-lg">
            <pre className="text-white text-sm whitespace-pre-wrap overflow-auto max-h-96">
              {result || 'Click "Test Sequential Code Generation" to start...'}
            </pre>
          </div>
        </div>
        
        <div className="text-center">
          <a href="/register" className="text-blue-400 hover:text-blue-300 underline">
            ← Back to Registration
          </a>
        </div>
      </div>
    </div>
  )
}
