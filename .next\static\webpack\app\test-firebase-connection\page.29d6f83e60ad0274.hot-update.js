"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-firebase-connection/page",{

/***/ "(app-pages-browser)/./src/app/test-firebase-connection/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/test-firebase-connection/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestFirebaseConnectionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TestFirebaseConnectionPage() {\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const testFirebaseConnection = async ()=>{\n        setResult('');\n        setIsLoading(true);\n        try {\n            setResult('🔍 Testing Complete Registration Flow...\\n');\n            setResult((prev)=>prev + \"Environment: \".concat(window.location.origin, \"\\n\"));\n            setResult((prev)=>prev + \"Project ID: \".concat(\"mytube-india\", \"\\n\"));\n            setResult((prev)=>prev + \"Auth Domain: \".concat(\"mytube-india.firebaseapp.com\", \"\\n\\n\"));\n            // Test 1: Firebase Auth User Creation (like registration)\n            setResult((prev)=>prev + '📡 Test 1: Firebase Auth User Creation\\n');\n            const testEmail = \"regtest\".concat(Date.now(), \"@example.com\");\n            const testPassword = 'regtest123456';\n            try {\n                setResult((prev)=>prev + \"Creating user: \".concat(testEmail, \"\\n\"));\n                const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, testEmail, testPassword);\n                const user = userCredential.user;\n                setResult((prev)=>prev + \"✅ Auth user created: \".concat(user.uid, \"\\n\"));\n                setResult((prev)=>prev + \"   Email: \".concat(user.email, \"\\n\"));\n                setResult((prev)=>prev + \"   Email verified: \".concat(user.emailVerified, \"\\n\"));\n                // Wait for auth to propagate\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                setResult((prev)=>prev + \"   Auth state: \".concat(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser ? 'authenticated' : 'not authenticated', \"\\n\"));\n                // Test 2: Create user document exactly like registration\n                setResult((prev)=>prev + '\\n📡 Test 2: Creating User Document (Registration Style)\\n');\n                // Import the exact field names and collections\n                const { FIELD_NAMES, COLLECTIONS } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_dataService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n                // Generate referral code exactly like registration\n                const timestamp = Date.now().toString().slice(-4);\n                const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase();\n                const userReferralCode = \"MY\".concat(timestamp).concat(randomPart);\n                setResult((prev)=>prev + \"Generated referral code: \".concat(userReferralCode, \"\\n\"));\n                // Create user data exactly like registration\n                const userData = {\n                    [FIELD_NAMES.name]: 'Registration Test User',\n                    [FIELD_NAMES.email]: testEmail.toLowerCase(),\n                    [FIELD_NAMES.mobile]: '9876543210',\n                    [FIELD_NAMES.referralCode]: userReferralCode,\n                    [FIELD_NAMES.referredBy]: '',\n                    [FIELD_NAMES.referralBonusCredited]: false,\n                    [FIELD_NAMES.plan]: 'Trial',\n                    [FIELD_NAMES.planExpiry]: null,\n                    [FIELD_NAMES.activeDays]: 0,\n                    [FIELD_NAMES.joinedDate]: new Date(),\n                    [FIELD_NAMES.wallet]: 0,\n                    [FIELD_NAMES.totalVideos]: 0,\n                    [FIELD_NAMES.todayVideos]: 0,\n                    [FIELD_NAMES.lastVideoDate]: null,\n                    [FIELD_NAMES.videoDuration]: 30,\n                    status: 'active'\n                };\n                setResult((prev)=>prev + \"Document path: \".concat(COLLECTIONS.users, \"/\").concat(user.uid, \"\\n\"));\n                setResult((prev)=>prev + \"Field count: \".concat(Object.keys(userData).length, \"\\n\"));\n                setResult((prev)=>{\n                    var _auth_currentUser;\n                    return prev + \"Current auth UID: \".concat((_auth_currentUser = _lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.uid, \"\\n\");\n                });\n                setResult((prev)=>prev + \"Target UID: \".concat(user.uid, \"\\n\"));\n                setResult((prev)=>{\n                    var _auth_currentUser;\n                    return prev + \"UIDs match: \".concat(((_auth_currentUser = _lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.uid) === user.uid, \"\\n\");\n                });\n                const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, COLLECTIONS.users, user.uid);\n                try {\n                    setResult((prev)=>prev + '\\nAttempting setDoc...\\n');\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.setDoc)(userDocRef, userData);\n                    setResult((prev)=>prev + '✅ User document created successfully!\\n');\n                    // Verify document\n                    const verifyDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(userDocRef);\n                    if (verifyDoc.exists()) {\n                        const docData = verifyDoc.data();\n                        setResult((prev)=>prev + '✅ Document verification successful\\n');\n                        setResult((prev)=>prev + \"   Name: \".concat(docData[FIELD_NAMES.name], \"\\n\"));\n                        setResult((prev)=>prev + \"   Email: \".concat(docData[FIELD_NAMES.email], \"\\n\"));\n                        setResult((prev)=>prev + \"   Plan: \".concat(docData[FIELD_NAMES.plan], \"\\n\"));\n                        setResult((prev)=>prev + \"   Referral Code: \".concat(docData[FIELD_NAMES.referralCode], \"\\n\"));\n                        setResult((prev)=>prev + '\\n🎉 REGISTRATION TEST SUCCESSFUL!\\n');\n                        setResult((prev)=>prev + 'The registration flow works perfectly.\\n');\n                        setResult((prev)=>prev + 'If registration is failing, check for:\\n');\n                        setResult((prev)=>prev + '- Form validation errors\\n');\n                        setResult((prev)=>prev + '- Network connectivity issues\\n');\n                        setResult((prev)=>prev + '- Browser console errors\\n');\n                    } else {\n                        setResult((prev)=>prev + '❌ Document verification failed\\n');\n                    }\n                } catch (setDocError) {\n                    setResult((prev)=>prev + \"❌ setDoc failed: \".concat(setDocError.message, \"\\n\"));\n                    setResult((prev)=>prev + \"   Error code: \".concat(setDocError.code, \"\\n\"));\n                    setResult((prev)=>prev + \"   Full error: \".concat(JSON.stringify(setDocError, null, 2), \"\\n\"));\n                    if (setDocError.code === 'permission-denied') {\n                        setResult((prev)=>prev + '\\n🔧 PERMISSION ISSUE DETECTED:\\n');\n                        setResult((prev)=>prev + '   - Check Firestore security rules\\n');\n                        setResult((prev)=>prev + '   - Ensure rules allow authenticated users to write their own documents\\n');\n                        setResult((prev)=>prev + '   - Verify the user is properly authenticated\\n');\n                    }\n                }\n                // Clean up\n                try {\n                    await user.delete();\n                    setResult((prev)=>prev + '✅ Test user deleted\\n');\n                } catch (deleteError) {\n                    setResult((prev)=>prev + \"⚠️ User deletion failed: \".concat(deleteError.message, \"\\n\"));\n                }\n            } catch (authError) {\n                setResult((prev)=>prev + \"❌ Auth user creation failed: \".concat(authError.message, \"\\n\"));\n                setResult((prev)=>prev + \"   Code: \".concat(authError.code, \"\\n\"));\n                if (authError.code === 'auth/network-request-failed') {\n                    setResult((prev)=>prev + '\\n🔧 NETWORK ISSUE DETECTED:\\n');\n                    setResult((prev)=>prev + '   - Check your internet connection\\n');\n                    setResult((prev)=>prev + '   - Try disabling VPN/proxy\\n');\n                    setResult((prev)=>prev + '   - Check if firewall is blocking Firebase\\n');\n                    setResult((prev)=>prev + '   - Try testing on a different network\\n');\n                }\n            }\n        } catch (error) {\n            setResult((prev)=>prev + \"❌ Test failed: \".concat(error.message, \"\\n\"));\n            setResult((prev)=>prev + \"   Code: \".concat(error.code, \"\\n\"));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-white mb-6\",\n                    children: \"Registration Flow Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testFirebaseConnection,\n                            disabled: isLoading,\n                            className: \"btn-primary mb-4\",\n                            children: isLoading ? 'Testing Registration...' : 'Test Complete Registration Flow'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black/50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-white text-sm whitespace-pre-wrap\",\n                                children: result || 'Click \"Test Complete Registration Flow\" to start...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/register\",\n                        className: \"text-blue-400 hover:text-blue-300 underline\",\n                        children: \"← Back to Registration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-firebase-connection\\\\page.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_s(TestFirebaseConnectionPage, \"TA2EjS24NWK+5U65aZ0FJpR3Amc=\");\n_c = TestFirebaseConnectionPage;\nvar _c;\n$RefreshReg$(_c, \"TestFirebaseConnectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-firebase-connection/page.tsx\n"));

/***/ })

});