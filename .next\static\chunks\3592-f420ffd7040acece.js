"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3592],{3592:(e,t,a)=>{a.d(t,{AX:()=>M,COLLECTIONS:()=>i,FIELD_NAMES:()=>n,Gl:()=>V,I0:()=>g,II:()=>b,IK:()=>k,Pp:()=>s,Q6:()=>D,QD:()=>T,Ss:()=>N,YG:()=>h,_f:()=>S,addTransaction:()=>u,b6:()=>l,bA:()=>G,fP:()=>x,getUserData:()=>c,i8:()=>U,iA:()=>_,k6:()=>d,mm:()=>p,mv:()=>I,pl:()=>f,pu:()=>H,ul:()=>B,updateWalletBalance:()=>w,w1:()=>A,wT:()=>E,x4:()=>Y,xj:()=>C,yx:()=>y,z8:()=>q,zb:()=>m});var r=a(5317),o=a(6104);let n={name:"name",email:"email",mobile:"mobile",referralCode:"referralCode",referredBy:"referredBy",referralBonusCredited:"referralBonusCredited",plan:"plan",planExpiry:"planExpiry",activeDays:"activeDays",joinedDate:"joinedDate",wallet:"wallet",bankAccountHolderName:"bankAccountHolderName",bankAccountNumber:"bankAccountNumber",bankIfscCode:"bankIfscCode",bankName:"bankName",bankDetailsUpdated:"bankDetailsUpdated",totalVideos:"totalVideos",todayVideos:"todayVideos",lastVideoDate:"lastVideoDate",videoDuration:"videoDuration",quickVideoAdvantage:"quickVideoAdvantage",quickVideoAdvantageExpiry:"quickVideoAdvantageExpiry",quickVideoAdvantageDays:"quickVideoAdvantageDays",quickVideoAdvantageSeconds:"quickVideoAdvantageSeconds",quickVideoAdvantageGrantedBy:"quickVideoAdvantageGrantedBy",quickVideoAdvantageGrantedAt:"quickVideoAdvantageGrantedAt",type:"type",amount:"amount",date:"date",status:"status",description:"description",userId:"userId"},i={users:"users",transactions:"transactions",withdrawals:"withdrawals",plans:"plans",settings:"settings",notifications:"notifications",adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function c(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserData:",e),null;let s=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(s.exists()){var t,a,c,d;let e=s.data(),r={name:String(e[n.name]||""),email:String(e[n.email]||""),mobile:String(e[n.mobile]||""),referralCode:String(e[n.referralCode]||""),referredBy:String(e[n.referredBy]||""),plan:String(e[n.plan]||"Trial"),planExpiry:(null==(t=e[n.planExpiry])?void 0:t.toDate())||null,activeDays:Number(e[n.activeDays]||0),joinedDate:(null==(a=e[n.joinedDate])?void 0:a.toDate())||new Date,videoDuration:Number(e[n.videoDuration]||("Trial"===e[n.plan]?30:300)),quickVideoAdvantage:!!e[n.quickVideoAdvantage],quickVideoAdvantageExpiry:(null==(c=e[n.quickVideoAdvantageExpiry])?void 0:c.toDate())||null,quickVideoAdvantageDays:Number(e[n.quickVideoAdvantageDays]||0),quickVideoAdvantageSeconds:Number(e[n.quickVideoAdvantageSeconds]||30),quickVideoAdvantageGrantedBy:String(e[n.quickVideoAdvantageGrantedBy]||""),quickVideoAdvantageGrantedAt:(null==(d=e[n.quickVideoAdvantageGrantedAt])?void 0:d.toDate())||null};return console.log("getUserData result:",r),r}return null}catch(e){return console.error("Error getting user data:",e),null}}async function d(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getWalletData:",e),{wallet:0};let t=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(t.exists()){let e=t.data(),a={wallet:Number(e[n.wallet]||0)};return console.log("getWalletData result:",a),a}return{wallet:0}}catch(e){return console.error("Error getting wallet data:",e),{wallet:0}}}async function s(e){try{let a=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(a.exists()){var t;let e=a.data(),r=e[n.totalVideos]||0,o=e[n.todayVideos]||0,i=null==(t=e[n.lastVideoDate])?void 0:t.toDate(),c=new Date,d=!i||i.toDateString()!==c.toDateString();return{totalVideos:r,todayVideos:d?0:o,remainingVideos:Math.max(0,50-(d?0:o))}}return{totalVideos:0,todayVideos:0,remainingVideos:50}}catch(e){throw console.error("Error getting video count data:",e),e}}async function l(e,t){try{await (0,r.mZ)((0,r.H9)(o.db,i.users,e),t)}catch(e){throw console.error("Error updating user data:",e),e}}async function u(e,t){try{let a={[n.userId]:e,[n.type]:t.type,[n.amount]:t.amount,[n.description]:t.description,[n.status]:t.status||"completed",[n.date]:r.Dc.now()};await (0,r.gS)((0,r.rJ)(o.db,i.transactions),a)}catch(e){throw console.error("Error adding transaction:",e),e}}async function g(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getTransactions:",e),[];let a=(0,r.P)((0,r.rJ)(o.db,i.transactions),(0,r._M)(n.userId,"==",e),(0,r.AB)(t)),c=(await (0,r.GG)(a)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[n.date])?void 0:t.toDate()}});return c.sort((e,t)=>{let a=e.date||new Date(0);return(t.date||new Date(0)).getTime()-a.getTime()}),c}catch(e){return console.error("Error getting transactions:",e),[]}}async function f(e){try{let t=(0,r.P)((0,r.rJ)(o.db,i.users),(0,r._M)(n.referredBy,"==",e));return(await (0,r.GG)(t)).docs.map(e=>{var t;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[n.joinedDate])?void 0:t.toDate()}})}catch(e){throw console.error("Error getting referrals:",e),e}}async function y(e){try{let t=new Date,a=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(a,{[n.totalVideos]:(0,r.GV)(1),[n.todayVideos]:(0,r.GV)(1),[n.lastVideoDate]:r.Dc.fromDate(t)})}catch(e){throw console.error("Error updating video count:",e),e}}async function w(e,t){try{let a=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(a,{[n.wallet]:(0,r.GV)(t)})}catch(e){throw console.error("Error updating wallet balance:",e),e}}async function p(e,t){try{if(!e||"string"!=typeof e)throw Error("Invalid userId provided");var a=t;let{accountHolderName:c,accountNumber:d,ifscCode:s,bankName:l}=a;if(!c||c.trim().length<2)throw Error("Account holder name must be at least 2 characters long");if(!d||!/^\d{9,18}$/.test(d.trim()))throw Error("Account number must be 9-18 digits");if(!s||!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(s.trim().toUpperCase()))throw Error("Invalid IFSC code format (e.g., SBIN0001234)");if(!l||l.trim().length<2)throw Error("Bank name must be at least 2 characters long");let u=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(u,{[n.bankAccountHolderName]:t.accountHolderName.trim(),[n.bankAccountNumber]:t.accountNumber.trim(),[n.bankIfscCode]:t.ifscCode.trim().toUpperCase(),[n.bankName]:t.bankName.trim(),[n.bankDetailsUpdated]:r.Dc.now()}),console.log("Bank details saved successfully for user:",e)}catch(e){throw console.error("Error saving bank details:",e),e}}async function m(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getBankDetails:",e),null;let t=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(t.exists()){let e=t.data();if(e[n.bankAccountNumber]){let t={accountHolderName:String(e[n.bankAccountHolderName]||""),accountNumber:String(e[n.bankAccountNumber]||""),ifscCode:String(e[n.bankIfscCode]||""),bankName:String(e[n.bankName]||"")};return console.log("getBankDetails result found"),t}}return console.log("No bank details found for user"),null}catch(e){return console.error("Error getting bank details:",e),null}}function v(e){return({Trial:2,Starter:30,Basic:30,Premium:30,Gold:30,Platinum:30,Diamond:30,499:30,1499:30,2999:30,3999:30,5999:30,9999:30})[e]||2}async function h(e){try{let t=await c(e);if(!t)return{expired:!0,reason:"User data not found"};if("Trial"===t.plan){let e=t.joinedDate||new Date,a=new Date,r=Math.floor((a.getTime()-e.getTime())/864e5),o=Math.max(0,2-r);return{expired:o<=0,reason:o<=0?"Trial period expired":void 0,daysLeft:o,activeDays:r}}if(t.planExpiry){let e=new Date,a=e>t.planExpiry,r=a?0:Math.ceil((t.planExpiry.getTime()-e.getTime())/864e5);return{expired:a,reason:a?"Plan subscription expired":void 0,daysLeft:r,activeDays:t.activeDays||0}}let a=v(t.plan),r=t.activeDays||0,o=Math.max(0,a-r),n=o<=0;return{expired:n,reason:n?"Plan validity period (".concat(a," days) exceeded based on active days"):void 0,daysLeft:o,activeDays:r}}catch(e){return console.error("Error checking plan expiry:",e),{expired:!0,reason:"Error checking plan status"}}}async function b(e,t,a){try{let c=(0,r.H9)(o.db,i.users,e);if("Trial"===t)await (0,r.mZ)(c,{[n.planExpiry]:null});else{let o;if(a)o=a;else{let e=v(t),a=new Date;o=new Date(a.getTime()+24*e*36e5)}await (0,r.mZ)(c,{[n.planExpiry]:r.Dc.fromDate(o)}),console.log("Updated plan expiry for user ".concat(e," to ").concat(o.toDateString()))}}catch(e){throw console.error("Error updating plan expiry:",e),e}}async function k(e,t,a){try{if("Trial"!==t||"Trial"===a)return void console.log("Referral bonus only applies when upgrading from Trial to paid plan");console.log("Processing referral bonus for user ".concat(e," upgrading from ").concat(t," to ").concat(a));let c=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(!c.exists())return void console.log("User not found");let d=c.data(),s=d[n.referredBy],l=d[n.referralBonusCredited];if(!s)return void console.log("User was not referred by anyone, skipping bonus processing");if(l)return void console.log("Referral bonus already credited for this user, skipping");console.log("Finding referrer with code:",s);let g=(0,r.P)((0,r.rJ)(o.db,i.users),(0,r._M)(n.referralCode,"==",s),(0,r.AB)(1)),f=await (0,r.GG)(g);if(f.empty)return void console.log("Referral code not found:",s);let y=f.docs[0].id,p={Trial:0,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200}[a]||0;if(console.log("Found referrer: ".concat(y,", bonus amount: ₹").concat(p)),p>0){await w(y,p);let t=(0,r.H9)(o.db,i.users,y);await (0,r.mZ)(t,{[n.totalVideos]:(0,r.GV)(50)});let c=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(c,{[n.referralBonusCredited]:!0}),await u(y,{type:"referral_bonus",amount:p,description:"Referral bonus for ".concat(a," plan upgrade + 50 bonus videos (User: ").concat(d[n.name],")")}),console.log("✅ Referral bonus processed: ₹".concat(p," + 50 videos for referrer ").concat(y))}else console.log("No bonus amount calculated, skipping")}catch(e){console.error("❌ Error processing referral bonus:",e)}}async function D(e){try{var t;let a=await c(e);if(!a)return{videoDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1};let r=!!(t=a).quickVideoAdvantage&&!!t.quickVideoAdvantageExpiry&&new Date<t.quickVideoAdvantageExpiry,o=a.videoDuration;return r?o=a.quickVideoAdvantageSeconds||30:o&&"Trial"!==a.plan||(o=({Trial:30,Starter:300,Basic:300,Premium:300,Gold:180,Platinum:120,Diamond:60})[a.plan]||30),{videoDuration:o,earningPerBatch:({Trial:10,Starter:25,Basic:75,Premium:150,Gold:200,Platinum:250,Diamond:400})[a.plan]||10,plan:a.plan,hasQuickAdvantage:r,quickAdvantageExpiry:a.quickVideoAdvantageExpiry}}catch(e){return console.error("Error getting user video settings:",e),{videoDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1}}}async function A(e,t,a){let c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:30;try{if(t<=0||t>365)throw Error("Days must be between 1 and 365");if(c<1||c>420)throw Error("Seconds must be between 1 and 420 (7 minutes)");let d=new Date,s=new Date(d.getTime()+24*t*36e5),l=(0,r.H9)(o.db,i.users,e);return await (0,r.mZ)(l,{[n.quickVideoAdvantage]:!0,[n.quickVideoAdvantageExpiry]:r.Dc.fromDate(s),[n.quickVideoAdvantageDays]:t,[n.quickVideoAdvantageSeconds]:c,[n.quickVideoAdvantageGrantedBy]:a,[n.quickVideoAdvantageGrantedAt]:r.Dc.fromDate(d)}),console.log("Granted quick video advantage to user ".concat(e," for ").concat(t," days until ").concat(s.toDateString())),await u(e,{type:"quick_advantage_granted",amount:0,description:"Quick video advantage granted for ".concat(t," days by ").concat(a)}),{success:!0,expiry:s}}catch(e){throw console.error("Error granting quick video advantage:",e),e}}async function E(e,t){try{let a=(0,r.H9)(o.db,i.users,e);return await (0,r.mZ)(a,{[n.quickVideoAdvantage]:!1,[n.quickVideoAdvantageExpiry]:null,[n.quickVideoAdvantageDays]:0,[n.quickVideoAdvantageSeconds]:30,[n.quickVideoAdvantageGrantedBy]:"",[n.quickVideoAdvantageGrantedAt]:null}),console.log("Removed quick video advantage from user ".concat(e)),await u(e,{type:"quick_advantage_removed",amount:0,description:"Quick video advantage removed by ".concat(t)}),{success:!0}}catch(e){throw console.error("Error removing quick video advantage:",e),e}}async function V(e,t){try{let a=[1,10,30].includes(t),c=t>=60&&t<=420;if(!a&&!c)throw Error("Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration");let d=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(d,{[n.videoDuration]:t}),console.log("Updated video duration for user ".concat(e," to ").concat(t," seconds"))}catch(e){throw console.error("Error updating user video duration:",e),e}}async function q(e){try{let t={title:e.title,message:e.message,type:e.type,targetUsers:e.targetUsers,userIds:e.userIds||[],createdAt:r.Dc.now(),createdBy:e.createdBy};console.log("Adding notification to Firestore:",t);let a=await (0,r.gS)((0,r.rJ)(o.db,i.notifications),t);console.log("Notification added successfully with ID:",a.id);let n=await (0,r.x7)(a);return n.exists()?console.log("Notification verified in database:",n.data()):console.warn("Notification not found after adding"),a.id}catch(e){throw console.error("Error adding notification:",e),e}}async function N(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;try{let a,n;if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserNotifications:",e),[];console.log("Loading notifications for user: ".concat(e));try{let e=(0,r.P)((0,r.rJ)(o.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.My)("createdAt","desc"),(0,r.AB)(t));a=await (0,r.GG)(e),console.log("Found ".concat(a.docs.length," notifications for all users"))}catch(n){console.warn("Error querying all users notifications, trying without orderBy:",n);let e=(0,r.P)((0,r.rJ)(o.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.AB)(t));a=await (0,r.GG)(e)}try{let a=(0,r.P)((0,r.rJ)(o.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",e),(0,r.My)("createdAt","desc"),(0,r.AB)(t));n=await (0,r.GG)(a),console.log("Found ".concat(n.docs.length," notifications for specific user"))}catch(c){console.warn("Error querying specific user notifications, trying without orderBy:",c);let a=(0,r.P)((0,r.rJ)(o.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",e),(0,r.AB)(t));n=await (0,r.GG)(a)}let c=[];a.docs.forEach(e=>{var t;c.push({id:e.id,...e.data(),createdAt:(null==(t=e.data().createdAt)?void 0:t.toDate())||new Date})}),n.docs.forEach(e=>{var t;c.push({id:e.id,...e.data(),createdAt:(null==(t=e.data().createdAt)?void 0:t.toDate())||new Date})}),c.sort((e,t)=>t.createdAt.getTime()-e.createdAt.getTime());let d=c.slice(0,t);return console.log("Returning ".concat(d.length," total notifications for user")),d}catch(e){return console.error("Error getting user notifications:",e),[]}}async function S(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{let t=(0,r.P)((0,r.rJ)(o.db,i.notifications),(0,r.My)("createdAt","desc"),(0,r.AB)(e));return(await (0,r.GG)(t)).docs.map(e=>{var t;return{id:e.id,...e.data(),createdAt:(null==(t=e.data().createdAt)?void 0:t.toDate())||new Date}})}catch(e){return console.error("Error getting all notifications:",e),[]}}async function x(e){try{if(!e||"string"!=typeof e)throw Error("Invalid notification ID provided");console.log("Deleting notification:",e),await (0,r.kd)((0,r.H9)(o.db,i.notifications,e)),console.log("Notification deleted successfully")}catch(e){throw console.error("Error deleting notification:",e),e}}async function G(e,t){try{let a=JSON.parse(localStorage.getItem("read_notifications_".concat(t))||"[]");a.includes(e)||(a.push(e),localStorage.setItem("read_notifications_".concat(t),JSON.stringify(a)))}catch(e){console.error("Error marking notification as read:",e)}}function I(e,t){try{return JSON.parse(localStorage.getItem("read_notifications_".concat(t))||"[]").includes(e)}catch(e){return console.error("Error checking notification read status:",e),!1}}function B(e,t){try{let a=JSON.parse(localStorage.getItem("read_notifications_".concat(t))||"[]");return e.filter(e=>!a.includes(e.id)).length}catch(e){return console.error("Error getting unread notification count:",e),0}}async function M(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUnreadNotifications:",e),[];console.log("Loading unread notifications for user: ".concat(e));let t=await N(e,50),a=JSON.parse(localStorage.getItem("read_notifications_".concat(e))||"[]"),r=t.filter(e=>e.id&&!a.includes(e.id));return console.log("Found ".concat(r.length," unread notifications")),r}catch(e){return console.error("Error getting unread notifications:",e),[]}}async function _(e){try{return(await M(e)).length>0}catch(e){return console.error("Error checking for unread notifications:",e),!1}}async function P(e){try{let t=(0,r.P)((0,r.rJ)(o.db,i.withdrawals),(0,r._M)("userId","==",e),(0,r._M)("status","==","pending"),(0,r.AB)(1));return!(await (0,r.GG)(t)).empty}catch(e){return console.error("Error checking pending withdrawals:",e),!1}}async function T(e){try{let t=await c(e);if(!t)return{allowed:!1,reason:"Unable to verify user information. Please try again."};if("Trial"===t.plan)return{allowed:!1,reason:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals."};if(await P(e))return{allowed:!1,reason:"You have a pending withdrawal request. Please wait for it to be processed before submitting a new request."};let r=new Date,o=r.getHours();if(o<10||o>=18)return{allowed:!1,reason:"Withdrawals are only allowed between 10:00 AM to 6:00 PM"};let{isAdminLeaveDay:n}=await a.e(9567).then(a.bind(a,9567));if(await n(r))return{allowed:!1,reason:"Withdrawals are not allowed on admin leave/holiday days"};let{isUserOnLeave:i}=await a.e(9567).then(a.bind(a,9567));if(await i(e,r))return{allowed:!1,reason:"Withdrawals are not allowed on your leave days"};return{allowed:!0}}catch(e){return console.error("Error checking withdrawal allowed:",e),{allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."}}}async function C(e,t,a){try{if(t<50)throw Error("Minimum withdrawal amount is ₹50");let n=await T(e);if(!n.allowed)throw Error(n.reason);if((await d(e)).wallet<t)throw Error("Insufficient wallet balance");await w(e,-t),await u(e,{type:"withdrawal_request",amount:-t,description:"Withdrawal request submitted - ₹".concat(t," debited from wallet")});let c={userId:e,amount:t,bankDetails:a,status:"pending",date:r.Dc.now(),createdAt:r.Dc.now()};return(await (0,r.gS)((0,r.rJ)(o.db,i.withdrawals),c)).id}catch(e){throw console.error("Error creating withdrawal request:",e),e}}async function U(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;try{let a=(0,r.P)((0,r.rJ)(o.db,i.withdrawals),(0,r._M)("userId","==",e),(0,r.My)("date","desc"),(0,r.AB)(t));return(await (0,r.GG)(a)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}})}catch(e){return console.error("Error getting user withdrawals:",e),[]}}async function H(){try{console.log("Generating sequential referral code...");try{let e=(0,r.rJ)(o.db,i.users),t=(0,r.P)(e,(0,r._M)(n.referralCode,">=","MYN0000"),(0,r._M)(n.referralCode,"<=","MYN9999"),(0,r.My)(n.referralCode,"desc"),(0,r.AB)(1)),a=await (0,r.GG)(t),c=1;if(a.empty)console.log("No existing MYN codes found, starting from MYN0001");else{let e=a.docs[0].data()[n.referralCode];if(console.log("Highest existing MYN code:",e),e&&e.startsWith("MYN")){let t=e.substring(3),a=parseInt(t);isNaN(a)||(c=a+1,console.log("Next sequential number: ".concat(c)))}}let d="MYN".concat(String(c).padStart(4,"0"));return console.log("Generated sequential referral code: ".concat(d)),d}catch(e){console.log("Sequential method failed, trying fallback:",e)}try{let e=(0,r.rJ)(o.db,i.users),t=(await (0,r.d_)(e)).data().count+1,a="MYN".concat(String(t).padStart(4,"0"));return console.log("Using count-based fallback code: ".concat(a)),a}catch(e){console.log("Count method also failed, using random fallback:",e)}let e="MYN".concat(Math.floor(1e3+9e3*Math.random()));return console.log("Using random fallback referral code:",e),e}catch(t){console.error("Error generating referral code:",t);let e="MYN".concat(Math.floor(1e3+9e3*Math.random()));return console.log("Using emergency fallback referral code:",e),e}}async function J(e){try{let t=(0,r.rJ)(o.db,i.users),a=(0,r.P)(t,(0,r._M)(n.referralCode,"==",e),(0,r.AB)(1));return!(await (0,r.GG)(a)).empty}catch(e){return console.error("Error checking referral code:",e),!1}}async function Y(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;for(let t=1;t<=e;t++)try{let e=await H();if(!await J(e))return console.log("Generated unique referral code: ".concat(e," (attempt ").concat(t,")")),e;console.log("Code ".concat(e," already exists, retrying... (attempt ").concat(t,")"))}catch(e){console.error("Error in attempt ".concat(t,":"),e)}let t=Date.now().toString().slice(-4),a="MYN".concat(t);return console.log("Using timestamp-based fallback code: ".concat(a)),a}}}]);