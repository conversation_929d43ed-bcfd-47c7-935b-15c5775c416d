(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8404],{2404:(e,t,a)=>{Promise.resolve().then(a.bind(a,9086))},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>o});var i=a(3915),s=a(3004),n=a(5317),c=a(858);let r=(0,i.Dk)().length?(0,i.Sx)():(0,i.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,s.xI)(r),l=(0,n.aU)(r);(0,c.c7)(r)},9086:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var i=a(5155),s=a(2115),n=a(3004),c=a(5317),r=a(6104);function o(){let[e,t]=(0,s.useState)(""),[a,o]=(0,s.useState)(!1),l=e=>{t(t=>t+e+"\n")},d=async()=>{t(""),o(!0);try{var e;l("\uD83D\uDD0D Testing Simple Registration Process...\n"),l("=== STEP 1: Creating Firebase Auth User ===");let t="test".concat(Date.now(),"@example.com");l("Creating user: ".concat(t));let a=(await (0,n.eJ)(r.j2,t,"test123456")).user;l("✅ Auth user created: ".concat(a.uid)),l("\n=== STEP 2: Waiting for Auth State ==="),await new Promise(e=>setTimeout(e,1e3)),l("Current auth user: ".concat(null==(e=r.j2.currentUser)?void 0:e.uid)),l("Auth state: ".concat(r.j2.currentUser?"authenticated":"not authenticated")),l("\n=== STEP 3: Creating Minimal User Document ===");let i={name:"Test User",email:t.toLowerCase(),mobile:"9876543210",referralCode:"TEST".concat(Date.now().toString().slice(-4)),plan:"Trial",joinedDate:c.Dc.now(),wallet:0,totalVideos:0,todayVideos:0,status:"active"};l("Document path: users/".concat(a.uid)),l("Data fields: ".concat(Object.keys(i).length)),l("\n=== STEP 4: Creating Document ===");let s=(0,c.H9)(r.db,"users",a.uid);try{await (0,c.BN)(s,i),l("✅ Document created successfully"),l("\n=== STEP 5: Verifying Document ===");let e=await (0,c.x7)(s);if(e.exists()){let t=e.data();l("✅ Document verification successful"),l("   Name: ".concat(t.name)),l("   Email: ".concat(t.email)),l("   Plan: ".concat(t.plan)),l("   Referral Code: ".concat(t.referralCode)),l("\n\uD83C\uDF89 SUCCESS: Registration process works!"),l("The issue might be in the registration form validation or error handling.")}else l("❌ Document not found after creation")}catch(e){l("❌ Document creation failed: ".concat(e.message)),l("   Error code: ".concat(e.code)),"permission-denied"===e.code&&(l("\n\uD83D\uDD27 PERMISSION DENIED - Possible causes:"),l("   1. Firestore rules are too restrictive"),l("   2. User authentication not properly propagated"),l("   3. Firebase project configuration issue"))}l("\n=== STEP 6: Cleanup ===");try{await a.delete(),l("✅ Test user deleted")}catch(e){l("⚠️ User deletion failed: ".concat(e.message))}try{await (0,n.CI)(r.j2),l("✅ Signed out")}catch(e){l("⚠️ Sign out failed: ".concat(e.message))}}catch(e){l("❌ Test failed: ".concat(e.message)),l("   Error code: ".concat(e.code)),"auth/email-already-in-use"===e.code&&l("   This is expected if testing multiple times")}finally{o(!1)}},u=async()=>{t(""),o(!0);try{var e;l("\uD83D\uDD0D Testing Firebase Connection...\n"),l("=== TEST 1: Firebase Configuration ==="),l("Auth instance: ".concat(r.j2?"initialized":"not initialized")),l("Firestore instance: ".concat(r.db?"initialized":"not initialized")),l("Current user: ".concat((null==(e=r.j2.currentUser)?void 0:e.uid)||"none")),l("\n=== TEST 2: Firestore Read Access ===");try{let e=(0,c.H9)(r.db,"test_collection","test_doc"),t=await (0,c.x7)(e);l("✅ Firestore read access works"),l("   Document exists: ".concat(t.exists()))}catch(e){l("❌ Firestore read failed: ".concat(e.message)),l("   Error code: ".concat(e.code))}l("\n=== TEST 3: Firestore Write Access (No Auth) ===");try{let e=(0,c.H9)(r.db,"test_collection","test_".concat(Date.now()));await (0,c.BN)(e,{test:!0,timestamp:c.Dc.now()}),l("✅ Firestore write access works (no auth required)")}catch(e){l("❌ Firestore write failed: ".concat(e.message)),l("   Error code: ".concat(e.code))}}catch(e){l("❌ Connection test failed: ".concat(e.message))}finally{o(!1)}};return(0,i.jsx)("div",{className:"min-h-screen p-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Simple Registration Test"}),(0,i.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,i.jsxs)("div",{className:"flex gap-4 mb-4",children:[(0,i.jsx)("button",{onClick:u,disabled:a,className:"btn-primary",children:a?"Testing...":"Test Firebase Connection"}),(0,i.jsx)("button",{onClick:d,disabled:a,className:"btn-primary",children:a?"Testing...":"Test Simple Registration"})]}),(0,i.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,i.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||"Click a test button to start..."})})]}),(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,8441,1684,7358],()=>t(2404)),_N_E=e.O()}]);