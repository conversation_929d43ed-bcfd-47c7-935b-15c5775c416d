"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_leaveService_ts";
exports.ids = ["_ssr_src_lib_leaveService_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/leaveService.ts":
/*!*********************************!*\
  !*** ./src/lib/leaveService.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyUserLeave: () => (/* binding */ applyUserLeave),\n/* harmony export */   calculateActiveDays: () => (/* binding */ calculateActiveDays),\n/* harmony export */   cancelUserLeave: () => (/* binding */ cancelUserLeave),\n/* harmony export */   createAdminLeave: () => (/* binding */ createAdminLeave),\n/* harmony export */   debugAdminLeaveStatus: () => (/* binding */ debugAdminLeaveStatus),\n/* harmony export */   deleteAdminLeave: () => (/* binding */ deleteAdminLeave),\n/* harmony export */   getAdminLeaves: () => (/* binding */ getAdminLeaves),\n/* harmony export */   getAllUserLeaves: () => (/* binding */ getAllUserLeaves),\n/* harmony export */   getUserLeaves: () => (/* binding */ getUserLeaves),\n/* harmony export */   getUserMonthlyLeaveCount: () => (/* binding */ getUserMonthlyLeaveCount),\n/* harmony export */   isAdminLeaveDay: () => (/* binding */ isAdminLeaveDay),\n/* harmony export */   isUserOnLeave: () => (/* binding */ isUserOnLeave),\n/* harmony export */   isWorkBlocked: () => (/* binding */ isWorkBlocked),\n/* harmony export */   updateUserLeaveStatus: () => (/* binding */ updateUserLeaveStatus)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\nconst COLLECTIONS = {\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Admin Leave Functions\nasync function createAdminLeave(leaveData) {\n    try {\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), {\n            ...leaveData,\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(leaveData.date),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n        });\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating admin leave:', error);\n        throw error;\n    }\n}\nasync function getAdminLeaves() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('date', 'asc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const leaves = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                createdAt: doc.data().createdAt.toDate()\n            }));\n        console.log('📅 All admin leaves:', leaves);\n        return leaves;\n    } catch (error) {\n        console.error('Error getting admin leaves:', error);\n        throw error;\n    }\n}\n// Debug function to check current admin leave status\nasync function debugAdminLeaveStatus() {\n    try {\n        const today = new Date();\n        console.log('🔍 Debug: Checking admin leave status for today:', today.toDateString());\n        const isLeave = await isAdminLeaveDay(today);\n        console.log('📊 Debug: Admin leave result:', isLeave);\n        const allLeaves = await getAdminLeaves();\n        console.log('📅 Debug: All admin leaves in database:', allLeaves);\n        const todayLeaves = allLeaves.filter((leave)=>leave.date.toDateString() === today.toDateString());\n        console.log('📅 Debug: Today\\'s admin leaves:', todayLeaves);\n    } catch (error) {\n        console.error('❌ Debug: Error checking admin leave status:', error);\n    }\n}\nasync function deleteAdminLeave(leaveId) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves, leaveId));\n    } catch (error) {\n        console.error('Error deleting admin leave:', error);\n        throw error;\n    }\n}\nasync function isAdminLeaveDay(date) {\n    try {\n        const startOfDay = new Date(date);\n        startOfDay.setHours(0, 0, 0, 0);\n        const endOfDay = new Date(date);\n        endOfDay.setHours(23, 59, 59, 999);\n        console.log('🔍 Checking admin leave for date range:', startOfDay.toISOString(), 'to', endOfDay.toISOString());\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfDay)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfDay)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const hasAdminLeave = !querySnapshot.empty;\n        if (hasAdminLeave) {\n            console.log('📅 Found admin leave(s) for today:', querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data(),\n                    date: doc.data().date.toDate()\n                })));\n        } else {\n            console.log('📅 No admin leaves found for today');\n        }\n        return hasAdminLeave;\n    } catch (error) {\n        console.error('❌ Error checking admin leave day:', error);\n        // Return false (no leave) on error to avoid blocking work unnecessarily\n        return false;\n    }\n}\n// User Leave Functions\nasync function applyUserLeave(leaveData) {\n    try {\n        // Check if user has available leave quota for automatic approval\n        const currentDate = new Date();\n        const currentYear = currentDate.getFullYear();\n        const currentMonth = currentDate.getMonth() + 1;\n        const usedLeaves = await getUserMonthlyLeaveCount(leaveData.userId, currentYear, currentMonth);\n        const maxLeaves = 4 // Monthly leave quota\n        ;\n        // Determine status and approval details\n        let status = 'pending';\n        let reviewedBy;\n        let reviewedAt = undefined;\n        let reviewNotes;\n        // Auto-approve if user has available quota\n        if (usedLeaves < maxLeaves) {\n            status = 'approved';\n            reviewedBy = 'system';\n            reviewedAt = firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now();\n            reviewNotes = `Auto-approved: ${usedLeaves + 1}/${maxLeaves} monthly leaves used`;\n        }\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), {\n            ...leaveData,\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(leaveData.date),\n            status,\n            appliedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now(),\n            ...reviewedBy && {\n                reviewedBy\n            },\n            ...reviewedAt && {\n                reviewedAt\n            },\n            ...reviewNotes && {\n                reviewNotes\n            }\n        });\n        return {\n            id: docRef.id,\n            autoApproved: status === 'approved',\n            usedLeaves: usedLeaves + (status === 'approved' ? 1 : 0),\n            maxLeaves\n        };\n    } catch (error) {\n        console.error('Error applying user leave:', error);\n        throw error;\n    }\n}\nasync function getUserLeaves(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('date', 'desc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                appliedAt: doc.data().appliedAt.toDate(),\n                reviewedAt: doc.data().reviewedAt?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting user leaves:', error);\n        throw error;\n    }\n}\n// Get all user leaves for admin review\nasync function getAllUserLeaves() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('appliedAt', 'desc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const leaves = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                appliedAt: doc.data().appliedAt.toDate(),\n                reviewedAt: doc.data().reviewedAt?.toDate()\n            }));\n        // Get user details for each leave\n        const { getUserData } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(ssr)/./src/lib/dataService.ts\"));\n        for (const leave of leaves){\n            try {\n                const userData = await getUserData(leave.userId);\n                if (userData) {\n                    leave.userName = userData.name;\n                    leave.userEmail = userData.email;\n                }\n            } catch (error) {\n                console.error(`Error getting user data for ${leave.userId}:`, error);\n                leave.userName = 'Unknown User';\n                leave.userEmail = '<EMAIL>';\n            }\n        }\n        return leaves;\n    } catch (error) {\n        console.error('Error getting all user leaves:', error);\n        throw error;\n    }\n}\nasync function updateUserLeaveStatus(leaveId, status, reviewedBy, reviewNotes) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves, leaveId), {\n            status,\n            reviewedBy,\n            reviewedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now(),\n            reviewNotes: reviewNotes || ''\n        });\n    } catch (error) {\n        console.error('Error updating user leave status:', error);\n        throw error;\n    }\n}\nasync function cancelUserLeave(leaveId) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves, leaveId));\n    } catch (error) {\n        console.error('Error cancelling user leave:', error);\n        throw error;\n    }\n}\nasync function getUserMonthlyLeaveCount(userId, year, month) {\n    try {\n        const startOfMonth = new Date(year, month - 1, 1);\n        const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('status', '==', 'approved'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfMonth)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfMonth)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return querySnapshot.size;\n    } catch (error) {\n        console.error('Error getting user monthly leave count:', error);\n        return 0;\n    }\n}\nasync function isUserOnLeave(userId, date) {\n    try {\n        const startOfDay = new Date(date);\n        startOfDay.setHours(0, 0, 0, 0);\n        const endOfDay = new Date(date);\n        endOfDay.setHours(23, 59, 59, 999);\n        console.log('🔍 Checking user leave for user:', userId, 'on date range:', startOfDay.toISOString(), 'to', endOfDay.toISOString());\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('status', '==', 'approved'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfDay)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfDay)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const hasUserLeave = !querySnapshot.empty;\n        if (hasUserLeave) {\n            console.log('👤 Found user leave(s) for today:', querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data(),\n                    date: doc.data().date.toDate()\n                })));\n        } else {\n            console.log('👤 No user leaves found for today');\n        }\n        return hasUserLeave;\n    } catch (error) {\n        console.error('❌ Error checking user leave day:', error);\n        // Return false (no leave) on error to avoid blocking work unnecessarily\n        return false;\n    }\n}\n// Active Days Calculation\nasync function calculateActiveDays(userId, planActivatedDate) {\n    const today = new Date();\n    const daysSincePlanActivated = Math.floor((today.getTime() - planActivatedDate.getTime()) / (1000 * 60 * 60 * 24));\n    try {\n        // Get admin leaves since plan activation\n        const adminLeavesQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(planActivatedDate)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(today)));\n        const adminLeavesSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(adminLeavesQuery);\n        const adminLeaveDays = adminLeavesSnapshot.size;\n        // Get approved user leaves since plan activation\n        const userLeavesQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('status', '==', 'approved'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(planActivatedDate)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(today)));\n        const userLeavesSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(userLeavesQuery);\n        const userLeaveDays = userLeavesSnapshot.size;\n        // Calculate active days: Days since plan activated - admin leaves - user leaves\n        const activeDays = Math.max(0, daysSincePlanActivated - adminLeaveDays - userLeaveDays);\n        return activeDays;\n    } catch (error) {\n        console.error('Error calculating active days:', error);\n        return Math.max(0, daysSincePlanActivated) // Return days since activation on error\n        ;\n    }\n}\n// Check if work/withdrawals should be blocked\nasync function isWorkBlocked(userId) {\n    try {\n        const today = new Date();\n        console.log('🔍 Checking work block status for user:', userId, 'on date:', today.toDateString());\n        // Check admin leave with detailed logging\n        try {\n            const isAdminLeave = await isAdminLeaveDay(today);\n            console.log('📅 Admin leave check result:', isAdminLeave);\n            if (isAdminLeave) {\n                console.log('🚫 Work blocked due to admin leave');\n                return {\n                    blocked: true,\n                    reason: 'System maintenance/holiday'\n                };\n            }\n        } catch (adminLeaveError) {\n            console.error('❌ Error checking admin leave (allowing work to continue):', adminLeaveError);\n        // Don't block work if admin leave check fails\n        }\n        // Check user leave with detailed logging\n        try {\n            const isUserLeave = await isUserOnLeave(userId, today);\n            console.log('👤 User leave check result:', isUserLeave);\n            if (isUserLeave) {\n                console.log('🚫 Work blocked due to user leave');\n                return {\n                    blocked: true,\n                    reason: 'You are on approved leave today'\n                };\n            }\n        } catch (userLeaveError) {\n            console.error('❌ Error checking user leave (allowing work to continue):', userLeaveError);\n        // Don't block work if user leave check fails\n        }\n        console.log('✅ Work is not blocked');\n        return {\n            blocked: false\n        };\n    } catch (error) {\n        console.error('❌ Error checking work block status (allowing work to continue):', error);\n        return {\n            blocked: false\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/leaveService.ts\n");

/***/ })

};
;