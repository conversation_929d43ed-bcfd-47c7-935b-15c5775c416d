{"/admin/page": "app/admin/page.js", "/_not-found/page": "app/_not-found/page.js", "/admin/login/page": "app/admin/login/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/leaves/page": "app/admin/leaves/page.js", "/admin/setup/page": "app/admin/setup/page.js", "/admin/notifications/page": "app/admin/notifications/page.js", "/admin/test-blocking/page": "app/admin/test-blocking/page.js", "/admin/transactions/page": "app/admin/transactions/page.js", "/dashboard/page": "app/dashboard/page.js", "/admin/upload-users/page": "app/admin/upload-users/page.js", "/admin/users/page": "app/admin/users/page.js", "/admin/withdrawals/page": "app/admin/withdrawals/page.js", "/login/page": "app/login/page.js", "/forgot-password/page": "app/forgot-password/page.js", "/admin/fix-permissions/page": "app/admin/fix-permissions/page.js", "/plans/page": "app/plans/page.js", "/debug-registration/page": "app/debug-registration/page.js", "/page": "app/page.js", "/profile/page": "app/profile/page.js", "/refer/page": "app/refer/page.js", "/reset-password/page": "app/reset-password/page.js", "/register/page": "app/register/page.js", "/test-registration/page": "app/test-registration/page.js", "/support/page": "app/support/page.js", "/debug-firestore/page": "app/debug-firestore/page.js", "/test-firestore/page": "app/test-firestore/page.js", "/test-firebase/page": "app/test-firebase/page.js", "/test-simple-registration/page": "app/test-simple-registration/page.js", "/transactions/page": "app/transactions/page.js", "/test-videos/page": "app/test-videos/page.js", "/wallet/page": "app/wallet/page.js", "/work/page": "app/work/page.js"}