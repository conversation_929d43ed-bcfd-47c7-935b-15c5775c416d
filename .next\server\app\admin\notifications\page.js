(()=>{var e={};e.id=993,e.ids=[391,993],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},6453:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(60687),a=s(43210),i=s(85814),n=s.n(i),o=s(87979),l=s(3582),c=s(91391),d=s(83475),u=s(77567);function m(){let{user:e,loading:t,isAdmin:s}=(0,o.wC)(),[i,m]=(0,a.useState)([]),[x,h]=(0,a.useState)([]),[f,p]=(0,a.useState)(!0),[g,y]=(0,a.useState)(!1),[b,N]=(0,a.useState)(!1),[w,v]=(0,a.useState)([]),[j,S]=(0,a.useState)(!1),[C,D]=(0,a.useState)({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]}),E=async()=>{try{p(!0);let[e,t]=await Promise.all([(0,l._f)(50),(0,c.lo)()]);m(e),h(t.users)}catch(e){console.error("Error loading data:",e),u.A.fire({icon:"error",title:"Error",text:"Failed to load data. Please try again."})}finally{p(!1)}},A=async()=>{try{N(!0),await (0,l.z8)({title:"Test Notification",message:"This is a test notification to verify the system is working correctly.",type:"info",targetUsers:"all",userIds:[],createdBy:e?.email||"admin"}),u.A.fire({icon:"success",title:"Test Notification Sent!",text:"Test notification sent to all users. Check user dashboards to verify delivery.",timer:3e3,showConfirmButton:!1}),E()}catch(e){console.error("Error sending test notification:",e),u.A.fire({icon:"error",title:"Test Failed",text:"Failed to send test notification. Please try again."})}finally{N(!1)}},k=async()=>{try{if(!C.title.trim()||!C.message.trim())return void u.A.fire({icon:"error",title:"Validation Error",text:"Please fill in both title and message."});if("specific"===C.targetUsers&&0===C.selectedUserIds.length)return void u.A.fire({icon:"error",title:"Validation Error",text:"Please select at least one user for specific targeting."});N(!0),console.log("Sending notification:",{title:C.title.trim(),message:C.message.trim(),type:C.type,targetUsers:C.targetUsers,userIds:"specific"===C.targetUsers?C.selectedUserIds:[],createdBy:e?.email||"admin"}),await (0,l.z8)({title:C.title.trim(),message:C.message.trim(),type:C.type,targetUsers:C.targetUsers,userIds:"specific"===C.targetUsers?C.selectedUserIds:[],createdBy:e?.email||"admin"}),u.A.fire({icon:"success",title:"Notification Sent!",text:`Notification sent to ${"all"===C.targetUsers?"all users":`${C.selectedUserIds.length} selected users`}.`,timer:3e3,showConfirmButton:!1}),D({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]}),y(!1),E()}catch(e){console.error("Error sending notification:",e),u.A.fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{N(!1)}},U=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},M=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return`${e} minute${e>1?"s":""} ago`}if(t<86400){let e=Math.floor(t/3600);return`${e} hour${e>1?"s":""} ago`}{let e=Math.floor(t/86400);return`${e} day${e>1?"s":""} ago`}},I=async(e,t)=>{if((await u.A.fire({icon:"warning",title:"Delete Notification",text:`Are you sure you want to delete "${t}"? This action cannot be undone.`,showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{S(!0),await (0,l.fP)(e),m(t=>t.filter(t=>t.id!==e)),u.A.fire({icon:"success",title:"Notification Deleted",text:"Notification has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notification:",e),u.A.fire({icon:"error",title:"Delete Failed",text:"Failed to delete notification. Please try again."})}finally{S(!1)}},L=async()=>{if(0===w.length)return void u.A.fire({icon:"warning",title:"No Selection",text:"Please select notifications to delete."});if((await u.A.fire({icon:"warning",title:"Delete Selected Notifications",text:`Are you sure you want to delete ${w.length} selected notifications? This action cannot be undone.`,showCancelButton:!0,confirmButtonText:"Yes, Delete All",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{S(!0),await Promise.all(w.map(e=>(0,l.fP)(e))),m(e=>e.filter(e=>!w.includes(e.id))),v([]),u.A.fire({icon:"success",title:"Notifications Deleted",text:`${w.length} notifications have been deleted successfully`,timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notifications:",e),u.A.fire({icon:"error",title:"Delete Failed",text:"Failed to delete some notifications. Please try again."})}finally{S(!1)}},_=e=>{v(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return t||f?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(n(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-gray-700",children:["Total: ",i.length,w.length>0&&(0,r.jsxs)("span",{className:"ml-2 text-blue-600",children:["(",w.length," selected)"]})]}),w.length>0&&(0,r.jsxs)("button",{onClick:L,disabled:j,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,r.jsx)("i",{className:"fas fa-trash mr-2"}),"Delete Selected (",w.length,")"]}),(0,r.jsxs)("button",{onClick:A,disabled:b||j,className:"bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,r.jsx)("i",{className:"fas fa-vial mr-2"}),"Test Notification"]}),(0,r.jsxs)("button",{onClick:()=>{if(0===i.length)return void u.A.fire({icon:"warning",title:"No Data",text:"No notifications to export."});let e=(0,d.Pe)(i);(0,d.Bf)(e,"notifications"),u.A.fire({icon:"success",title:"Export Complete",text:`Exported ${i.length} notifications to CSV file.`,timer:2e3,showConfirmButton:!1})},disabled:j,className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,r.jsxs)("button",{onClick:()=>y(!0),disabled:j,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Send Notification"]}),(0,r.jsxs)("button",{onClick:E,disabled:j,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===i.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-6xl mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications sent yet"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Start by sending your first notification to users"}),(0,r.jsxs)("button",{onClick:()=>y(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Send First Notification"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-3 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:w.length===i.length&&i.length>0,onChange:()=>{w.length===i.length?v([]):v(i.map(e=>e.id).filter(Boolean))},className:"mr-3"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:["Select All (",i.length," notifications)"]})]}),w.length>0&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[w.length," selected"]}),(0,r.jsxs)("button",{onClick:L,disabled:j,className:"text-red-600 hover:text-red-800 disabled:opacity-50",children:[(0,r.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete Selected"]})]})]})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:i.map(e=>(0,r.jsx)("div",{className:`p-6 hover:bg-gray-50 ${w.includes(e.id)?"bg-blue-50":""}`,children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,r.jsx)("input",{type:"checkbox",checked:w.includes(e.id),onChange:()=>_(e.id),className:"mr-3"})}),(0,r.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,r.jsx)("i",{className:U(e.type)})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.title,(0,r.jsx)("span",{className:"ml-2 px-2 py-1 text-xs font-bold bg-red-100 text-red-800 rounded-full",children:"\uD83D\uDEA8 BLOCKING"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"success"===e.type?"bg-green-100 text-green-800":"warning"===e.type?"bg-yellow-100 text-yellow-800":"error"===e.type?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}`,children:e.type.charAt(0).toUpperCase()+e.type.slice(1)}),(0,r.jsx)("button",{onClick:()=>I(e.id,e.title),disabled:j,className:"text-red-600 hover:text-red-800 disabled:opacity-50 p-1",title:"Delete notification",children:(0,r.jsx)("i",{className:"fas fa-trash"})})]})]}),(0,r.jsx)("p",{className:"text-gray-700 mt-2",children:e.message}),(0,r.jsx)("div",{className:"flex items-center justify-between mt-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:[(0,r.jsx)("i",{className:"fas fa-user mr-1"}),"By: ",e.createdBy]}),(0,r.jsxs)("span",{children:[(0,r.jsx)("i",{className:"fas fa-users mr-1"}),"Target: ","all"===e.targetUsers?"All Users":`${e.userIds?.length||0} Selected Users`]}),(0,r.jsxs)("span",{children:[(0,r.jsx)("i",{className:"fas fa-clock mr-1"}),M(e.createdAt)]})]})})]})]})},e.id))})]})})}),g&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Send Notification"}),(0,r.jsx)("button",{onClick:()=>y(!1),className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Title"}),(0,r.jsx)("input",{type:"text",value:C.title,onChange:e=>D(t=>({...t,title:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification title..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,r.jsx)("textarea",{value:C.message,onChange:e=>D(t=>({...t,message:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification message..."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,r.jsxs)("select",{value:C.type,onChange:e=>D(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"info",children:"Info"}),(0,r.jsx)("option",{value:"success",children:"Success"}),(0,r.jsx)("option",{value:"warning",children:"Warning"}),(0,r.jsx)("option",{value:"error",children:"Error"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Target"}),(0,r.jsxs)("select",{value:C.targetUsers,onChange:e=>D(t=>({...t,targetUsers:e.target.value,selectedUserIds:"all"===e.target.value?[]:t.selectedUserIds})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Users"}),(0,r.jsx)("option",{value:"specific",children:"Specific Users"})]})]})]}),(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle text-red-500 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"\uD83D\uDEA8 All Notifications are Blocking (Mandatory)"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Users must acknowledge this notification before they can continue with any activities (watching videos, accessing dashboard features, etc.)"})]})]})}),"specific"===C.targetUsers&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Users"}),(0,r.jsx)("div",{className:"max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-2",children:x.map(e=>(0,r.jsxs)("label",{className:"flex items-center p-2 hover:bg-gray-50 rounded",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.selectedUserIds.includes(e.id),onChange:t=>{t.target.checked?D(t=>({...t,selectedUserIds:[...t.selectedUserIds,e.id]})):D(t=>({...t,selectedUserIds:t.selectedUserIds.filter(t=>t!==e.id)}))},className:"mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name||"Unknown User"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.email||"No email"," • ",e.plan||"No plan"]})]})]},e.id))}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[C.selectedUserIds.length," user(s) selected"]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,r.jsx)("button",{onClick:()=>y(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,r.jsx)("button",{onClick:k,disabled:b,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:b?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Notification"]})})]})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,s)=>{"use strict";s.d(t,{db:()=>c,j2:()=>l});var r=s(67989),a=s(63385),i=s(75535),n=s(70146);let o=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,a.xI)(o),c=(0,i.aU)(o);(0,n.c7)(o)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},35592:(e,t,s)=>{Promise.resolve().then(s.bind(s,60795))},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,t,s)=>{"use strict";s.d(t,{M4:()=>o,_f:()=>n});var r=s(33784),a=s(77567);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e,t="/login"){try{if((await a.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await r.j2.signOut(),a.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),a.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e,t="/login"){try{e&&i(e),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60795:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\notifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\notifications\\page.tsx","default")},62251:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["admin",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,60795)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\notifications\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\notifications\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/notifications/page",pathname:"/admin/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,s)=>{"use strict";function r(e,t,s){if(!e||0===e.length)return void alert("No data to export");let r=s||Object.keys(e[0]),a=new Blob([[r.join(","),...e.map(e=>r.map(t=>{let s=e[t];if(null==s)return"";if("string"==typeof s){let e=s.replace(/"/g,'""');return e.includes(",")?`"${e}"`:e}return s instanceof Date?s.toLocaleDateString():String(s)}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(a);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function a(e){return e.map(e=>({Name:e.name,Email:e.email,Mobile:e.mobile,"Referral Code":e.referralCode,"Referred By":e.referredBy||"Direct",Plan:e.plan,"Active Days":e.activeDays,"Total Videos":e.totalVideos,"Today Videos":e.todayVideos,"Video Duration (seconds)":e.videoDuration||300,"Wallet Balance":e.wallet||0,Status:e.status,"Joined Date":e.joinedDate?.toLocaleDateString()||""}))}function i(e){return e.map(e=>({"User ID":e.userId,"User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":e.userMobile||"","User Number":e.userNumber||"",Type:e.type,Amount:e.amount,Description:e.description,Status:e.status,Date:e.date?.toLocaleDateString()||""}))}function n(e){return e.map(e=>({"User Name":e.userName,"User Email":e.userEmail,"Mobile Number":e.userMobile||"","User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount,"Account Holder":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":e.bankDetails?.accountNumber||"","IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status,"Request Date":e.requestDate?.toLocaleDateString()||"","Admin Notes":e.adminNotes||""}))}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt?.toLocaleDateString()||"","Sent Date":e.sentAt?.toLocaleDateString()||""}))}s.d(t,{Bf:()=>r,Fz:()=>a,Pe:()=>o,dB:()=>n,sL:()=>i})},87979:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>n,hD:()=>i,wC:()=>o});var r=s(43210);s(63385),s(33784);var a=s(51278);function i(){let[e,t]=(0,r.useState)(null),[s,i]=(0,r.useState)(!0),n=async()=>{try{await (0,a.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:n}}function n(){let{user:e,loading:t}=i();return{user:e,loading:t}}function o(){let{user:e,loading:t}=i(),[s,a]=(0,r.useState)(!1),[n,o]=(0,r.useState)(!0);return{user:e,loading:t||n,isAdmin:s}}},91391:(e,t,s)=>{"use strict";s.d(t,{Ki:()=>c,Pn:()=>o,TK:()=>u,getWithdrawals:()=>d,hG:()=>m,lo:()=>l,updateWithdrawalStatus:()=>x});var r=s(75535),a=s(33784),i=s(3582);let n=new Map;async function o(){let e="dashboard-stats",t=function(e){let t=n.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let s=r.Dc.fromDate(t),o=await (0,r.GG)((0,r.rJ)(a.db,i.COLLECTIONS.users)),l=o.size,c=(0,r.P)((0,r.rJ)(a.db,i.COLLECTIONS.users),(0,r._M)(i.FIELD_NAMES.joinedDate,">=",s)),d=(await (0,r.GG)(c)).size,u=0,m=0,x=0,h=0;o.forEach(e=>{let s=e.data();u+=s[i.FIELD_NAMES.totalVideos]||0,m+=s[i.FIELD_NAMES.wallet]||0;let r=s[i.FIELD_NAMES.lastVideoDate]?.toDate();r&&r.toDateString()===t.toDateString()&&(x+=s[i.FIELD_NAMES.todayVideos]||0)});try{let e=(0,r.P)((0,r.rJ)(a.db,i.COLLECTIONS.transactions),(0,r._M)(i.FIELD_NAMES.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.GG)(e)).forEach(e=>{let s=e.data(),r=s[i.FIELD_NAMES.date]?.toDate();r&&r>=t&&(h+=s[i.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let f=(0,r.P)((0,r.rJ)(a.db,i.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),p=(await (0,r.GG)(f)).size,g=(0,r.P)((0,r.rJ)(a.db,i.COLLECTIONS.withdrawals),(0,r._M)("date",">=",s)),y=(await (0,r.GG)(g)).size,b={totalUsers:l,totalVideos:u,totalEarnings:m,pendingWithdrawals:p,todayUsers:d,todayVideos:x,todayEarnings:h,todayWithdrawals:y};return n.set(e,{data:b,timestamp:Date.now()}),b}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(e=50,t=null){try{let s=(0,r.P)((0,r.rJ)(a.db,i.COLLECTIONS.users),(0,r.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(e));t&&(s=(0,r.P)((0,r.rJ)(a.db,i.COLLECTIONS.users),(0,r.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let n=await (0,r.GG)(s);return{users:n.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{let t=(0,r.P)((0,r.rJ)(a.db,i.COLLECTIONS.users),(0,r._M)(i.FIELD_NAMES.mobile,"==",e));return(await (0,r.GG)(t)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()}))}catch(e){throw console.error("Error searching users by mobile:",e),e}}async function d(e=50,t=null){try{let s=(0,r.P)((0,r.rJ)(a.db,i.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(e));t&&(s=(0,r.P)((0,r.rJ)(a.db,i.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(t),(0,r.AB)(e)));let n=await (0,r.GG)(s);return{withdrawals:n.docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()})),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function u(e,t){try{await (0,r.mZ)((0,r.H9)(a.db,i.COLLECTIONS.users,e),t),n.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function m(e){try{await (0,r.kd)((0,r.H9)(a.db,i.COLLECTIONS.users,e)),n.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function x(e,t,o){try{let l=await (0,r.x7)((0,r.H9)(a.db,i.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=l.data(),m={status:t,updatedAt:r.Dc.now()};if(o&&(m.adminNotes=o),await (0,r.mZ)((0,r.H9)(a.db,i.COLLECTIONS.withdrawals,e),m),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(s.bind(s,3582));await e(c,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${d} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(s.bind(s,3582));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:`Withdrawal rejected - ₹${d} credited back to wallet`})}n.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99144:(e,t,s)=>{Promise.resolve().then(s.bind(s,6453))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[204,756,567,441,582],()=>s(62251));module.exports=r})();